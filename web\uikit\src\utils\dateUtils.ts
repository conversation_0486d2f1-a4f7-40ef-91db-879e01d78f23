import { isToday, parseISO, formatISO, isPast } from "date-fns";
import { enCA, frCA } from "date-fns/locale";

export const MILLISECONDS_IN_MINUTE = 60000;
export const MILLISECONDS_IN_HOUR = 3600000;
export const MILLISECONDS_IN_DAY = 86400000;

export const getTimezoneConvertedDate = (date: Date | string): Date => {
  if (date instanceof Date) {
    return date;
  } else if (date.slice(-1) === "Z") {
    return new Date(date);
  }
  // Append 'Z' to interpret string date as UTC instead of local
  return new Date(date + "Z");
};

// Determine if a date is past due from todays current date
export const isPastDue = (dateString: string): boolean => {
  const date = parseISO(dateString);
  return isPast(date) && !isToday(date);
};

// Takes a date string and returns → Jan 01, 2025 | 01 janv. 2025
export const getDateLabel = (
  dateString: string,
  localeString: string = "en-CA"
): string => {
  if (!dateString) {
    return "";
  }
  const localeStringNormalized = localeString.includes("fr")
    ? "fr-CA"
    : "en-CA";
  const date = parseISO(dateString);

  const options: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "2-digit",
    year: "numeric",
  };
  const formatter = new Intl.DateTimeFormat(localeStringNormalized, options);
  return formatter.format(date);
};

// If the date is today → display as: Today at 12:00 PM | Aujourd’hui à
// If the date is not today → display as: Jan 01, 2025 | 01 janv. 2025
export const getDateTimeLabel = (
  dateString: string,
  localeString: string = "en-CA",
  alwaysShowTime: boolean = false,
  capitalize: boolean = false
): string => {
  if (!dateString) {
    return "";
  }
  const date = parseISO(dateString);
  // Use en-US locale instead of en-CA for AM/PM instead of a.m./p.m.
  const localeStringNormalized = localeString.includes("fr")
    ? "fr-CA"
    : "en-US";
  const options: Intl.DateTimeFormatOptions = {
    hour: "numeric",
    minute: "2-digit",
    hour12: localeStringNormalized == "fr-CA" ? false : true,
  };
  const formatter = new Intl.DateTimeFormat(localeStringNormalized, options);
  const timeLabel = formatter.format(date);

  if (isToday(date)) {
    const todayString = localeString.includes("fr")
      ? "aujourd'hui à"
      : "today at";
    return capitalize
      ? `${todayString.charAt(0).toUpperCase() +
          todayString.slice(1)} ${timeLabel}`
      : `${todayString} ${timeLabel}`;
  }

  const dateLabel = getDateLabel(dateString, localeString);
  if (alwaysShowTime) {
    return `${dateLabel} ${
      localeString.includes("fr") ? "à" : "at"
    } ${timeLabel}`;
  }

  return dateLabel;
};

// If the date is today → display as: Today | Aujourd’hui
// If the date is not today but in the future → display as: Jan 01, 2025 | 01 janv. 2025
// If the date is not today but in the past → {x} days ago | il y a {x} jours
//    For a single day in the past (yesterday) → 1 day ago | il y a 1 jour
// Note: Calculations disregard the time of the date time, so dates are calculated based
//       on the beginning of the day.
export const getDueDateLabel = (
  dateString: string,
  localeString: string = "en-CA"
): string => {
  if (!dateString) {
    return "";
  }
  const date = parseISO(dateString);
  const locale = localeString.includes("fr") ? frCA : enCA;

  if (isToday(date)) {
    return locale === frCA ? "Aujourd'hui" : "Today";
  } else if (isPastDue(dateString)) {
    const today = new Date();
    const startOfToday = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
      0,
      0,
      0
    );
    const startOfDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      0,
      0,
      0
    );
    const daysPast = Math.floor(
      (startOfToday.getTime() - startOfDate.getTime()) / MILLISECONDS_IN_DAY
    );
    const daysString = `${locale === frCA ? "jour" : "day"}${
      daysPast > 1 ? "s" : ""
    }`;
    return locale === frCA
      ? `il y a ${daysPast} ${daysString}`
      : `${daysPast} ${daysString} ago`;
  } else {
    return getDateLabel(dateString, localeString);
  }
};

// Returns a string of a date range if there are 2 dates in the Set
// ex. Jun 10, 2025 – Jun 11, 2025
// If there is only 1 date in the set or 2 of the same dates, and allowSingleDay is true,
// then a single date will be returned.
export const getDateRangeLabel = (
  dates: Set<Date>,
  locale: string = "en-CA"
): string => {
  if (!dates || dates.size === 0) {
    return "";
  }

  const datesArray = Array.from(dates).sort(
    (a, b) => new Date(a).getTime() - new Date(b).getTime()
  );

  if (datesArray.length >= 2) {
    const startDate = new Date(datesArray[0]);
    const endDate = new Date(datesArray[1]);
    const formattedStartDate = getDateLabel(formatISO(startDate), locale);
    const formattedEndDate = getDateLabel(formatISO(endDate), locale);

    if (
      startDate.getFullYear() === endDate.getFullYear() &&
      startDate.getMonth() === endDate.getMonth() &&
      startDate.getDate() === endDate.getDate()
    ) {
      return formattedStartDate;
    }
    return `${formattedStartDate} – ${formattedEndDate}`;
  } else if (datesArray.length == 1) {
    const date = datesArray[0];
    return getDateLabel(formatISO(date), locale);
  }

  return "";
};

export const getDocumentsDateLabel = (
  dateString: string,
  locale: string = "en-CA"
): string => {
  const date = parseISO(dateString);
  const now = new Date();
  const diffInMinutes =
    (now.getTime() - date.getTime()) / MILLISECONDS_IN_MINUTE;

  if (diffInMinutes < 1) {
    return locale.includes("fr") ? "À l'instant" : "Just now";
  }

  return getDateTimeLabel(dateString, locale, false, true);
};

export const getStartEndDateRange = (
  range: "today" | "last7Days" | "last30Days"
): { startOfRange: Date; endOfRange: Date } => {
  const today = new Date();
  let startOfRange = null;
  let endOfRange = null;
  switch (range) {
    case "today": {
      startOfRange = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        0,
        0,
        0
      );
      endOfRange = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        0,
        0,
        0
      );
      return { startOfRange, endOfRange };
    }
    case "last7Days": {
      endOfRange = today;
      startOfRange = new Date();
      startOfRange.setDate(endOfRange.getUTCDate() - 6);
      return { startOfRange, endOfRange };
    }
    case "last30Days": {
      endOfRange = today;
      startOfRange = new Date();
      startOfRange.setDate(endOfRange.getUTCDate() - 29);
      return { startOfRange, endOfRange };
    }
  }
};

export const isDueDateInDateRange = (
  startDate: Date,
  endDate: Date,
  dueDate: Date
) => dueDate >= startDate && dueDate < endDate;
