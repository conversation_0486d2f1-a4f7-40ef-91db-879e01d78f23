import { FileUpload, useFileUpload } from "@ark-ui/react";
import { DocumentUploadBannerProps } from "./spec";
import styles from "./documentUploadBanner.module.css";
import {
  Icon,
  DocumentUploadFile,
  getDateTimeLabel,
  DEFAULT_MAX_FILES,
  validateFileForUpload,
} from "@bcp/uikit";
import { formatISO } from "date-fns";
import { PriorityActionItemBannerType } from "../spec";
import { useTranslation } from "react-i18next";
import { useToastService } from "~/services/toast";

const fileNameExists = (name: string, files: DocumentUploadFile[]): boolean => {
  return files.some(item => item.file.name === name);
};

export const DocumentUploadBanner: React.FC<DocumentUploadBannerProps> = ({
  existingFiles = [],
  id,
  username,
  onFileAccept,
  onFileReject,
  onFileDelete,
  type = PriorityActionItemBannerType.Default,
  dataTestId = "uikit-documentUpload",
  role,
  ariaLabel = "Document Upload",
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
}) => {
  const { t } = useTranslation("overview");
  const { t: tDocuments } = useTranslation("documents");
  const { showToast } = useToastService();

  const onFileChangeHandler = (details: FileUpload.FileChangeDetails) => {
    const isReject = details.rejectedFiles.length > 0;
    const isUpload =
      details.acceptedFiles.length > existingFiles.length && !isReject;
    const isRemove = details.acceptedFiles.length < existingFiles.length;

    if (isReject) {
      if (onFileReject) onFileReject();
      return;
    }

    if (isUpload && !isRemove) {
      const numberOfNewFiles =
        details.acceptedFiles.length - existingFiles.length;

      const newFiles = details.acceptedFiles.slice(
        details.acceptedFiles.length - numberOfNewFiles
      );

      const filesToUpload: DocumentUploadFile[] = [];

      newFiles.forEach(newFile => {
        if (fileNameExists(newFile.name, existingFiles)) {
          showToast({
            type: "error",
            title: tDocuments("failed-to-upload", {
              fileName: newFile.name,
            }),
            message: tDocuments("file-already-exists"),
            persist: false,
            errorMessageWithLineBreaks: true,
            replaceLineBreaksWithBulletPoints: true,
          });
          return;
        }

        const fileBody = {
          time: getDateTimeLabel(formatISO(new Date())),
          file: newFile,
          id: newFile.name,
        };

        filesToUpload.push(fileBody);
      });

      const updatedFiles = [...existingFiles, ...filesToUpload];

      if (onFileAccept) {
        onFileAccept(updatedFiles);
      }
    }
  };

  const handleValidateUpload = (file: File) => {
    const { errors } = validateFileForUpload(file);

    for (const error of errors) {
      showToast({
        title: tDocuments("failed-to-upload", { fileName: file.name }),
        type: "error",
        persist: false,
        message: error,
        errorMessageWithLineBreaks: true,
        replaceLineBreaksWithBulletPoints: true,
      });
    }

    return errors;
  };

  const onFileDeleteHandler = (fileId: string) => {
    //remove file from files array
    const updatedFiles = existingFiles.filter(file => file.id !== fileId);
    showToast({
      type: "info",
      message: "File removed successfully",
      persist: false,
    });

    if (onFileDelete) {
      onFileDelete(updatedFiles);
    }
  };

  const fileUpload = useFileUpload({
    id: id || "file-upload",
    validate: handleValidateUpload,
    onFileChange: onFileChangeHandler,
    maxFiles: DEFAULT_MAX_FILES,
  });

  return (
    <div className={styles.documentUploadBannerWrapper}>
      <FileUpload.RootProvider
        value={fileUpload}
        data-testid={dataTestId}
        role={role}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        aria-labelledby={ariaLabelledBy}
        tabIndex={tabIndex}
        className={styles[type.toLowerCase()]}
      >
        <div className={styles.dropzoneContainer}>
          <FileUpload.Dropzone className={styles[type.toLowerCase()]}>
            <FileUpload.Label>
              <Icon
                iconName="document-add"
                altText="DocumentAddIcon"
                size={20}
              />
            </FileUpload.Label>
            <div className={styles.labels}>
              <FileUpload.Label>{t("upload-files")}</FileUpload.Label>
            </div>
            <FileUpload.HiddenInput />
          </FileUpload.Dropzone>
        </div>
        {existingFiles.length > 0 && (
          <FileUpload.ItemGroup>
            {existingFiles.map((item, index) => (
              <FileUpload.Item
                key={`${item.file.name}-${index}`}
                file={item.file}
              >
                <div className={styles.documentIcon}>
                  <Icon iconName="documents-icon" altText="DocumentPreview" />
                </div>
                <FileUpload.ItemName />
                <div className={styles.nameContainer}>
                  <span>{username} </span>•<span> {item.time}</span>
                </div>
                <FileUpload.ItemDeleteTrigger
                  onClick={() => onFileDeleteHandler(item.file.name)}
                >
                  <Icon iconName="ellipses-icon" altText="ellipses-icon"></Icon>
                </FileUpload.ItemDeleteTrigger>
              </FileUpload.Item>
            ))}
          </FileUpload.ItemGroup>
        )}
      </FileUpload.RootProvider>
    </div>
  );
};
