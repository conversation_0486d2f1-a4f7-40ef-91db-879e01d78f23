import React from "react";
import { IconName } from "~/icon";
import { defaultProps } from "~/default.spec";
interface AvatarProps extends defaultProps {
    imageUrl?: string;
    initials: string;
    avatarSize?: "chip" | "x-small" | "small" | "medium" | "small-medium" | "medium-small" | "large" | "x-large";
    fontSize?: "x-small" | "small" | "medium" | "large";
    fontBold?: boolean;
    type?: "monogram" | "icon" | "photo";
    bgColor?: string;
    fgColor?: string;
    iconName?: IconName;
    useBorder?: boolean;
    clickable?: boolean;
    onClick?: () => void;
}
export declare const CustomAvatar: React.FC<AvatarProps>;
export {};
