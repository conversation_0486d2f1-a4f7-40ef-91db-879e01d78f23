import { ReactNode } from "react";
import { defaultProps } from "~/default.spec";
import { iconMap } from "~/icon/iconMap";
declare type IconName = keyof typeof iconMap;
export declare type InlineMessageType = "info" | "warning" | "success" | "error";
export interface InlineMessageProps extends defaultProps {
    title?: string;
    message?: string;
    iconName?: IconName;
    iconSize?: number;
    width?: number;
    height?: number;
    type: InlineMessageType;
    children?: ReactNode;
    withIcon?: boolean;
}
export {};
