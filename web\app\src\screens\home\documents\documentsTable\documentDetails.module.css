.documentDetails {
  box-shadow: 0 4px 8px #00000026;
  width: 300px;
  background: #fff;
  padding: 16px;
  border-radius: 2px;
}

.documentDetailsContainer {
  position: relative;
}

.documentDetailsBlock {
  border-bottom: 1px solid var(--color-border-pale-grey);
  margin-bottom: 12px;
  padding-bottom: 12px;
}

.documentDetailsBlock:last-child {
  border-bottom: none;
  margin-bottom: 0px;
  padding-bottom: 0px;
}

.documentDetailsHeader {
  margin: 0px;
  font-size: var(--fontsize-body);
}

.documentDetailsLabel {
  color: var(--color-bg-inactive-charcoal);
  font-family: var(--fontsize-body-small);
  font-weight: 600;
  line-height: var(--lineheight-body);
  font-size: var(--fontsize-body-xsmall);
  text-transform: uppercase;
  margin: 0px;
}

.documentDetailsText {
  margin: 0px;
  font-size: var(--fontsize-body-xsmall);
}

.chipStyling {
  padding-top: 8px;
  width: fit-content;
}

.manageAccessContainer {
  margin: 0px;
}

.manageAccess {
  font-weight: 600;
  color: var(--color-secondary-cobalt);
  margin: 8px 0 12px 0;
  width: fit-content;
  font-size: var(--fontsize-body-xsmall);
  border-radius: 0;
  padding: 0;
  border: 0;
}

.manageAccess:hover {
  cursor: pointer;
}

.sharedWithContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.userAvatarsContainer {
  margin-top: 4px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 4px;
}

/* Users Modal Styles */
.usersModalContent {
  display: flex;
  flex-direction: column;
  max-height: 100%;
  overflow-y: auto;
}

.userRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px 8px 0;
  border-bottom: 1px solid var( --color-primary-pale-charcoal);
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.userName {
  font-size: var(--fontsize-body-small);
  font-weight: 500;
  color: var(--color-text-primary);
}

.userOrganization {
  font-size: var(--fontsize-body-small);
  color: var(--color-text-secondary);
}
