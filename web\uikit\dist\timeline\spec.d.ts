import { defaultProps } from "~/default.spec";
export declare type StageStatus = "complete" | "delayed" | "in progress" | "upcoming";
export interface Stage {
    id: string;
    title: string;
    subtitle?: string;
    translationKey: string;
    projectCompletion?: number;
    status: string;
    substages?: Stage[];
    isSubstage?: boolean;
    isLastStage?: boolean;
    active: boolean;
    startDate?: Date;
    endDate?: Date;
    createdAt: Date;
    updatedAt: Date;
    sortOrder?: number;
    _isNewTemplate?: boolean;
}
export interface Timeline extends defaultProps {
    title: string;
    stages: Stage[];
    lastUpdated?: string;
    isClient?: boolean;
    isEditMode: () => void;
}
