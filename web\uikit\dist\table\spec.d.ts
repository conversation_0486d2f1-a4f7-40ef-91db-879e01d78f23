import { ReactNode } from "react";
import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
export interface ColumnConfig<T> {
    key: keyof T;
    headerClass: string;
    header: string | JSX.Element;
    showFilterIcon: boolean;
    tableCellClass: string;
    render?: (item: T, index: number) => React.ReactNode;
}
export interface TableProps<T> extends defaultProps {
    columns: ColumnConfig<T>[];
    data: T[];
    placeHolderItems?: string[];
    emptyMessage?: string;
    emptySubMessage?: string;
    isLoading?: boolean;
    trailingComponent?: ReactNode;
    emptyMessageIcon?: IconName;
    className?: string;
    onClickRow?: (value: T) => void;
    caption?: string;
    shouldRenderDocumentsSkeleton?: boolean;
    dataKeyProperty?: keyof T;
    highlightImportantRow?: (item: T) => boolean;
}
