import React from "react";
import { defaultProps } from "~/default.spec";
export declare enum SpinnerSize {
    SMALL = "small",
    MEDIUM = "medium",
    LARGE = "large",
    EXTRA_LARGE = "extraLarge"
}
export declare enum SpinnerType {
    PAGE = "page",
    DROPDOWN = "dropdown",
    BUTTON = "button",
    TABLE = "table"
}
interface SpinnerProps extends defaultProps {
    children?: React.ReactNode;
    background?: string;
    minHeight?: string | number;
    size?: SpinnerSize | string | number;
    isLoading?: boolean;
    message?: string;
    borderColor?: string;
    borderWidth?: number;
    type?: SpinnerType;
}
export declare const Spinner: React.FC<SpinnerProps>;
export {};
