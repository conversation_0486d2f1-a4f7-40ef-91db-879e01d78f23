import React from "react";
import { Direction } from "~/tooltip/spec";
import { defaultProps } from "~/default.spec";
interface AssigneeTooltipProps extends defaultProps {
    children: React.ReactNode;
    message: string;
    inputId: string;
    direction?: Direction;
    closeDelay?: number;
    openDelay?: number;
    withArrow?: boolean;
    smallSize?: boolean;
    wide?: boolean;
}
export declare const AssigneeTooltip: React.FC<AssigneeTooltipProps>;
export {};
