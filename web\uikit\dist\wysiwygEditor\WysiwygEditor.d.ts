import React from "react";
import "./editorTheme.css";
import { defaultProps } from "~/default.spec";
interface FooterButtonConfig {
    primaryButton?: React.ReactNode;
    secondaryButton?: React.ReactNode;
}
interface WYSIWYGEditorProps extends defaultProps {
    value?: string;
    onChange?: (content: string) => void;
    onContentSave?: (content: string) => void;
    showToolbar?: boolean;
    footerBtnConfig?: FooterButtonConfig;
    placeholder?: string;
    textSize?: "small" | "large";
    onFocus?: () => void;
    onBlur?: () => void;
    maxCharacterCount?: number;
}
export declare const WYSIWYGEditor: React.FC<WYSIWYGEditorProps>;
export {};
