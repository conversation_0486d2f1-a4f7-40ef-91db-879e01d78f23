import React from "react";
import { ToggleProps } from "~/toggle";
import { TooltipProps } from "~/tooltip";
import { defaultProps } from "~/default.spec";
export interface ToggleListItemProps extends defaultProps {
    inputId: string;
    title: string;
    subtitle: string;
    withBorder?: boolean;
    toggleConfig?: ToggleProps;
    tooltipConfig?: TooltipProps;
}
export declare const ToggleListItem: React.FC<ToggleListItemProps>;
