import { DatePickerDate, DocumentUploadFile, SuggestedUser } from "@bcp/uikit";
import { Dispatch, SetStateAction } from "react";
import {
  ActionItem,
  SignerOptionEnum,
  ActionItemDocument,
  ActionItemUser,
} from "~/services/action-items/spec";
import { FolderNode } from "./assignFolderModal/spec";
import { INode } from "~/services/document";

export interface ActionItemFormProps {
  isOpen: boolean;
  type: ActionItemFormType;
  selectedAssignees: SelectedAssignee[];
  isPriority: boolean;
  targetFolder: FolderNode | null;
  fileDestinationFetchError: boolean;
  duplicateDataTargetFolder?: INode;
  signerOption?: SignerOptionEnum;
  instructions: string;
  files: DocumentUploadFile[];
  title: string;
  setTitle: React.Dispatch<React.SetStateAction<string>>;
  formErrors?: Record<string, string>;
  setSelectedAssignees: (users: SelectedAssignee[]) => void;
  setIsPriority: (setIsPriority: boolean) => void;
  setSignerOption?: (signerOption: SignerOptionEnum) => void;
  setInstructions: (instructions: string) => void;
  setFiles: (files: DocumentUploadFile[]) => void;
  setDueDate: (dueDate: DatePickerDate) => void;
  date?: string | null;
  docUploadKey: number;
  isLoading?: boolean;
  showFormRequiredFields?: boolean;
  openAssignFolderModal: () => void;
  envelopeId: string | null;
  onChangeEnvelopeId: (envelopeId: string | null) => void;
  enableSigningOrder: boolean;
  onChangeEnableSigningOrder: (enableSigningOrder: boolean) => void;
}

export interface SelectedAssignee {
  user: ActionItemUser;
}

export const acceptedFileTypes = [
  "application/pdf",
  "image/jpeg",
  "image/png",
  "image/gif",
  "application/zip",
  "application/x-zip-compressed",
  "application/vnd.rar",
  "text/plain",
  "application/msword",
  "application/vnd.ms-excel",
  "application/vnd.ms-powerpoint",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  "application/vnd.ms-excel.sheet.macroEnabled.12",
  "application/vnd.ms-word.document.macroEnabled.12",
  "application/vnd.ms-powerpoint.presentation.macroEnabled.12",
  "application/vnd.visio",
  "application/vnd.ms-visio.drawing.main+xml",
  "application/vnd.visio2013",
  "text/html",
  "application/xml",
  "text/xml",
  "application/xslt+xml",
  "video/mp4",
  "application/x-7z-compressed",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
  "application/vnd.ms-xpsdocument",
  "application/vnd.ms-outlook",
  "message/rfc822",
  "text/csv",
  "application/x-qbw",
  "application/x-preview",
  "application/x-game",
  "application/x-zipx",
];

export const MAX_FILE_SIZE = 2000000000;

export const MAX_INSTRUCTIONS_CHARACTER_COUNT = 1000;

export enum ActionItemFormType {
  DocumentRequest = "RequestItem",
  SignatureRequest = "Signature",
}

export enum SignatureRequestDocumentStatus {
  None = "None",
  InProgress = "InProgress",
  Declined = "Declined",
  Signed = "Signed",
  Cancelled = "Cancelled",
}

export interface ActionItemDrawerProps {
  actionItemDetails: ActionItem;
  setSelectedAssignees: Dispatch<SetStateAction<SelectedAssignee[]>>;
  selectedAssignees: SelectedAssignee[];
  description: string;
  setDescription: Dispatch<SetStateAction<string>>;
  dueDate: string | null;
  setDueDate: (value: DatePickerDate) => void;
  files: DocumentUploadFile[];
  setFiles: (files: DocumentUploadFile[]) => void;
  isPriority: boolean;
  setIsPriority: Dispatch<SetStateAction<boolean>>;
  showFormRequiredFields?: boolean;
  documents: ActionItemDocument[];
  fileUploadDestination?: INode;
  shouldReset?: boolean;
  onCommentTextChange?: (text: string) => void;
}

export interface IAdditionalProperties {
  createdBy: string;
  dateCreated: string;
  lastUpdated: string;
}

export const mockAdditionalProperties: IAdditionalProperties = {
  createdBy: "Kathy Phillips",
  dateCreated: "Sep 15, 2024",
  lastUpdated: "Today at 1:09 pm",
};

export const mockSearchDropdownItems: SuggestedUser[] = [
  {
    name: "Alice Johnson",
    email: "<EMAIL>",
    avatarUrl: "/path/to/alice-avatar.jpg",
  },
  {
    name: "Bob Smith",
    email: "<EMAIL>",
    avatarUrl: "/path/to/bob-avatar.jpg",
  },
  {
    name: "Charlie Brown",
    email: "<EMAIL>",
    avatarUrl: "/path/to/charlie-avatar.jpg",
  },
  {
    name: "David Wilson",
    email: "<EMAIL>",
    avatarUrl: "/path/to/david-avatar.jpg",
  },
  {
    name: "Eve Adams",
    email: "<EMAIL>",
    avatarUrl: "/path/to/eve-avatar.jpg",
  },
];

export interface ManageCommentsProps {
  actionItemId: string;
  isCommentingDisabled?: boolean;
  onCommentTextChange?: (text: string) => void;
}
