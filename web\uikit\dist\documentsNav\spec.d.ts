import { defaultProps } from "~/default.spec";
import { TreeBranchProps } from "~/treeView";
export interface DocumentsNavProps extends defaultProps {
    id: string;
    items: TreeBranchProps[];
    projectId: string;
    clientId: string;
    isLoading: boolean;
    updateIsRestricted: (restricted: boolean) => void;
    updateFolderItemId: (listItemId: number) => void;
    searchResultsCount?: number;
}
