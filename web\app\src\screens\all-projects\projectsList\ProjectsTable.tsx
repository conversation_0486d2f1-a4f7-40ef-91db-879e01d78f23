import React, { useState, useEffect, useRef } from "react";
import styles from "./projectsTable.module.css";
import {
  Table,
  Icon,
  StatusBadge,
  ProjectHealthStatusType,
  DropdownMenu,
  StatusBadgeType,
  normalizeProjectHealthStatusType,
  IconName,
  getDateLabel,
} from "@bcp/uikit";
import { SortKey, SortConfig, SortDirection, ProjectsListProps } from "./spec";
import { IProject } from "~/services/project";
import { RenameProjectModal } from "../../projects/renameProjectModal/RenameProjectModal";
import { useTranslation } from "react-i18next";
import { useRoleService } from "~/services/role";
import { Link } from "react-router-dom";
import classNames from "classnames";
import { formatISO } from "date-fns";

export const ProjectsTable: React.FC<ProjectsListProps> = ({ projects }) => {
  const { t, i18n } = useTranslation("allProjects");
  const { t: tGlobal } = useTranslation("global");
  const { isAdmin } = useRoleService();
  const [sortConfig, setSortConfig] = useState<{
    key: SortKey;
    direction: SortDirection;
  }>({
    key: "projectName",
    direction: "asc",
  });
  const [activeProject, setActiveProject] = useState<IProject | null>(null);
  const [sortedList, setSortedList] = useState<IProject[]>([]);
  const [showRenameProjectModal, setShowRenameProjectModal] = useState(false);
  const tableRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const sorted = [...projects].sort((a, b) => {
      const { key, direction } = sortConfig;

      let valueA: string | number | Date = "";
      let valueB: string | number | Date = "";

      switch (key) {
        case "projectName":
          valueA = (a.name ?? "").toLowerCase();
          valueB = (b.name ?? "").toLowerCase();
          break;
        case "progress":
          valueA = a.completionPercentage ?? 0;
          valueB = b.completionPercentage ?? 0;
          break;
        case "startDate":
          valueA = a.createdAt ? new Date(a.createdAt) : new Date(0);
          valueB = b.createdAt ? new Date(b.createdAt) : new Date(0);
          break;
        case "endDate":
          valueA = a.endDate ? new Date(a.endDate) : new Date(0);
          valueB = b.endDate ? new Date(b.endDate) : new Date(0);
          break;
        default:
          valueA = "";
          valueB = "";
      }

      let compare = 0;

      if (typeof valueA === "string" && typeof valueB === "string") {
        compare = valueA.localeCompare(valueB);
      } else if (valueA instanceof Date && valueB instanceof Date) {
        compare = valueA.getTime() - valueB.getTime();
      } else if (typeof valueA === "number" && typeof valueB === "number") {
        compare = valueA - valueB;
      }

      return direction === "asc" ? compare : -compare;
    });

    setSortedList(sorted);
  }, [sortConfig, projects]);

  const toggleSort = (key: SortKey) => {
    const newConfig: SortConfig = {
      key,
      direction:
        sortConfig.key === key && sortConfig.direction === "asc"
          ? "desc"
          : "asc",
    };
    setSortConfig(newConfig);
  };

  const handleProjectSelection = (project: IProject) => {
    setActiveProject(project);
    setShowRenameProjectModal(true);
  };

  // TODO: Needs updating once missing project properties are added (status, progress, start date, end date)
  const columns = [
    {
      key: "projectName" as keyof IProject,
      headerClass: "",
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("projectName")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("project-name") }
          )}
        >
          {t("project-name")}
          <Icon
            iconName={
              sortConfig.key == "projectName"
                ? sortConfig.direction == "asc"
                  ? ("filter-up-arrow" as IconName)
                  : ("filter-down-arrow" as IconName)
                : ("base-filter-arrows" as IconName)
            }
            altText=""
          />
        </button>
      ),
      showFilterIcon: false,
      tableCellClass: "",
      render: (project: IProject) => (
        <Link
          className={styles.projectLink}
          to={`/client/${project.clientId}/project/${project.id}/overview`}
        >
          <span className={classNames(styles.projectName, styles.truncate)}>
            {project.name}
          </span>
        </Link>
      ),
    },
    {
      key: "status" as keyof IProject,
      header: <div>{t("health-status")}</div>,
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (project: IProject) => {
        return (
          <StatusBadge
            badgeType={StatusBadgeType.PROJECT}
            type={
              project.statusText
                ? normalizeProjectHealthStatusType(project.statusText)
                : ProjectHealthStatusType.ONTRACK
            }
          />
        );
      },
    },
    {
      key: "progress" as keyof IProject,
      header: (
        <button
          className={styles.sortableHeader}
          // only valid sorting key for now is 'projectName', others will be added once backend is done
          onClick={() => toggleSort("progress")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("progress") }
          )}
        >
          {t("progress")}
          <Icon
            iconName={
              sortConfig.key == "progress"
                ? sortConfig.direction == "asc"
                  ? ("filter-up-arrow" as IconName)
                  : ("filter-down-arrow" as IconName)
                : ("base-filter-arrows" as IconName)
            }
            altText=""
          />
        </button>
      ),
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (project: IProject) => (
        <div className={styles.progressBarWrapper}>
          <div className={styles.progressBarContainer}>
            <div
              className={styles.progressBar}
              style={{
                width: `${
                  project?.lastVisited
                    ? new Date(project.lastVisited).getDate() * 3
                    : project.completionPercentage
                }%`,
              }}
            ></div>
          </div>
          <p className={styles.progressBarText}>
            {project.completionPercentage}%
          </p>
        </div>
      ),
    },
    {
      key: "startDate" as keyof IProject,
      header: (
        <button
          className={styles.sortableHeader}
          // only valid sorting key for now is 'projectName', others will be added once backend is done
          onClick={() => toggleSort("startDate")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("start-date") }
          )}
        >
          {t("start-date")}
          <Icon
            iconName={
              sortConfig.key == "startDate"
                ? sortConfig.direction == "asc"
                  ? ("filter-up-arrow" as IconName)
                  : ("filter-down-arrow" as IconName)
                : ("base-filter-arrows" as IconName)
            }
            altText=""
          />
        </button>
      ),
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (project: IProject) => (
        <p className={styles.projectDate}>
          {project?.startDate
            ? getDateLabel(formatISO(project?.startDate), i18n.language)
            : "N/A"}
        </p>
      ),
    },
    {
      key: "actions" as keyof IProject,
      header: <span className="sr-only">{tGlobal("actions")}</span>,
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (project: IProject) => (
        <div className={styles.actions}>
          {isAdmin && (
            <DropdownMenu
              ariaLabel={t("dropdown-menu-label", {
                projectName: project.name,
              })}
              id={project.id}
              items={[{ value: "rename", label: t("rename-project") }]}
              onChange={() => handleProjectSelection(project)}
            />
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <div className={styles.tableContainer} ref={tableRef}>
        <Table
          columns={columns}
          data={sortedList}
          className={"dropdown-menu-container"}
          emptyMessage={t("no-projects")}
          caption={t("all-projects-table")}
        />
      </div>
      <RenameProjectModal
        project={activeProject}
        setActiveProject={setActiveProject}
        showRenameProjectModal={showRenameProjectModal}
        hideModal={() => {
          setShowRenameProjectModal(false);
          const modalTrigger = tableRef.current?.querySelector<
            HTMLButtonElement
          >(`#menu\\:${activeProject?.id}\\:trigger`);
          modalTrigger?.focus();
        }}
      />
    </>
  );
};
