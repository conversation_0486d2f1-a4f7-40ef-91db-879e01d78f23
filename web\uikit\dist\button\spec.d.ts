import { defaultProps } from "~/default.spec";
import { IconName } from "../icon";
export declare enum ButtonTypeEnum {
    primary = "primary",
    secondary = "secondary",
    tertiary = "tertiary"
}
export declare enum ButtonSizeEnum {
    large = "large",
    long = "long",
    small = "small",
    icon = "icon"
}
export interface ButtonProps extends defaultProps {
    id: string;
    type: ButtonTypeEnum;
    size: ButtonSizeEnum;
    label: string;
    disabled?: boolean;
    loading?: boolean;
    labelOnLoading?: boolean;
    withRightIcon?: boolean;
    rightIconName?: IconName;
    withLeftIcon?: boolean;
    leftIconName?: IconName;
    withBorder?: boolean;
    isHighLighted?: boolean;
    onClick: () => void;
    className?: string;
    isWarning?: boolean;
}
