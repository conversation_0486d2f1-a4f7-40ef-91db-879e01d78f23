import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
import { IconColor } from "~/selectionCard/spec";
export interface TaskTypeDropdownProps extends defaultProps {
    id: string;
    items: TaskDropdownItem[];
    onChange: (item: TaskDropdownItem) => void;
}
export interface TaskDropdownItem {
    label: string;
    value: string;
    color: IconColor;
    iconName: IconName;
}
