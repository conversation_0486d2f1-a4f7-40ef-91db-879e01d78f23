import React, { useState, useMemo, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import styles from "./editProjectStages.module.css";
import { Toggle } from "~/toggle";
import { Icon } from "~/icon";
import { DropdownInput } from "~/dropdownInput";
import { DropdownInputItem } from "~/dropdownInput/spec";
import { projectTimelineStatus } from "./spec";
import { Stage, StageStatus } from "~/timeline";
import { CustomDatePicker } from "~/datePicker";
import { defaultProps } from "~/default.spec";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import { getDateRangeLabel } from "../utils/dateUtils";
import { formatISO } from "date-fns";
import { Input } from "~/input";

interface SubStage {
  id: string;
  title: string;
  translationKey: string;
  // Keep startDate and endDate as nullable for creating new substages (before saving), otherwise will never be null
  startDate?: Date;
  endDate?: Date;
  status: string;
  sortOrder?: number;
  // Temporary flag to distinguish new template substages
  _isNewTemplate?: boolean;
}

interface EditProjectStagesProps extends defaultProps {
  id: string;
  stageName: string;
  subStages: SubStage[];
  substageOptions: { label: string; value: string }[];
  startDate?: Date;
  endDate?: Date;
  status: string;
  isActive: boolean;
  setStages: React.Dispatch<React.SetStateAction<Stage[]>>;
  onDeleteSubStage: (stageId: string, subStageId: string) => void;
  onAddSubstage: (stageId: string) => void;
  onAddCustomSubstage: (stageId: string) => void;
  showValidationErrors?: boolean;
}

// Helper function to validate if all required fields are filled
export const validateSubStages = (subStages: SubStage[]): boolean => {
  return subStages.every(subStage => {
    // All substages must have a title (maps to 'Kind' field in database)
    // The presence of translationKey determines if it's template-based or custom
    const hasTitle = subStage.title && subStage.title.trim() !== "";
    const hasDates = subStage.startDate && subStage.endDate;
    const hasStatus = subStage.status && subStage.status !== "";

    return hasTitle && hasDates && hasStatus;
  });
};

export const EditProjectStages: React.FC<EditProjectStagesProps> = ({
  id,
  stageName,
  startDate,
  endDate,
  status,
  isActive,
  subStages,
  setStages,
  onDeleteSubStage,
  onAddSubstage,
  onAddCustomSubstage,
  substageOptions,
  showValidationErrors = false,
  role,
  dataTestId = "uikit-EditProjectStages",
  ariaLabel,
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
}) => {
  const { t, i18n } = useTranslation("overview");
  const { t: tDropdown } = useTranslation("dropdown");
  const { t: tProjectStages } = useTranslation("projectStages");
  const [expanded, setExpanded] = useState(false);
  const [showAddSubstageDropdown, setShowAddSubstageDropdown] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [draggedSubstageId, setDraggedSubstageId] = useState<string | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const portalDropdownRef = useRef<HTMLDivElement>(null);

   let today = new Date();
   today = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);

  // Calculate dropdown position
  const calculateDropdownPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const dropdownWidth = 200; // min-width from CSS
      const dropdownHeight = 80; // approximate height for 2 options

      let top = rect.bottom + window.scrollY + 4; // 4px gap below button
      let left = rect.left + window.scrollX;

      // Adjust if dropdown would go off-screen horizontally
      if (left + dropdownWidth > window.innerWidth) {
        left = window.innerWidth - dropdownWidth - 10; // 10px margin
      }

      // Adjust if dropdown would go off-screen vertically
      if (top + dropdownHeight > window.innerHeight + window.scrollY) {
        top = rect.top + window.scrollY - dropdownHeight - 4; // Show above button
      }

      setDropdownPosition({ top, left });
    }
  };

  // Handle dropdown toggle
  const handleDropdownToggle = () => {
    if (!showAddSubstageDropdown) {
      calculateDropdownPosition();
    }
    setShowAddSubstageDropdown(!showAddSubstageDropdown);
  };

  // Close dropdown when clicking outside and handle position updates
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if click is inside the dropdown container or the portal dropdown
      const isInsideContainer = dropdownRef.current && dropdownRef.current.contains(target);
      const isInsidePortalDropdown = portalDropdownRef.current && portalDropdownRef.current.contains(target);

      if (!isInsideContainer && !isInsidePortalDropdown) {
        setShowAddSubstageDropdown(false);
      }
    };

    const handleScroll = () => {
      if (showAddSubstageDropdown) {
        calculateDropdownPosition();
      }
    };

    const handleResize = () => {
      if (showAddSubstageDropdown) {
        calculateDropdownPosition();
      }
    };

    if (showAddSubstageDropdown) {
      // Use a slight delay to ensure the dropdown is rendered before adding the listener
      setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside);
      }, 0);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [showAddSubstageDropdown]);

  const defaultSelection = useMemo(() => {
    const dropdownStatus = projectTimelineStatus.find(item => {
      return item.value == status;
    });

    const label = dropdownStatus?.translationKey
      ? tDropdown(`${dropdownStatus.translationKey}-label`)
      : dropdownStatus?.value;

    return {
      value: status,
      label: label,
      description:
        dropdownStatus?.translationKey &&
        i18n.exists(`dropdown:${dropdownStatus.translationKey}-description`)
          ? tDropdown(`${dropdownStatus.translationKey}-description`)
          : "",
    };
  }, [status]);

  const memoizedSubStages = useMemo(() => {
    return subStages.map(subStage => {
      const dropdownStatus = projectTimelineStatus.find(
        status => status.value == subStage.status
      );

      const label = dropdownStatus?.translationKey
        ? tDropdown(`${dropdownStatus.translationKey}-label`)
        : dropdownStatus?.value;

      return {
        ...subStage,
        defaultSelection: {
          value: dropdownStatus?.value ?? subStage.status,
          label: label ?? subStage.status,
          icon: dropdownStatus?.icon ?? "",
        },
      };
    });
  }, [subStages]);

  const handleStatusChange = (item: DropdownInputItem) => {
    setStages(prevStages =>
      prevStages.map(stage =>
        stage.id === id
          ? { ...stage, status: item.value as StageStatus }
          : stage
      )
    );
  };

  const handleDateChange = (dates: Date[]) => {
    if (dates && dates.length == 2) {
      const startDate = dates[0];
      const endDate = dates[1];
      setStages(prevStages =>
        prevStages.map(stage =>
          stage.id === id
            ? {
                ...stage,
                startDate: startDate,
                endDate: endDate,
              }
            : stage
        )
      );
    }
  };

  const handleToggleChange = () => {
    setStages(prevStages =>
      prevStages.map(stage =>
        stage.id === id ? { ...stage, active: !stage.active } : stage
      )
    );
  };

  const handleSubstageStatusChange = (
    subStageId: string,
    item: DropdownInputItem
  ) => {
    setStages(prevStages =>
      prevStages.map(stage =>
        stage.id === id
          ? {
              ...stage,
              substages: stage.substages?.map(s =>
                s.id === subStageId
                  ? { ...s, status: item.value as StageStatus }
                  : s
              ),
            }
          : stage
      )
    );
  };

  const handleDeleteClick = (subStageId: string) => {
    onDeleteSubStage(id, subStageId);
  };

  const handleSubstageTitleChange = (
    subStageId: string,
    item: DropdownInputItem
  ) => {
    setStages(prevStages =>
      prevStages.map(stage =>
        stage.id === id
          ? {
              ...stage,
              substages: stage.substages?.map(s =>
                s.id === subStageId
                  ? {
                      ...s,
                      translationKey: item.value as string,
                      title: item.label || item.value as string, // Clear placeholder and set proper title
                    }
                  : s
              ),
            }
          : stage
      )
    );
  };

  const handleSubstageDateChange = (dates: Date[], subStageId: string) => {
    if (dates && dates.length == 2) {
      const startDate = dates[0];
      const endDate = dates[1];
      setStages(prevStages =>
        prevStages.map(stage =>
          stage.id === id
            ? {
                ...stage,
                substages: stage.substages?.map(s =>
                  s.id === subStageId
                    ? {
                        ...s,
                        startDate: startDate,
                        endDate: endDate,
                      }
                    : s
                ),
              }
            : stage
        )
      );
    }
  };

  const handleCustomSubstageNameChange = (subStageId: string, title: string) => {
    setStages(prevStages =>
      prevStages.map(stage =>
        stage.id === id
          ? {
              ...stage,
              substages: stage.substages?.map(s =>
                s.id === subStageId
                  ? { ...s, title: title }
                  : s
              ),
            }
          : stage
      )
    );
  };

  // Drag and drop handlers for substage reordering
  const handleDragStart = (e: React.DragEvent, subStageId: string) => {
    setDraggedSubstageId(subStageId);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", subStageId);
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    const draggedId = e.dataTransfer.getData("text/plain");

    if (!draggedId) return;

    setStages(prevStages =>
      prevStages.map(stage => {
        if (stage.id === id && stage.substages) {
          const substages = [...stage.substages];
          const draggedIndex = substages.findIndex(s => s.id === draggedId);

          if (draggedIndex === -1 || draggedIndex === dropIndex) return stage;

          // Remove the dragged item
          const [draggedItem] = substages.splice(draggedIndex, 1);

          // Insert at new position
          substages.splice(dropIndex, 0, draggedItem);

          // Update sort order for all substages
          const updatedSubstages = substages.map((substage, index) => ({
            ...substage,
            sortOrder: index
          }));

          return {
            ...stage,
            substages: updatedSubstages
          };
        }
        return stage;
      })
    );

    setDraggedSubstageId(null);
    setDragOverIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedSubstageId(null);
    setDragOverIndex(null);
  };

  const dates = [];
  if (startDate) {
    dates.push(startDate);
  }
  if (endDate) {
    dates.push(endDate);
  }
  const datesSet = new Set<Date>(dates);

  return (
    <div
      className={styles.container}
      data-testid={dataTestId}
      role={role}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-labelledby={ariaLabelledBy}
      tabIndex={tabIndex}
    >
      <div className={styles.firstStage}>
        <button
          id={`${id}-trigger`}
          aria-controls={`${id}-accordion-content`}
          aria-expanded={expanded}
          className={styles.expandButton}
          onClick={() => setExpanded(!expanded)}
          aria-label={t(
            expanded ? "view-substages-collapse" : "view-substages-expand",
            { stageName }
          )}
        >
          {!expanded ? (
            <Icon iconName={"chevron"} />
          ) : (
            <Icon iconName={"chevron-down"} />
          )}
        </button>
        <Toggle
          ariaLabel={t(
            isActive ? "toggle-stage-no-active" : "toggle-stage-to-active",
            { stageName }
          )}
          isDefaultChecked={isActive}
          onCheckedChange={handleToggleChange}
        />
        <div className={styles.details}>
          <div className={styles.stageName}>{stageName}</div>
          <div className={styles.subStages}>{subStages.length} substages</div>
        </div>
        <div className={styles.statusDropdown}>
          <CustomDatePicker
            ariaLabel=""
            childrenTrigger={
              <button className={styles.editProjectDateInput}>
                <Icon iconName="calendar-placeholder" height={14} width={14} />
                <div className={styles.datePlaceholder}>
                  <span className="sr-only">{stageName}</span>
                  <span className="sr-only">{t("date-range")}</span>
                  {startDate && endDate
                    ? getDateRangeLabel(datesSet, i18n.language)
                    : t("date-range")}
                </div>
                <div className={styles.dropdownArrow}>
                  <Icon iconName="dropdown-arrow-down" />
                </div>
              </button>
            }
            closeOnSelect={true}
            placeholder={""}
            startDate={startDate ? formatISO(startDate) : ""}
            endDate={endDate ? formatISO(endDate) : ""}
            selectionMode="range"
            onRangeChange={(dates: Date[]) => {
              handleDateChange(dates);
            }}
            calendarId={id}
            onChange={() => {}}
          />
        </div>
        <div className={styles.statusDropdown}>
          <DropdownInput
            ariaLabel={t("status-label", {
              stageName,
              status: defaultSelection.label,
            })}
            items={projectTimelineStatus.map(status => ({
              ...status,
              label: tDropdown(`${status.translationKey}-label`),
              useCustomStyle: true,
            }))}
            onSelectionChange={handleStatusChange}
            defaultSelection={defaultSelection}
            error={false}
            withIcon={true}
            id={`projectTimeline-${id}-status`}
            floatingLabelEnabled={false}
            displayOffset={true}
            enableSearch={false}
            limitDropdownWidth={false}
            leftIconPaddingLeft="2.5rem"
            dropdownPortalClass={styles.statusDropdownWrapper}
            showCTA={false}
          />
        </div>
      </div>
      {expanded && (
        <div
          className={styles.subStageContainer}
          id={`${id}-accordion-content`}
          role="region"
          aria-labelledby={`${id}-trigger`}
        >
          {memoizedSubStages.map((subStage, index) => {

            const substageDates = [];
            if (subStage.startDate) {
              substageDates.push(subStage.startDate);
            }
            if (subStage.endDate) {
              substageDates.push(subStage.endDate);
            }
            const substageDatesSet = new Set<Date>(substageDates);
            // Determine if this is a custom substage
            const isNewTemplateSubstage = subStage.title === "TEMPLATE_PLACEHOLDER";
            const isCustomSubstage = isNewTemplateSubstage
              ? false // New template substage
              : (!subStage.translationKey || subStage.translationKey.trim() === "");

            const isDragging = draggedSubstageId === subStage.id;
            const isDragOver = dragOverIndex === index;

            return (
              <div
                key={subStage.id}
                className={classNames(styles.subStage, {
                  [styles.customSubstageRow]: isCustomSubstage,
                  [styles.dragging]: isDragging,
                  [styles.dragOver]: isDragOver
                })}
                draggable={true}
                onDragStart={(e) => handleDragStart(e, subStage.id)}
                onDragOver={(e) => handleDragOver(e, index)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, index)}
                onDragEnd={handleDragEnd}
              >
                <div className={styles.dragHandle}>
                  <Icon iconName="drag-handle" width={16} height={42} />
                </div>
                <div className={styles.substageNameField}>
                  {isCustomSubstage ? (
                    <Input
                      placeholder={t("sub-stage")}
                      value={subStage.title || ""}
                      onValueChange={(value) => handleCustomSubstageNameChange(subStage.id, value)}
                      inputId={`substageTimeline-${subStage.id}-name`}
                      floatingLabelEnabled={false}
                      error={showValidationErrors && (!subStage.title || subStage.title.trim() === "")}
                      errorMessage=""
                      ariaLabel={t("sub-stage")}
                    />
                  ) : (
                    <DropdownInput
                      placeholder={t("sub-stage")}
                      items={substageOptions}
                      onSelectionChange={item =>
                        handleSubstageTitleChange(subStage.id, item)
                      }
                      defaultSelection={isNewTemplateSubstage ? undefined : {
                        value: subStage.translationKey,
                        label: tProjectStages(subStage.translationKey),
                      }}
                      ariaLabel={t("sub-stage")}
                      error={showValidationErrors && (!subStage.translationKey || subStage.translationKey === "")}
                      errorMessage=""
                      id={`substageTimeline-${subStage.id}-title`}
                      floatingLabelEnabled={false}
                      enableSearch={false}
                      displayOffset={true}
                      limitDropdownWidth={false}
                      showCTA={false}
                      
                    />
                  )}
                 {showValidationErrors && ((isCustomSubstage && (!subStage.title || subStage.title.trim() === ""))
                   || (!isCustomSubstage && (!subStage.translationKey || subStage.translationKey === ""))) &&
                  <div className={styles.inlineErrorMessage}>
                    <Icon iconName="error-message-icon" size={20}/>
                    {isCustomSubstage &&
                      t("sub-stage-required")
                    }
                    {!isCustomSubstage &&
                      t("sub-stage-required")
                    }
                  </div>
                  }
                </div>
                <div className={styles.substageeDateField}>
                  <CustomDatePicker
                    ariaLabel=""
                    childrenTrigger={
                      <button className={classNames(styles.editProjectDateInput, {
                        [styles.errorState]: showValidationErrors && (!subStage.startDate || !subStage.endDate)
                      })}>
                        <span className="sr-only">
                          {isCustomSubstage ? subStage.title : tProjectStages(subStage.translationKey)}
                        </span>
                        <span className="sr-only">{t("date-range")}</span>
                        <Icon
                          iconName="calendar-placeholder"
                          height={14}
                          width={14}
                        />
                        <div
                          className={classNames(styles.datePlaceholder, {
                            [styles.faded]:
                              !subStage.startDate || !subStage.endDate,
                          })}
                        >
                          {subStage.startDate && subStage.endDate
                            ? getDateRangeLabel(substageDatesSet, i18n.language)
                            : t("date-range")}
                        </div>
                        <div className={styles.dropdownArrow}>
                          <Icon iconName="dropdown-arrow-down" />
                        </div>
                      </button>
                    }
                    closeOnSelect={true}
                    placeholder={""}
                    startDate={subStage.startDate ? formatISO(subStage.startDate) : formatISO(today)}
                    endDate={subStage.endDate ? formatISO(subStage.endDate) : formatISO(today)}
                    selectionMode="range"
                    onRangeChange={(dates: Date[]) => {
                      handleSubstageDateChange(dates, subStage.id);
                    }}
                    calendarId={`${stageName.split(" ").join("_")}-${
                      subStage.id
                    }`}
                    onChange={() => {}}
                    triggerClassName={styles.dateTrigger}
                  />
                  { showValidationErrors && (!subStage.startDate || !subStage.endDate) &&
                    <div className={styles.inlineErrorMessage}>
                       <Icon iconName="error-message-icon" size={20}/>
                       {t("date-range-required")}
                    </div>
                 }
                </div>
                <div className={styles.substageStatusField}>
                  <DropdownInput
                    ariaLabel={t("status-label", {
                      stageName: isCustomSubstage ? subStage.title : tProjectStages(subStage.translationKey),
                      status: subStage.defaultSelection.label,
                    })}
                    items={projectTimelineStatus.map(status => ({
                      ...status,
                      label: tDropdown(`${status.translationKey}-label`),
                      useCustomStyle: true,
                    }))}
                    onSelectionChange={item => {
                      handleSubstageStatusChange(subStage.id, item);
                    }}
                    defaultSelection={subStage.defaultSelection}
                    error={showValidationErrors && (!subStage.status || subStage.status === "")}
                    errorMessage=""
                    withIcon={true}
                    floatingLabelEnabled={false}
                    id={`substageTimeline-${subStage.id}`}
                    displayOffset={true}
                    enableSearch={false}
                    limitDropdownWidth={false}
                    leftIconPaddingLeft="2.5rem"
                    dropdownPortalClass={styles.statusDropdownWrapper}
                    showCTA={false}
                  />
                  { showValidationErrors && (!subStage.status || subStage.status === "") &&
                  <div className={styles.inlineErrorMessage}>
                      <Icon iconName="error-message-icon" size={20}/>
                      {t("status-required")}
                  </div>
                 }
                </div>
                <button
                  aria-label={t("remove-substage", {
                    stageName: isCustomSubstage ? subStage.title : tProjectStages(subStage.translationKey),
                  })}
                  className={styles.deleteButton}
                  onClick={() => handleDeleteClick(subStage.id)}
                >
                  <Icon iconName={"recycle-bin"} />
                </button>
              </div>
            );
          })}
          <div className={styles.addSubstageContainer} ref={dropdownRef}>
            <button
              ref={buttonRef}
              className={styles.addSubstageButton}
              onClick={handleDropdownToggle}
              aria-expanded={showAddSubstageDropdown}
              aria-haspopup="true"
            >
              {t("add-substage")}
              <Icon iconName="dropdown-arrow-down" />
            </button>
            {showAddSubstageDropdown && createPortal(
              <div
                ref={portalDropdownRef}
                className={styles.addSubstageDropdown}
                style={{
                  top: `${dropdownPosition.top}px`,
                  left: `${dropdownPosition.left}px`,
                }}
              >
                <button
                  className={styles.dropdownOption}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onAddSubstage(id);
                    setShowAddSubstageDropdown(false);
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                >
                  {t("add-from-template")}
                </button>
                <button
                  className={styles.dropdownOption}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onAddCustomSubstage(id);
                    setShowAddSubstageDropdown(false);
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                >
                  {t("create-custom-substage")}
                </button>
              </div>,
              document.body
            )}
          </div>
        </div>
      )}
    </div>
  );
};
