import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
export interface TextButtonProps extends defaultProps {
    id: string;
    size: TextButtonSizeEnum;
    disabled?: boolean;
    label: string;
    onClick: () => void;
    iconName?: IconName;
    customIconSize?: number;
    includeChevron?: boolean;
    className?: string;
}
export declare enum TextButtonSizeEnum {
    large = "large",
    medium = "medium",
    small = "small",
    inline = "inline"
}
