import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
export interface SelectionCardProps extends defaultProps {
    title: string;
    subtitle?: string;
    onCardClick: () => void;
    theme?: BackgroundColor;
    cardStyles?: string;
    leftIconConfig?: {
        name: IconName;
        altText: string;
        colorOverride?: IconColor;
    };
    rightIconConfig?: {
        name: IconName;
        altText: string;
        colorOverride?: IconColor;
    };
    disabled?: boolean;
}
export declare enum BackgroundColor {
    WHITE = "white",
    CHARCOAL = "charcoal"
}
export declare enum IconColor {
    BURGUNDY = "burgundy",
    EMERALD = "emerald",
    OCEAN = "ocean",
    GOLD = "gold",
    JADE = "jade"
}
