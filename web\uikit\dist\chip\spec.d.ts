import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
export interface ChipProps extends defaultProps {
    text: string;
    onDismissBtnClick?: () => void;
    onClickAction?: () => void;
    type?: ChipType;
    iconName?: IconName;
    iconWidth?: number | string;
    iconHeight?: number | string;
    backgroundColor?: string;
    foregroundColor?: string;
    textOnly?: boolean;
    id?: string;
}
export declare enum ChipType {
    DEFAULT = "default",
    WARNING = "warning",
    ERROR = "error",
    ACTION = "action",
    LOCATION = "location",
    REQUEST = "request",
    PRIORITY = "priority",
    STATUS = "status",
    DUE_DATE = "dueDate",
    OVERVIEW = "overview",
    READONLY = "readOnly"
}
