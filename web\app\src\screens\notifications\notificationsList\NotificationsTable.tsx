import React, { use<PERSON>allback, useMemo, useState } from "react";
import styles from "./notificationsTable.module.css";
import {
  Table,
  Icon,
  NotificationTypeBadge,
  ChipDropdownItem,
  IconName,
  ButtonTypeEnum,
  ButtonSizeEnum,
  Button,
  Spinner,
  getTimezoneConvertedDate,
  getDateTimeLabel,
} from "@bcp/uikit";
import { SortKey, SortConfig, NotificationsListProps } from "./spec";
import { NotificationFilters } from "./notificationFilters/NotificationFilters";
import { NotificationBadgeType } from "@bcp/uikit";
import { NotificationType } from "~/services/user";
import { useTranslation } from "react-i18next";
import useNavigateWithParams from "~/utils/navigate/useNavigateWithParams";
import classNames from "classnames";
import {
  IGenericEntity,
  INotificationData,
  NotificationCategoryUtil,
} from "~/services/notifications";
import { parseTemplate } from "~/utils/generic/templateUtils";
import { formatISO } from "date-fns";
import ImportantStatus from "./importantStatus/ImportantStatus";
import ReadStatus from "./readStatus/ReadStatus";
import { useNotificationsService } from "../../../services/notifications/useNotificationsService";

export const NotificationsTable: React.FC<NotificationsListProps> = ({
  notifications,
  projects,
  selectedProjects,
  selectedNotificationTypes,
  sortConfig,
  isBusy,
  isFetchingMore,
  hasMore,
  notificationQuantity,
  seeUnreadNotification,
  seeImportantNotification,
  setSelectedNotificationTypes,
  setSelectedProjects,
  setSortConfig,
  onFetchMore,
  setPageData,
  setSeeImportantNotification,
  setseeUnreadNotification,
  setNotificationQuantity,
}) => {
  const { t, i18n } = useTranslation("notifications");
  const { t: tGlobal } = useTranslation("global");
  const { updateReadAt } = useNotificationsService();
  const navigate = useNavigateWithParams();
  const [_, setActiveNotification] = useState<INotificationData | null>(null);

  const data = notifications ?? [];

  const filterChipOptions = {
    showNotificationType: true,
    showProjectFilter: true,
  };

  const generateUniqueProjects = (
    data: IGenericEntity[]
  ): ChipDropdownItem[] => {
    const uniqueProjects = Array.from(
      new Set(data.map(row => row).filter(Boolean))
    );
    return uniqueProjects.map(project => ({
      id: project.id,
      label: project.name,
      value: project.id,
      includeAvatar: false,
    }));
  };

  const projectsFilter = useMemo(() => generateUniqueProjects(projects ?? []), [
    projects,
  ]);

  const getActivityTemplate = useCallback(
    (notification: INotificationData): string => {
      const key = `activity_${notification.notificationType}`;
      return t(key) ?? "";
    },
    [t]
  );

  const getPathUrl = (path?: string) => {
    if (!path || typeof path !== "string") return "";
    const segments = path.split("/").filter(Boolean);
    if (segments.length <= 1) return "";

    segments.pop(); // remove the file name and extension
    const encodedPath = segments.map(encodeURIComponent).join("/");
    return `/${encodedPath}`;
  };

  const getFolderName = (path: string): string => {
    const parts = path.split("/").filter(Boolean);
    return parts.length >= 2 ? parts[parts.length - 2] : "";
  };

  const getActivityContent = useCallback(
    (notification: INotificationData): React.ReactNode[] => {
      const payload = JSON.parse(notification.payload ?? "{}");
      const template = getActivityTemplate(notification);
      const bold = (text: string | undefined): JSX.Element => (
        <strong className={styles.titleLink}>{text}</strong>
      );

      const replacements: Record<string, React.ReactNode> = (() => {
        switch (notification.notificationType) {
          case NotificationType.ActionItemAssigned:
          case NotificationType.ActionItemCommentAdded:
            return {
              item: bold(payload.taskName),
            };

          case NotificationType.ActionItemStatusChanged: {
            const statusKey = `action_item_status_${payload.status?.toLowerCase()}`;
            return {
              item: bold(payload.taskName),
              status: bold(t(statusKey)),
            };
          }
          case NotificationType.FinalDeliveryAdded:
            return {
              doc: bold(payload.documentName),
              folder: bold(getFolderName(payload.path)),
            };

          case NotificationType.ProjectHealthStatusChanged: {
            const statusKey = `project_health_status_${payload.status?.toLowerCase()}`;
            return {
              project: bold(notification.project?.name),
              status: bold(t(statusKey)),
            };
          }

          case NotificationType.ProjectInvite:
            return {
              project: bold(notification.project?.name),
            };

          case NotificationType.PortalInvite:
            return {};

          default:
            return {};
        }
      })();

      return parseTemplate(template, replacements);
    },
    [getActivityTemplate]
  );

  const getNotificationCategory = (
    value: string | NotificationType
  ): NotificationBadgeType => {
    return NotificationCategoryUtil.getCategoryByType(value);
  };

  const notificationTypes: ChipDropdownItem[] = [
    {
      id: NotificationBadgeType.ACTION_ITEM,
      label: t("action-item"),
      value: NotificationBadgeType.ACTION_ITEM,
    },
    {
      id: NotificationBadgeType.DELIVERABLE,
      label: t("deliverable"),
      value: NotificationBadgeType.DELIVERABLE,
    },
    {
      id: NotificationBadgeType.PROJECT_HEALTH_STATUS,
      label: t("project-health-status"),
      value: NotificationBadgeType.PROJECT_HEALTH_STATUS,
    },
    {
      id: NotificationBadgeType.GENERAL,
      label: t("general"),
      value: NotificationBadgeType.GENERAL,
    },
  ];

  const handleRowClick = async (notificationId: string) => {
    const notification = data.find(
      (item: INotificationData) => item.notificationId === notificationId
    );
    if (notification) {
      setActiveNotification(notification);
      try {
        await updateReadAt(true, notificationId);
        setNotificationQuantity(prev => prev && Math.max(0, prev - 1));

        const payload = JSON.parse(notification.payload ?? "{}");

        switch (notification.notificationType) {
          case NotificationType.ActionItemAssigned:
          case NotificationType.ActionItemCommentAdded:
          case NotificationType.ActionItemStatusChanged: {
            const taskId = payload.taskId;
            navigate(
              `/client/${notification.client?.id}/project/${notification.project?.id}/action-items/${taskId}`
            );
            break;
          }
          case NotificationType.FinalDeliveryAdded: {
            const path = getPathUrl(payload.path);
            navigate(
              `/client/${notification.client?.id}/project/${notification.project?.id}/documents?path=${path}`
            );
            break;
          }
          case NotificationType.ProjectHealthStatusChanged:
          case NotificationType.ProjectInvite:
            navigate(
              `/client/${notification.client?.id}/project/${notification.project?.id}`
            );
            break;
          default:
            return;
        }
      } catch (error) {
        console.error(error);
      }
    }
  };

  const toggleSort = (key: SortKey) => {
    const newConfig: SortConfig = {
      key,
      direction:
        sortConfig.key === key && sortConfig.direction === "asc"
          ? "desc"
          : "asc",
    };
    setSortConfig(newConfig);
  };

  const columns = [
    {
      key: "isImportant" as keyof INotificationData,
      header: <></>,
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "notificationActivityTableCell",
      render: (notification: INotificationData) => (
        <ImportantStatus
          notification={notification}
          setPageData={setPageData}
          seeImportantNotification={seeImportantNotification}
        />
      ),
    },
    {
      key: "text" as keyof INotificationData,
      headerClass: "notificationActivityTableHeader",
      header: <div>{tGlobal("activity")}</div>,
      showFilterIcon: false,
      tableCellClass: "notificationActivityTableCell",
      render: (notification: INotificationData) => (
        <div className={styles.activity}>
          <p className={classNames(styles.text, styles.truncate)}>
            {getActivityContent(notification)}
          </p>
          {notification.notificationType !== NotificationType.ProjectInvite && (
            <p className={styles.userName}>{notification.actor?.name}</p>
          )}
        </div>
      ),
    },
    {
      key: "projectName" as keyof INotificationData,
      headerClass: "",
      header: <div>{tGlobal("project")}</div>,
      showFilterIcon: false,
      tableCellClass: "",
      render: (notification: INotificationData) => (
        <p className={classNames(styles.projectName, styles.truncate)}>
          {notification.project?.name}
        </p>
      ),
    },
    {
      key: "type" as keyof INotificationData,
      header: <div>{tGlobal("type")}</div>,
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (notification: INotificationData) => {
        let normalizedType: NotificationBadgeType;
        try {
          normalizedType = getNotificationCategory(
            notification.notificationType
          );
        } catch (error) {
          console.error(error);
          normalizedType = NotificationBadgeType.GENERAL;
        }
        return (
          <div className={styles.notificationTypeBadge}>
            <NotificationTypeBadge type={normalizedType} />
          </div>
        );
      },
    },
    {
      key: "happenedAt" as keyof INotificationData,
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("happenedAt")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("time") }
          )}
        >
          {tGlobal("time")}
          <div className={styles.sortTableIcon}>
            <Icon
              iconName={
                sortConfig.key == "happenedAt"
                  ? sortConfig.direction == "asc"
                    ? ("filter-up-arrow" as IconName)
                    : ("filter-down-arrow" as IconName)
                  : ("base-filter-arrows" as IconName)
              }
            />
          </div>
        </button>
      ),
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (notification: INotificationData) => {
        const notificationDate = getTimezoneConvertedDate(
          notification?.happenedAt ?? new Date("")
        );

        return (
          <div className={styles.time}>
            <p className={styles.date}>
              {notification?.happenedAt
                ? getDateTimeLabel(
                    formatISO(notificationDate),
                    i18n.language,
                    true,
                    true
                  )
                : "N/A"}
            </p>
            <ReadStatus
              notification={notification}
              setPageData={setPageData}
              seeUnreadNotification={seeUnreadNotification}
              setNotificationQuantity={setNotificationQuantity}
            />
          </div>
        );
      },
    },
  ];

  const FetchMore = (hasMore || isFetchingMore) && (
    <Spinner isLoading={isFetchingMore} background="#ffffff" size={"large"}>
      <div className={styles.fetchMore}>
        <Button
          id="notification-table-fetch-more-button"
          type={ButtonTypeEnum.primary}
          size={ButtonSizeEnum.long}
          label={t("load-more")}
          withRightIcon={false}
          onClick={() => onFetchMore?.()}
        />
      </div>
    </Spinner>
  );

  const handleMarkAllAsRead = async () => {
    await updateReadAt(true);
    setPageData(prev =>
      (prev ?? []).map(n => {
        return { ...n, readAt: new Date() };
      })
    );
    setNotificationQuantity(0);
  };

  return (
    <>
      <div className={styles.tableContainer}>
        <div className={styles.filtersContainer}>
          <NotificationFilters
            initialNotificationTypes={new Set()}
            initialProjects={new Set()}
            onFiltersChange={({ notificationTypes, projects }) => {
              setSelectedNotificationTypes(notificationTypes);
              setSelectedProjects(projects);
            }}
            filters={filterChipOptions}
            projects={projectsFilter}
            notificationTypes={notificationTypes}
            showResetButton={
              selectedNotificationTypes.size > 0 ||
              selectedProjects.size > 0 ||
              seeUnreadNotification ||
              seeImportantNotification
            }
            seeImportantNotification={seeImportantNotification}
            seeUnreadNotification={seeUnreadNotification}
            setSeeImportantNotification={setSeeImportantNotification}
            setseeUnreadNotification={setseeUnreadNotification}
          />
          <div className={styles.markAllAsRead}>
            <Button
              type={ButtonTypeEnum.primary}
              onClick={handleMarkAllAsRead}
              id="Mark-all-as-read"
              label={t("mark-all-as-Read")}
              size={ButtonSizeEnum.small}
              disabled={!notificationQuantity}
              withRightIcon={false}
              className=""
            />
          </div>
        </div>
        <Spinner
          isLoading={isBusy}
          background="rgba(255,255,255,0.8)"
          size={"large"}
        >
          <Table
            columns={columns}
            data={data}
            className="dropdown-menu-container"
            emptyMessage={t("empty-message")}
            trailingComponent={FetchMore}
            highlightImportantRow={(notification: INotificationData) =>
              notification.isImportant
            }
            onClickRow={(notification: INotificationData) =>
              handleRowClick(notification.notificationId)
            }
          />
        </Spinner>
      </div>
    </>
  );
};
