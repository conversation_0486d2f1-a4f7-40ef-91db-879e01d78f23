import { useTranslation } from "react-i18next";
import { DocumentRow } from "../spec";
import sharedStyles from "../sharedTableStyles.module.css";
import styles from "./documentDetails.module.css";
import { Popover, Portal } from "@ark-ui/react";
import { useState, useEffect } from "react";
import {
  Chip,
  ChipType,
  getDocumentsDateLabel,
  Icon,
  Modal,
  ModalSize,
  Spinner,
  CustomAvatar,
  getInitials,
} from "@bcp/uikit";
import { useDocumentService } from "~/services/document";
import { useSettingsService } from "~/services/settings";
import { useClientService } from "~/services/client";
import { useProjectService } from "~/services/project";
import { UserAvatars } from "./userAvatars/UserAvatars";

interface DocumentDetailsProps {
  item: DocumentRow;
  location: string;
  onManageAccess: () => void;
  showManageAccess: boolean;
  show: boolean;
}

function getLastPathSegment(url: string): string {
  const segments = url.split("/").filter(Boolean);
  return segments[segments.length - 1] || "";
}

interface User {
  id: string;
  displayName: string;
  email: string;
}

export const DocumentDetails: React.FC<DocumentDetailsProps> = ({
  item,
  location = "",
  showManageAccess = true,
  onManageAccess,
}) => {
  const { t, i18n } = useTranslation("documents");
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [usersWithAccess, setUsersWithAccess] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [showUsersModal, setShowUsersModal] = useState(false);
  const { getPermissions } = useDocumentService();
  const { memberFirmId } = useSettingsService();
  const { activeClient } = useClientService();
  const { activeProject } = useProjectService();

  // Call getPermissions when popover opens and item is restricted
  useEffect(() => {
    if (detailsOpen && item.restricted && item.listItemId) {
      setLoadingUsers(true);
      getPermissions(
        memberFirmId,
        activeClient?.bgpId || 0,
        activeProject?.bgpId || 0,
        item.listItemId
      )
        .then(data => {
          if (data.users && data.users.length > 0) {
            const users = data.users.map((user: any) => ({
              id: user.id,
              displayName: user.displayName,
              email: user.email,
            }));
            setUsersWithAccess(users);
          } else {
            setUsersWithAccess([]);
          }
        })
        .catch(error => {
          console.error("Error calling getPermissions:", error);
          setUsersWithAccess([]);
        })
        .finally(() => {
          setLoadingUsers(false);
        });
    } else if (!detailsOpen) {
      // Reset users when popover closes
      setUsersWithAccess([]);
      setLoadingUsers(false);
    }
  }, [
    detailsOpen,
    item.restricted,
    item.listItemId,
    memberFirmId,
    activeClient?.bgpId,
    activeProject?.bgpId,
    getPermissions,
  ]);

  return (
    <Popover.Root
      portalled
      open={detailsOpen}
      onOpenChange={({ open }: Popover.OpenChangeDetails) =>
        setDetailsOpen(open)
      }
      positioning={{
        placement: "bottom-end",
        fitViewport: true,
        strategy: "fixed",
      }}
    >
      <Popover.Trigger
        className={sharedStyles.infoButton}
        aria-label={t("view-details", { title: item.title })}
      >
        <Icon iconName={"info-icon-black-and-white"} />
      </Popover.Trigger>
      <Portal>
        <Popover.Positioner style={{ zIndex: 700 }}>
          <Popover.Content>
            <div className={styles.documentDetails}>
              <Popover.Title asChild>
                <h4 className={styles.documentDetailsHeader}>
                  {item.folderOrFile.toLowerCase() === "folder"
                    ? t("folder-details")
                    : t("file-details")}
                </h4>
              </Popover.Title>
              <Popover.Description>
                <div className={styles.documentDetailsBlock}>
                  <p className={styles.documentDetailsLabel}>
                    {t("shared-with")}
                  </p>
                  <div className={styles.sharedWithContent}>
                    <p className={styles.documentDetailsText}>
                      {item.restricted
                        ? t("restricted-to-select-users")
                        : t("shared-with-everyone-radio-btn-title")}
                    </p>
                    {item.restricted && usersWithAccess.length > 0 && (
                      <div className={styles.userAvatarsContainer}>
                        <UserAvatars users={usersWithAccess} />
                      </div>
                    )}
                    {item.restricted && loadingUsers && (
                      <div className={styles.loadingContainer}>
                        <Spinner isLoading={true} size="small" />
                      </div>
                    )}
                  </div>
                  {/* Practitioner */}
                  {!showManageAccess && (
                    <p className={styles.manageAccessContainer}>
                      <button
                        className={styles.manageAccess}
                        onClick={() => {
                          setDetailsOpen(false);
                          onManageAccess();
                        }}
                      >
                        {t("manage-access")}
                      </button>
                    </p>
                  )}
                  {/* Clients */}
                  {showManageAccess &&
                    item.restricted &&
                    usersWithAccess.length > 0 && (
                      <p className={styles.manageAccessContainer}>
                        <button
                          className={styles.manageAccess}
                          onClick={() => {
                            setShowUsersModal(true);
                          }}
                        >
                          {t("view-all-users")}
                        </button>
                      </p>
                    )}
                </div>
              </Popover.Description>
              <div className={styles.documentDetailsBlock}>
                <p className={styles.documentDetailsLabel}>{t("location")}</p>
                <div className={styles.chipStyling}>
                  <Chip
                    text={getLastPathSegment(location)}
                    type={ChipType.LOCATION}
                    iconName={"folder-icon"}
                  />
                </div>
              </div>
              <div className={styles.documentDetailsBlock}>
                <p className={styles.documentDetailsLabel}>
                  {t("last-modified")}
                </p>
                <p className={styles.documentDetailsText}>
                  {getDocumentsDateLabel(item.modifiedAt, i18n.language)}
                  {item.modifiedAt && item.modifiedByName && " • "}
                  {item.modifiedByName}
                </p>
              </div>
            </div>
          </Popover.Content>
        </Popover.Positioner>
      </Portal>

      {/* Users Access Modal */}
      <Modal
        id="users-access-modal"
        title={t("users-have-access-modal-title", {
          count: usersWithAccess.length,
          type: item.folderOrFile
        })}
        isVisible={showUsersModal}
        hide={() => setShowUsersModal(false)}
        size={ModalSize.LARGE}
        includeHeaderBorder={true}
        allowOverflow={false}
      >
        <div className={styles.usersModalContent}>
          {usersWithAccess.map(user => (
            <div key={user.id} className={styles.userRow}>
              <div className={styles.userInfo}>
                <CustomAvatar
                  avatarSize="small"
                  fontSize="small"
                  type="monogram"
                  initials={getInitials(user.displayName).charAt(0)}
                />
                <span className={styles.userName}>{user.displayName}</span>
              </div>
              <span className={styles.userOrganization}>
                {user.email.includes("@bdo.") ? "BDO" : "Other"}
              </span>
            </div>
          ))}
        </div>
      </Modal>
    </Popover.Root>
  );
};