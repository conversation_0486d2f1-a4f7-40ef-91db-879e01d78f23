import { defaultProps } from "~/default.spec";
export declare type ToastTypes = "error" | "success" | "info";
export interface ToastProps extends defaultProps {
    title?: string;
    message?: string;
    type: ToastTypes;
    isVisible: boolean;
    persist: boolean;
    duration?: number;
    onClose: () => void;
    withCTA?: boolean;
    onCTAClick?: () => void;
    ctaText?: string;
    errorMessageWithLineBreaks?: boolean;
    replaceLineBreaksWithBulletPoints?: boolean;
}
