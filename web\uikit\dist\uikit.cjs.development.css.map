{"version": 3, "sources": ["attachmentHistory.module.css", "icon.module.css", "button.module.css", "spinner.module.css", "chip.module.css", "avatar.module.css", "accordion.module.css", "assigneeTooltip.module.css", "input.module.css", "autocomplete.module.css", "avatarCard.module.css", "buttonDropdown.module.css", "taskType.module.css", "selector.module.css", "cardSelector.module.css", "carousel.module.css", "checkbox.module.css", "checkboxCard.module.css", "chipDropdown.module.css", "checkboxDropdownItem.module.css", "radioDropdownItem.module.css", "circlebutton.module.css", "tooltip.module.css", "fonts.module.css", "theme.module.css", "copyclipboard.module.css", "datepicker.module.css", "documentUpload.module.css", "treeView.module.css", "documentsNav.module.css", "drawer.module.css", "dropdownMenu.module.css", "dropdownInput.module.css", "textButton.module.css", "editProjectStages.module.css", "toggle.module.css", "inlineMessage.module.css", "inviteUserCard.module.css", "menuitem.module.css", "modal.module.css", "notificationTypeBadge.module.css", "progressBar.module.css", "progressCompletion.module.css", "sectionBanner.module.css", "selectionCard.module.css", "searchDropdown.module.css", "statusBadge.module.css", "statusChip.module.css", "tab.module.css", "table.module.css", "tabs.module.css", "taskTypeDropdown.module.css", "textarea.module.css", "textCarousel.module.css", "toast.module.css", "toggleListItem.module.css", "userSelect.module.css", "toolbar.module.css", "wysywygeditor.module.css", "editorTheme.css", "mentionmenu.module.css", "comments.module.css", "projectTimeline.module.css"], "names": [], "mappings": "AAAA;EACE,uCAAuC;EACvC,oDAAoD;EACpD,aAAa;EACb,kBAAkB;;EAElB;IACE,oCAAoC;IACpC,mCAAmC;EACrC;;EAEA;IACE,kBAAkB;IAClB,eAAe;IACf,4CAA4C;IAC5C,kBAAkB;;IAElB;MACE;QACE,gCAAgC;MAClC;IACF;EACF;;EAEA;IACE,gBAAgB;IAChB,UAAU;;IAEV;;;MAGE,aAAa;MACb,mBAAmB;MACnB,mBAAmB;IACrB;;IAEA;MACE,wDAAwD;MACxD,2BAA2B;MAC3B,4BAA4B;IAC9B;;IAEA;MACE,8BAA8B;MAC9B,+BAA+B;IACjC;;IAEA;MACE,mBAAmB;MACnB,yDAAyD;MACzD,0DAA0D;MAC1D,2DAA2D;;MAE3D,mBAAmB;MACnB,aAAa;MACb,+BAA+B;MAC/B,qBAAqB;;MAErB;QACE,YAAY;QACZ,YAAY;QACZ,aAAa;;QAEb;UACE,+BAA+B;UAC/B,gBAAgB;QAClB;;QAEA;UACE,qCAAqC;QACvC;MACF;;MAEA;QACE,wCAAwC;QACxC,WAAW;;QAEX;UACE,eAAe;UACf,oCAAoC;QACtC;MACF;;MAEA;QACE;UACE,4BAA4B;QAC9B;MACF;;MAEA;QACE;UACE,oCAAoC;QACtC;MACF;;MAEA;QACE,oCAAoC;QACpC,+BAA+B;QAC/B,mCAAmC;QACnC,gBAAgB;QAChB,uBAAuB;QACvB,mBAAmB;MACrB;IACF;EACF;AACF;;AAEA;EACE;IACE,gBAAgB;EAClB;AACF;;AAEA;EACE,qCAAqC;EACrC,uCAAuC;EACvC,wCAAwC;EACxC,iBAAiB;EACjB,mBAAmB;AACrB;;AAEA;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,gBAAgB;AAClB;;AAEA;EACE,eAAe;EACf,kBAAkB;;EAElB;IACE,4CAA4C;EAC9C;;EAEA;IACE,gDAAgD;IAChD,mBAAmB;EACrB;AACF;;AAEA;EACE,oBAAoB;EACpB,kBAAkB;EAClB,yBAAyB;EACzB,+BAA+B;EAC/B,mCAAmC;EACnC,6CAA6C;EAC7C,kBAAkB;EAClB,gBAAgB;AAClB;;ACtJA;EACE,aAAa;EACb,mBAAmB;AACrB;ACHA;EACE,kBAAkB;EAClB,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,uBAAuB;EACvB,kBAAkB;EAClB,gBAAgB;EAChB,mCAAmC;EACnC,WAAW;EACX,+BAA+B;EAC/B,eAAe;EACf,6CAA6C;EAC7C,iCAAiC;AACnC;;AAEA;EACE,mBAAmB;EACnB,gDAAgD;AAClD;;AAEA;EACE,sBAAsB;EACtB,YAAY;AACd;;AAEA;EACE,oBAAoB;EACpB,YAAY;AACd;;AAEA;EACE,YAAY;EACZ,YAAY;EACZ,gBAAgB;AAClB;;AAEA;;EAEE,mBAAmB;EACnB,oDAAoD;EACpD,wCAAwC;EACxC;IACE,uCAAuC;EACzC;AACF;;AAEA;EACE,YAAY;EACZ,4BAA4B;EAC5B,0CAA0C;AAC5C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,+CAA+C;EAC/C,uCAAuC;EACvC,oCAAoC;EACpC;IACE,mCAAmC;EACrC;AACF;;AAEA;EACE,+CAA+C;EAC/C,4BAA4B;EAC5B;IACE,2BAA2B;EAC7B;AACF;;AAEA;;EAEE,mBAAmB;EACnB,oDAAoD;EACpD,wCAAwC;EACxC,6BAA6B;EAC7B;IACE,uCAAuC;EACzC;AACF;;AAEA;EACE,YAAY;EACZ,oCAAoC;EACpC,6BAA6B;EAC7B;IACE,mCAAmC;EACrC;;EAEA;IACE,0BAA0B;EAC5B;AACF;;AAEA;;EAEE,mBAAmB;EACnB,6CAA6C;EAC7C;IACE,4CAA4C;EAC9C;AACF;;AAEA;EACE,sCAAsC;EACtC;IACE;MACE,qCAAqC;IACvC;EACF;AACF;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;EACE,+CAA+C;AACjD;AACA;EACE,YAAY;AACd;;AAEA;EACE,uDAAuD;EACvD,qDAAqD;AACvD;;AAEA;EACE,aAAa;EACb,eAAe;AACjB;;AAEA;EACE,UAAU;AACZ;;ACrJA;EACE,0CAA0C;EAC1C,2CAA2C;EAC3C,kBAAkB;EAClB,qBAAqB;EACrB,sBAAsB;EACtB,8DAAsC;AACxC;;AAEA;EACE,sCAAsC;EACtC,4CAA4C;AAC9C;;AAEA;EACE,sCAAsC;EACtC,4CAA4C;AAC9C;;AAEA;EACE,sCAAsC;EACtC,oDAAoD;AACtD;;AAEA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AAEA;EACE,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,YAAY;AACd;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,mBAAmB;EACnB,+BAA+B;AACjC;;AAEA;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,gCAAgC;EAChC,+BAA+B;AACjC;;ACzDA;EACE,aAAa;EACb,oCAAoC;EACpC,mBAAmB;EACnB,uBAAuB;EACvB,kBAAkB;EAClB,0CAA0C;EAC1C,sBAAsB;AACxB;;AAEA;;EAEE,uCAAuC;EACvC,4CAA4C;EAC5C,YAAY;AACd;;AAEA;EACE,yBAAyB;EACzB,kBAAkB;EAClB,0CAA0C;EAC1C,YAAY;EACZ,kBAAkB;EAClB,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;EACvB,kBAAkB;EAClB,6CAA6C;AAC/C;;AAEA;EACE,YAAY;EACZ,gBAAgB;EAChB,wDAAwD;EACxD,kBAAkB;AACpB;;AAEA;EACE,+BAA+B;EAC/B,sBAAsB;EACtB,YAAY;EACZ,oBAAoB;EACpB,4CAA4C;EAC5C,iCAAiC;EACjC,8BAA8B;EAC9B,kBAAkB;EAClB,yCAAyC;AAC3C;;AAEA;EACE,+BAA+B;EAC/B,sBAAsB;EACtB,kBAAkB;EAClB,YAAY;EACZ,oBAAoB;EACpB,wBAAwB;EACxB,4CAA4C;EAC5C,iCAAiC;EACjC,8BAA8B;AAChC;;AAEA;EACE,+BAA+B;EAC/B,sBAAsB;EACtB,kBAAkB;EAClB,YAAY;EACZ,oBAAoB;EACpB,wBAAwB;EACxB,uBAAuB;EACvB,iCAAiC;EACjC,8BAA8B;EAC9B,yBAAyB;AAC3B;;AAEA;EACE,mBAAmB;EACnB,sBAAsB;EACtB,kBAAkB;EAClB,YAAY;EACZ,kBAAkB;EAClB,wBAAwB;EACxB,yBAAyB;EACzB,+CAA+C;EAC/C,8BAA8B;AAChC;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,WAAW;EACX,uCAAuC;EACvC,sBAAsB;EACtB,wBAAwB;EACxB,qCAAqC;EACrC,YAAY;AACd;;AAEA;EACE,2BAA2B;EAC3B,uBAAuB;AACzB;;AAEA;EACE;IACE;MACE,uDAAuD;MACvD,eAAe;IACjB;EACF;AACF;;AAEA;EACE,UAAU;;EAEV;IACE,aAAa;;IAEb;MACE,gDAAgD;MAChD,oBAAoB;IACtB;EACF;AACF;;AAEA,2DAA2D;AAC3D;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,qCAAqC;EACrC,yCAAyC;EACzC,aAAa;EACb,cAAc;EACd,oBAAoB;EACpB,qBAAqB;AACvB;;AAEA;;EAEE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,qCAAqC;EACrC,yCAAyC;EACzC,4BAA4B;EAC5B,eAAe;EACf,kBAAkB;AACpB;;AAEA;;;CAGC;AACD;EACE,cAAc;EACd;IACE;MACE,WAAW;IACb;EACF;AACF;;AAEA,0CAA0C;AAC1C;;;;;;;GAOG;;AAEH;EACE,qBAAqB;EACrB;IACE;MACE,8BAA8B;IAChC;EACF;AACF;;AAEA;EACE,8CAA8C;EAC9C,oBAAoB;AACtB;;AAEA;EACE,6CAA6C;EAC7C,oBAAoB;AACtB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,qCAAqC;EACrC,yCAAyC;EACzC,4BAA4B;EAC5B,8CAA8C;EAC9C,aAAa;EACb,cAAc;EACd,oBAAoB;EACpB,qBAAqB;EACrB,6CAA6C;AAC/C;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,sBAAsB;EACtB,kBAAkB;EAClB,iBAAiB;EACjB,mBAAmB;EACnB,uCAAuC;AACzC;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,gBAAgB;EAChB,0CAA0C;AAC5C;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,gBAAgB;EAChB,oCAAoC;EACpC,uCAAuC;AACzC;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,gBAAgB;EAChB,6CAA6C;EAC7C,uCAAuC;AACzC;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,gBAAgB;EAChB,0CAA0C;EAC1C,uCAAuC;AACzC;;AAEA;EACE,uCAAuC;EACvC,0CAA0C;EAC1C,qCAAqC;EACrC,yCAAyC;EACzC,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;;EAEE,6CAA6C;EAC7C,0CAA0C;EAC1C,qCAAqC;EACrC,yCAAyC;AAC3C;;AAEA;EACE,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;EACE,6CAA6C;EAC7C,0CAA0C;EAC1C,eAAe;EACf,gBAAgB;EAChB,iBAAiB;EACjB,kBAAkB;EAClB,cAAc;AAChB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,YAAY;EACZ,gBAAgB;EAChB,UAAU;EACV,UAAU;EACV,eAAe;EACf,iCAAiC;EACjC,gBAAgB;EAChB,kBAAkB;EAClB,iCAAiC;;EAEjC;IACE,4CAA4C;EAC9C;AACF;;ACnTA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,cAAc;AAChB;;AAEA;EACE,cAAc;EACd,aAAa;EACb,sCAAsC;AACxC;;AAEA;EACE,YAAY;EACZ,WAAW;EACX,sCAAsC;AACxC;;AAEA;EACE,cAAc;EACd,aAAa;EACb,qCAAqC;AACvC;;AAEA;EACE,cAAc;EACd,aAAa;EACb,qCAAqC;AACvC;;AAEA;EACE,YAAY;EACZ,WAAW;EACX,qCAAqC;AACvC;;AAEA;EACE,cAAc;EACd,aAAa;EACb,gBAAgB;EAChB,6BAA6B;AAC/B;;AAEA;EACE,gBAAgB;EAChB,eAAe;EACf,gBAAgB;EAChB,6BAA6B;AAC/B;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,uDAAuD;EACvD,oCAAoC;EACpC,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,qCAAqC;EACrC,gBAAgB;AAClB;;AAEA;EACE,sCAAsC;EACtC,WAAW;EACX,YAAY;AACd;;AAEA;EACE,kBAAkB;EAClB,wBAAwB;EACxB,yBAAyB;AAC3B;;AAEA;EACE,eAAe;EACf,WAAW;EACX,YAAY;AACd;;AAEA;EACE,cAAc;EACd,aAAa;AACf;;AAEA;EACE,kBAAkB;EAClB,cAAc;EACd,aAAa;AACf;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,SAAS;EACT,iBAAiB;AACnB;;AAEA;EACE,sCAAsC;EACtC,gBAAgB;AAClB;;AAEA;EACE,qCAAqC;EACrC,gBAAgB;AAClB;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE;IACE,WAAW;IACX,YAAY;EACd;;EAEA;IACE,aAAa;IACb,cAAc;EAChB;;EAEA;IACE,WAAW;IACX,YAAY;EACd;;EAEA;IACE,kBAAkB;IAClB,cAAc;IACd,aAAa;EACf;;EAEA;IACE,cAAc;IACd,aAAa;EACf;AACF;;ACtJA;EACE,WAAW;EACX,gBAAgB;EAChB,aAAa;EACb,sBAAsB;;EAEtB;IACE,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,aAAa;IACb,SAAS;IACT,wDAAwD;IACxD,2DAA2D;IAC3D,eAAe;IACf,gBAAgB;;IAEhB;MACE,qCAAqC;MACrC,gBAAgB;MAChB,mCAAmC;MACnC,oCAAoC;IACtC;;IAEA;MACE,oCAAoC;MACpC,mBAAmB;IACrB;EACF;;EAEA;IACE,yBAAyB;EAC3B;;EAEA;IACE;MACE;QACE,gCAAgC;MAClC;IACF;EACF;;EAEA;IACE,wBAAwB;IACxB,uCAAuC;EACzC;;EAEA;IACE,uCAAuC;EACzC;;EAEA;IACE,uCAAuC;IACvC,wDAAwD;IACxD,2DAA2D;IAC3D,gBAAgB;IAChB,4BAA4B;EAC9B;;EAEA;IACE,UAAU;IACV,aAAa;IACb,mBAAmB;IACnB,iCAAiC;IACjC,sBAAsB;IACtB,uBAAuB;IACvB,uBAAuB;IACvB,SAAS;IACT,mBAAmB;EACrB;;EAEA;IACE,UAAU;IACV,SAAS;IACT,sBAAsB;IACtB,kBAAkB;IAClB,iCAAiC;EACnC;AACF;;AC9EA;EACE,+CAA+C;EAC/C,yCAAyC;EACzC,kBAAkB;EAClB,+BAA+B;EAC/B,6CAA6C;EAC7C,yCAAyC;EACzC,aAAa;EACb,gBAAgB;EAChB,+BAA+B;EAC/B,qBAAqB;AACvB;;AAEA;EACE,gBAAgB;EAChB,qCAAqC;AACvC;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,iBAAiB;EACjB,iDAAiD;AACnD;;ACzBA;EACE,WAAW;EACX,kBAAkB;EAClB,mBAAmB;AACrB;;AAEA;EACE,uCAAuC;EACvC,WAAW;EACX,aAAa;EACb,+CAA+C;EAC/C,kBAAkB;EAClB,oCAAoC;EACpC,+BAA+B;EAC/B,oBAAoB;EACpB,mCAAmC;EACnC,YAAY;EACZ,sBAAsB;EACtB,4EAA4E;;EAE5E;IACE,oDAAoD;EACtD;AACF;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,YAAY;EACZ,eAAe;AACjB;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,4CAA4C;EAC5C,2CAA2C;EAC3C,wCAAwC;EACxC,mBAAmB;AACrB;;AAEA;EACE,aAAa;EACb,oBAAoB;AACtB;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,aAAa;EACb,+CAA+C;;EAE/C;IACE,+CAA+C;IAC/C,gDAAgD;IAChD,mBAAmB;EACrB;AACF;;AAEA;;EAEE,kBAAkB;EAClB,SAAS;EACT,UAAU;EACV,+BAA+B;EAC/B,mCAAmC;EACnC,wCAAwC;EACxC,oBAAoB;EACpB,UAAU;AACZ;;AAEA;EACE,4BAA4B;AAC9B;;AAEA;EACE,mBAAmB;EACnB,UAAU;EACV,6BAA6B;EAC7B,iCAAiC;AACnC;;AAEA;EACE,QAAQ;EACR,sCAAsC;EACtC,0CAA0C;EAC1C,0CAA0C;AAC5C;;AAEA;;EAEE,QAAQ;EACR,sCAAsC;EACtC,0CAA0C;AAC5C;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,0CAA0C;AAC5C;;AAEA;EACE,aAAa;EACb,WAAW;EACX,sCAAsC;EACtC,qCAAqC;EACrC,yCAAyC;EACzC,mBAAmB;AACrB;;AAEA;EACE,sCAAsC;EACtC,qCAAqC;EACrC,yCAAyC;AAC3C;;AAEA;EACE,SAAS;EACT,cAAc;EACd,gBAAgB;AAClB;;AAEA;EACE,kBAAkB;EAClB,iCAAiC;EACjC,SAAS;EACT,UAAU;EACV,6BAA6B;AAC/B;;AAEA;EACE,+BAA+B;EAC/B,+BAA+B;AACjC;;AAEA;EACE,kBAAkB;EAClB,WAAW;EACX,SAAS;;EAET;IACE;MACE,mCAAmC;IACrC;EACF;AACF;;AAEA;EACE;IACE,gBAAgB;EAClB;AACF;;AChKA;EACE,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,WAAW;EACX,kBAAkB;AACpB;;AAEA;EACE,uCAAuC;EACvC,WAAW;EACX,aAAa;EACb,oBAAoB;EACpB,0CAA0C;EAC1C,kBAAkB;EAClB,oCAAoC;EACpC,+BAA+B;EAC/B,mCAAmC;EACnC,YAAY;EACZ,sBAAsB;EACtB,yCAAyC;AAC3C;;AAEA;EACE,aAAa;EACb,+CAA+C;AACjD;;AAEA;EACE,kBAAkB;EAClB,QAAQ;EACR,UAAU;EACV,+BAA+B;EAC/B,mCAAmC;EACnC,wCAAwC;EACxC,2BAA2B;EAC3B,oBAAoB;EACpB,4BAA4B;EAC5B,UAAU;AACZ;;AAEA;EACE,SAAS;EACT,sCAAsC;EACtC,0CAA0C;EAC1C,oCAAoC;AACtC;;AAEA;;EAEE,SAAS;EACT,sCAAsC;EACtC,0CAA0C;EAC1C,oCAAoC;AACtC;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,WAAW;EACX,WAAW;EACX,eAAe;EACf,aAAa;EACb,uCAAuC;EACvC,0CAA0C;EAC1C,kBAAkB;EAClB,YAAY;EACZ,sBAAsB;AACxB;;AAEA;EACE,aAAa;EACb,kBAAkB;EAClB,WAAW;EACX,QAAQ;EACR,2BAA2B;AAC7B;;AC/EA;EACE,mBAAmB;EACnB,aAAa;EACb,uCAAuC;EACvC,kBAAkB;EAClB,SAAS;EACT,WAAW;EACX,gBAAgB;EAChB,iBAAiB;EACjB,mCAAmC;AACrC;;AAEA;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,gBAAgB;EAChB,SAAS;EACT,qBAAqB;AACvB;;AAEA;EACE,SAAS;EACT,qCAAqC;EACrC,yCAAyC;AAC3C;;AAEA;EACE,mBAAmB;EACnB,uBAAuB;EACvB,aAAa;EACb,WAAW;AACb;;AAEA;EACE;IACE,eAAe;IACf,sCAAsC;IACtC,iBAAiB;EACnB;;EAEA;IACE,qBAAqB;EACvB;AACF;;AC3CA;EACE,kBAAkB;EAClB,oCAAoC;;EAEpC;IACE,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,kBAAkB;IAClB,oBAAoB;IACpB,oCAAoC;IACpC,mCAAmC;IACnC,+CAA+C;IAC/C,gBAAgB;IAChB,oDAAoD;IACpD,iCAAiC;IACjC,aAAa;;IAEb;MACE;QACE,mCAAmC;MACrC;IACF;EACF;;EAEA;IACE,4BAA4B;IAC5B,+CAA+C;IAC/C,eAAe;;IAEf;MACE;QACE,2BAA2B;MAC7B;IACF;EACF;;EAEA;IACE,SAAS;IACT,+CAA+C;IAC/C,gDAAgD;IAChD,mBAAmB;EACrB;;EAEA;IACE,yBAAyB;IACzB,QAAQ;IACR,SAAS;IACT,YAAY;IACZ,UAAU;EACZ;;EAEA;IACE,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB;IACtB,aAAa;IACb,SAAS;IACT,oCAAoC;IACpC,eAAe;IACf,yCAAyC;IACzC,sBAAsB;IACtB,YAAY;IACZ,WAAW;;IAEX;MACE;QACE,2BAA2B;MAC7B;IACF;EACF;;EAEA;IACE;MACE;QACE,8CAA8C;MAChD;IACF;EACF;;EAEA;IACE,oDAAoD;EACtD;AACF;;ACpFA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,kBAAkB;EAClB,iCAAiC;EACjC,cAAc;EACd,kBAAkB;EAClB,eAAe;AACjB;;AAEA;EACE,iDAAiD;AACnD;;AAEA;EACE,gDAAgD;AAClD;;AAEA;EACE,8CAA8C;AAChD;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,6CAA6C;AAC/C;;AC7BA;EACE,aAAa;EACb,sBAAsB;AACxB;;AAEA;;EAEE,uCAAuC;EACvC,WAAW;AACb;;AAEA;EACE,0CAA0C;EAC1C,kBAAkB;EAClB,aAAa;EACb,iBAAiB;AACnB;;AAEA;EACE,sBAAsB;EACtB,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE;IACE,gDAAgD;IAChD,mBAAmB;EACrB;AACF;AACA;EACE,kDAAkD;AACpD;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,uBAAuB;AACzB;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,aAAa;EACb,uBAAuB;EACvB,mBAAmB;EACnB,SAAS;EACT,YAAY;EACZ,eAAe;;EAEf;IACE;MACE,2CAA2C;IAC7C;EACF;AACF;;AAEA;EACE,aAAa;EACb,aAAa;EACb,uBAAuB;AACzB;;AAEA;;EAEE,aAAa;EACb,WAAW;EACX,YAAY;EACZ,OAAO;EACP,oCAAoC;AACtC;;AAEA;EACE,sBAAsB;EACtB,uBAAuB;AACzB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,SAAS;EACT,+BAA+B;EAC/B,yCAAyC;AAC3C;;AAEA;EACE,SAAS;EACT,qCAAqC;EACrC,yCAAyC;EACzC,gBAAgB;EAChB,0CAA0C;AAC5C;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,aAAa;EACb,0CAA0C;EAC1C,kBAAkB;EAClB,kBAAkB;EAClB;qCACmC;;EAEnC;IACE;MACE;QACE,WAAW;QACX,gDAAgD;QAChD,kBAAkB;QAClB,OAAO;QACP,QAAQ;QACR,SAAS;QACT,MAAM;QACN,kBAAkB;QAClB,mBAAmB;MACrB;IACF;EACF;AACF;;AAEA;EACE,eAAe;EACf,cAAc;EACd,mBAAmB;EACnB,kBAAkB;EAClB,gDAAgD;EAChD,kDAAkD;EAClD,mDAAmD;EACnD,oBAAoB;;EAEpB;IACE;MACE,sBAAsB;IACxB;EACF;AACF;;AAEA;EACE;IACE,4BAA4B;IAC5B,2BAA2B;EAC7B;AACF;;ACzJA;;EAEE,gBAAgB;EAChB,aAAa;EACb,eAAe;EACf,WAAW;AACb;;AAEA;EACE,YAAY;AACd;;ACVA;EACE,aAAa;EACb,sBAAsB;EACtB,yBAAyB;EACzB,YAAY;EACZ,WAAW;EACX,kBAAkB;EAClB,WAAW;AACb;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,iBAAiB;EACjB,mBAAmB;EACnB,8BAA8B;EAC9B,iCAAiC;EACjC,kBAAkB;EAClB,UAAU;EACV,iEAAiE;EACjE,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA;EACE,UAAU;EACV,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,yBAAyB;EACzB,mBAAmB;EACnB,mBAAmB;EACnB,kBAAkB;EAClB,UAAU;EACV,eAAe;EACf,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,SAAS;EACT,gBAAgB;AAClB;;AAEA;EACE,6BAA6B;EAC7B,YAAY;EACZ,eAAe;EACf,aAAa;EACb,aAAa;EACb,4BAA4B;AAC9B;;AAEA;EACE,kBAAkB;EAClB,kDAAkD;AACpD;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;EAClB,kDAAkD;AACpD;;AAEA;EACE,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,uBAAuB;EACvB,6BAA6B;EAC7B,UAAU;AACZ;;AAEA;EACE,6BAA6B;EAC7B,aAAa;EACb,YAAY;EACZ,eAAe;EACf,aAAa;AACf;;AAEA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,gBAAgB;AAClB;;AAEA;;;;EAIE,kDAAkD;EAClD,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,YAAY;EACZ,sCAAsC;AACxC;;AAEA;EACE,cAAc;EACd,kBAAkB;EAClB,UAAU;EACV,kEAAkE;AACpE;;AAEA;EACE,UAAU;EACV,eAAe;AACjB;;AAEA;EACE,UAAU;EACV,4BAA4B;EAC5B,qCAAqC;EACrC,yCAAyC;EACzC,cAAc;EACd,wBAAwB;AAC1B;;AAEA;EACE;IACE,iBAAiB;EACnB;;EAEA;IACE,mBAAmB;IACnB,aAAa;EACf;;EAEA;IACE,aAAa;EACf;AACF;;ACxJA;EACE,kBAAkB;EAClB,aAAa;EACb,mBAAmB;EACnB,YAAY;EACZ,mBAAmB;EACnB,uBAAuB;;EAEvB;IACE,aAAa;;IAEb;MACE;QACE,gDAAgD;QAChD,oBAAoB;QACpB,kBAAkB;MACpB;IACF;EACF;AACF;;AAEA;EACE,kBAAkB;EAClB,eAAe;EACf,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,cAAc;AAChB;;AAEA;EACE,cAAc;EACd,aAAa;AACf;;AAEA;EACE,eAAe;EACf,cAAc;AAChB;;AAEA;EACE,2DAA2D;AAC7D;;AAEA;EACE,uCAAuC;EACvC,4CAA4C;EAC5C,SAAS;EACT,yCAAyC;;EAEzC;IACE,aAAa;EACf;AACF;;AAEA;EACE,iDAAiD;AACnD;;AAEA;EACE,+CAA+C;EAC/C,iDAAiD;EACjD,yCAAyC;AAC3C;;AAEA;EACE,4CAA4C;EAC5C,qDAAqD;AACvD;;AAEA;;EAEE,uDAAuD;AACzD;;AAEA;EACE,4DAA4D;AAC9D;;AAEA;EACE,YAAY;EACZ,WAAW;EACX,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,eAAe;AACjB;;AAEA;EACE,gBAAgB;EAChB,+BAA+B;EAC/B,mCAAmC;EACnC,oCAAoC;AACtC;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE;IACE,kCAAkC;EACpC;AACF;;ACvGA;EACE,kBAAkB;EAClB,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,8BAA8B;EAC9B,uCAAuC;EACvC,kBAAkB;EAClB,0CAA0C;EAC1C,aAAa;EACb,WAAW;EACX,oCAAoC;;EAEpC;IACE,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,kBAAkB;;IAElB;MACE,kBAAkB;IACpB;EACF;;EAEA;IACE,aAAa;EACf;;EAEA;IACE,4CAA4C;EAC9C;;EAEA;IACE,aAAa;EACf;;EAEA;IACE,gDAAgD;EAClD;AACF;;AAEA;EACE,WAAW;EACX,cAAc;EACd,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,YAAY;EACZ,WAAW;EACX,iDAAiD;AACnD;;AAEA;EACE,8CAA8C;AAChD;;AAEA;;;EAGE,+CAA+C;AACjD;;AAEA;EACE;IACE,iBAAiB;EACnB;AACF;;AAEA;EACE,0CAA0C;EAC1C,sCAAsC;EACtC,wCAAwC;;EAExC;IACE,mBAAmB;EACrB;;EAEA;IACE;MACE;QACE,uCAAuC;MACzC;IACF;EACF;AACF;;ACpFA;EACE,kBAAkB;EAClB,qCAAqC;;EAErC;IACE,aAAa;IACb,mBAAmB;IACnB,oBAAoB;IACpB,mBAAmB;IACnB,WAAW;IACX,sBAAsB;IACtB,oDAAoD;IACpD,iCAAiC;IACjC,eAAe;IACf,+BAA+B;IAC/B,0CAA0C;;IAE1C;MACE,qCAAqC;MACrC,yCAAyC;MACzC,aAAa;MACb,WAAW;MACX,mBAAmB;IACrB;;IAEA;MACE,UAAU;MACV,YAAY;MACZ,oDAAoD;IACtD;;IAEA;;MAEE,YAAY;IACd;;IAEA;MACE;QACE;UACE,gCAAgC;QAClC;MACF;IACF;;IAEA;MACE;QACE;UACE;YACE,gCAAgC;UAClC;QACF;MACF;IACF;;IAEA;MACE;QACE,yBAAyB;QACzB;UACE;YACE,mCAAmC;UACrC;QACF;MACF;IACF;EACF;;EAEA;IACE,+CAA+C;IAC/C,+BAA+B;IAC/B,aAAa;EACf;;EAEA;IACE,gDAAgD;IAChD,mBAAmB;EACrB;;EAEA;IACE,aAAa;IACb,2BAA2B;IAC3B,sBAAsB;IACtB,uBAAuB;IACvB,WAAW;IACX,mBAAmB;IACnB,kBAAkB;EACpB;;EAEA;IACE,aAAa;IACb,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,YAAY;IACZ,mBAAmB;IACnB,oDAAoD;IACpD,oBAAoB;IACpB,iCAAiC;IACjC,mBAAmB;IACnB,kBAAkB;;IAElB;MACE,2CAA2C;IAC7C;;IAEA;MACE,aAAa;MACb,+CAA+C;;MAE/C;QACE,+CAA+C;MACjD;IACF;EACF;;EAEA;IACE,kBAAkB;IAClB,YAAY;IACZ,QAAQ;IACR,2BAA2B;IAC3B,YAAY;IACZ,iCAAiC;IACjC,oBAAoB;EACtB;;EAEA;IACE,wCAAwC;IACxC,iCAAiC;EACnC;;EAEA;IACE,YAAY;IACZ,+CAA+C;IAC/C,kBAAkB;IAClB,kBAAkB;IAClB,sBAAsB;IACtB,uBAAuB;IACvB,uBAAuB;IACvB,uCAAuC;IACvC,UAAU;IACV,iBAAiB;IACjB,gBAAgB;EAClB;;EAEA;IACE,wDAAwD;IACxD,WAAW;IACX,aAAa;IACb,wCAAwC;IACxC,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,gBAAgB;IAChB,SAAS;IACT,uCAAuC;;IAEvC;MACE,oBAAoB;IACtB;EACF;AACF;;AAEA;EACE,yBAAyB;EACzB,2CAA2C;AAC7C;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,YAAY;EACZ,kBAAkB;EAClB,eAAe;;EAEf;IACE,WAAW;IACX,2BAA2B;IAC3B,eAAe;EACjB;AACF;;AClLA;EACE,aAAa;EACb,qBAAqB;EACrB,YAAY;EACZ,mBAAmB;EACnB,mBAAmB;EACnB,SAAS;EACT,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA;EACE,gBAAgB;EAChB,mBAAmB;EACnB,uBAAuB;AACzB;;ACtBA;EACE,aAAa;EACb,qBAAqB;EACrB,YAAY;EACZ,mBAAmB;EACnB,mBAAmB;EACnB,SAAS;AACX;ACPA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,aAAa;EACb,cAAc;EACd,uCAAuC;EACvC,UAAU;EACV,kBAAkB;EAClB,eAAe;EACf,6CAA6C;AAC/C;;AAEA;EACE,WAAW;EACX,YAAY;AACd;;AAEA;EACE,0CAA0C;AAC5C;;AAEA;;EAEE,4CAA4C;AAC9C;;AAEA;EACE,0CAA0C;;EAE1C;IACE,4BAA4B;EAC9B;AACF;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,uCAAuC;AACzC;;ACzCA;EACE,+CAA+C;EAC/C,yCAAyC;EACzC,kBAAkB;EAClB,+BAA+B;EAC/B,6CAA6C;EAC7C,yCAAyC;EACzC,aAAa;EACb,gBAAgB;EAChB,+BAA+B;EAC/B,qBAAqB;AACvB;;AAEA;EACE,gBAAgB;EAChB,qCAAqC;AACvC;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,iBAAiB;EACjB,iDAAiD;AACnD;;AAEA;EACE,oBAAoB;AACtB;;AC7BA;EACE,0BAA0B;EAC1B;iEAC+D;EAC/D,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B;mEACiE;EACjE,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B;8DAC4D;EAC5D,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B;kEACgE;EAChE,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B;+DAC6D;EAC7D,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B;8DAC4D;EAC5D,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B;gEAC8D;EAC9D,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B;gEAC8D;EAC9D,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;EAC1B;mEACiE;EACjE,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AC/EA;EACE,sBAAsB;EACtB,sBAAsB;EACtB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,sBAAsB;EACtB,uBAAuB;;EAEvB,oBAAoB;EACpB,qBAAqB;EACrB,+BAA+B;EAC/B,+BAA+B;EAC/B,8BAA8B;EAC9B,8BAA8B;EAC9B,iCAAiC;EACjC,wBAAwB;;EAExB,wBAAwB;EACxB,qBAAqB;EACrB,uBAAuB;EACvB,qBAAqB;EACrB,qBAAqB;EACrB,uBAAuB;EACvB,uBAAuB;;EAEvB,sBAAsB;EACtB,yBAAyB;EACzB,gCAAgC;EAChC,8BAA8B;EAC9B,6BAA6B;EAC7B,6BAA6B;EAC7B,mCAAmC;EACnC,4BAA4B;;EAE5B,mBAAmB;EACnB,4BAA4B;EAC5B,0CAA0C;EAC1C,uCAAuC;EACvC,iCAAiC;EACjC,sCAAsC;EACtC,uCAAuC;EACvC,8BAA8B;EAC9B,8BAA8B;EAC9B,0CAA0C;EAC1C,sCAAsC;;EAEtC,qBAAqB;EACrB,mCAAmC;EACnC,iCAAiC;EACjC,+CAA+C;EAC/C,kCAAkC;EAClC,gCAAgC;EAChC,+BAA+B;EAC/B,6CAA6C;EAC7C,+BAA+B;;EAE/B,iBAAiB;EACjB,0CAA0C;EAC1C,uCAAuC;EACvC,2CAA2C;;EAE3C,sBAAsB;EACtB,yBAAyB;EACzB,0DAA0D;EAC1D,oEAAoE;EACpE,0DAA0D;EAC1D,wBAAwB;EACxB,8BAA8B;EAC9B,8DAA8D;EAC9D,qCAAqC;EACrC,iCAAiC;EACjC,gCAAgC;EAChC,kCAAkC;EAClC,4CAA4C;EAC5C,kCAAkC;EAClC,gDAAgD;;EAEhD,kBAAkB;EAClB,4DAA4D;EAC5D,8DAA8D;EAC9D,kEAAkE;EAClE,yCAAyC;EACzC,qDAAqD;EACrD,2CAA2C;EAC3C,gCAAgC;EAChC,oCAAoC;EACpC,0BAA0B;EAC1B,4BAA4B;EAC5B,oBAAoB;EACpB,4BAA4B;EAC5B,qBAAqB;EACrB,6BAA6B;EAC7B,oBAAoB;EACpB,sBAAsB;EACtB,6BAA6B;EAC7B,2BAA2B;EAC3B,oBAAoB;EACpB,sBAAsB;;EAEtB,6BAA6B;EAC7B,uBAAuB,EAAE,mBAAmB;EAC5C,sBAAsB,EAAE,mCAAmC;EAC3D,qBAAqB,EAAE,0CAA0C;EACjE,sBAAsB,EAAE,6BAA6B;EACrD,sBAAsB,EAAE,4BAA4B;EACpD,uBAAuB,EAAE,6BAA6B;EACtD,wBAAwB,EAAE,wBAAwB;;EAElD,wBAAwB;EACxB,uDAAuD;;EAEvD,UAAU;EACV,4BAA4B;AAC9B;;AAEA;EACE;IACE,uBAAuB;IACvB,sBAAsB;IACtB,uBAAuB;IACvB,uBAAuB;IACvB,uBAAuB;IACvB,sBAAsB;;IAEtB,uBAAuB;IACvB,qBAAqB;IACrB,uBAAuB;IACvB,qBAAqB;IACrB,qBAAqB;IACrB,uBAAuB;;IAEvB,UAAU;IACV,6BAA6B;EAC/B;AACF;;AAEA;EACE,uCAAuC;AACzC;;AAEA;EACE,6BAA6B;EAC7B,iCAAiC;EACjC,gBAAgB;AAClB;;AAEA;EACE,6BAA6B;EAC7B,iCAAiC;EACjC,gBAAgB;AAClB;;AAEA;EACE,6BAA6B;EAC7B,iCAAiC;EACjC,gBAAgB;AAClB;;AAEA;EACE,6BAA6B;EAC7B,iCAAiC;EACjC,gBAAgB;AAClB;;AAEA;EACE,6BAA6B;EAC7B,iCAAiC;EACjC,gBAAgB;AAClB;;AAEA;EACE,6BAA6B;EAC7B,iCAAiC;EACjC,gBAAgB;AAClB;;AAEA;EACE,kCAAkC;EAClC,sCAAsC;AACxC;;ACpLA;EACE,aAAa;EACb,mBAAmB;EACnB,YAAY;AACd;;AAEA;EACE,SAAS;AACX;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,iCAAiC;EACjC,6BAA6B;AAC/B;;AAEA;EACE,4CAA4C;AAC9C;;ACxBA;EACE,aAAa;AACf;;AAEA;;;;EAIE,uCAAuC;AACzC;;AAEA;EACE,6CAA6C;EAC7C,qCAAqC;AACvC;;AAEA;EACE,iCAAiC;;EAEjC;IACE,oDAAoD;EACtD;AACF;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,eAAe;EACf,YAAY;EACZ,UAAU;AACZ;;AAEA,2FAA2F;AAC3F;EACE,aAAa;EACb,iBAAiB;AACnB;;AAEA,sDAAsD;AACtD;;;EAGE,oBAAoB;AACtB;;AAEA,wDAAwD;AACxD;;;EAGE,sBAAsB;AACxB;;AAEA;EACE,mCAAmC;AACrC;;AAEA;EACE,uCAAuC;EACvC,oCAAoC;EACpC,gBAAgB;EAChB,UAAU;EACV,YAAY;AACd;;AAEA;EACE,WAAW;EACX,uCAAuC;EACvC,6CAA6C;EAC7C,+BAA+B;EAC/B,mCAAmC;EACnC,oCAAoC;EACpC,oBAAoB;EACpB,kBAAkB;EAClB,+CAA+C;EAC/C,aAAa;;EAEb;IACE,oDAAoD;EACtD;AACF;;AAEA;EACE,aAAa;;EAEb;IACE,gDAAgD;IAChD,mBAAmB;EACrB;AACF;;AAEA;EACE,kDAAkD;AACpD;;AAEA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,uBAAuB;AACzB;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;;EAEE,kBAAkB;EAClB,YAAY;EACZ,oCAAoC;AACtC;;AAEA;EACE,UAAU;EACV,aAAa;EACb,YAAY;EACZ,qCAAqC;EACrC,yCAAyC;EACzC,kBAAkB;AACpB;;AAEA;EACE,eAAe;EACf,oCAAoC;EACpC,oDAAoD;AACtD;;AAEA;EACE,gBAAgB;EAChB,kBAAkB;EAClB,wCAAwC;EACxC,cAAc,EAAE,QAAQ;AAC1B;;AAEA;EACE,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,WAAW;EACX,SAAS;EACT,+BAA+B;EAC/B,UAAU;EACV,WAAW;EACX,0CAA0C;EAC1C,kBAAkB;AACpB;;AAEA;;EAEE,2BAA2B;EAC3B,oBAAoB;EACpB,mBAAmB;EACnB,YAAY;AACd;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,aAAa;EACb,uBAAuB;EACvB,mBAAmB;AACrB;;AAEA;EACE,6BAA6B;EAC7B,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;EAClB,qCAAqC;EACrC,mCAAmC;EACnC,wCAAwC;AAC1C;;AAEA;EACE,oBAAoB;EACpB,oDAAoD;EACpD,oBAAoB;EACpB,uCAAuC;EACvC,0CAA0C;EAC1C,eAAe;EACf,mBAAmB;AACrB;;AAEA;EACE,aAAa;EACb,WAAW;EACX,uBAAuB;EACvB,uBAAuB;EACvB,2DAA2D;AAC7D;;AAEA;;;;;;EAME,0CAA0C;EAC1C,4BAA4B;EAC5B,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA,uCAAuC;AACvC;;EAEE;;GAEC,EAAE,qBAAqB;EACxB,oCAAoC;EACpC,gBAAgB;AAClB;;AAEA;;;;;EAKE,eAAe;AACjB;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,2BAA2B;EAC3B,oBAAoB;EACpB,mBAAmB;EACnB,YAAY;AACd;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,+CAA+C;AACjD;;AAEA;EACE,wDAAwD;EACxD,aAAa;EACb,8BAA8B;AAChC;;AAEA;EACE,wCAAwC;EACxC,eAAe;EACf,aAAa;EACb,gBAAgB;EAChB,gBAAgB;AAClB;;ACxQA;EACE,aAAa;EACb,WAAW;EACX,YAAY;EACZ,4CAA4C;EAC5C,+CAA+C;EAC/C,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,WAAW;EACX,aAAa;AACf;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;EAC/B,mCAAmC;EACnC,gBAAgB;EAChB,SAAS;AACX;;AAEA;EACE,sCAAsC;EACtC,oBAAoB;AACtB;;AAEA;EACE,0CAA0C;EAC1C,qCAAqC;EACrC,yCAAyC;EACzC,SAAS;AACX;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE;IACE,6CAA6C;EAC/C;AACF;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,aAAa;EACb,qBAAqB;EACrB,mBAAmB;EACnB,oBAAoB;EACpB,uDAAuD;EACvD,oBAAoB;EACpB,kBAAkB;EAClB,yBAAyB;EACzB,wCAAwC;EACxC,yBAAyB;AAC3B;;AAEA;EACE,eAAe;EACf,4CAA4C;AAC9C;;AAEA;EACE,0DAA0D;EAC1D,2CAA2C;AAC7C;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,gBAAgB;AAClB;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mDAAmD;EACnD,2BAA2B;EAC3B,4BAA4B;EAC5B,yCAAyC;EACzC,qBAAqB;EACrB,mBAAmB;AACrB;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;EAC/B,iCAAiC;EACjC,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,eAAe;EACf,SAAS;EACT,WAAW;EACX,6BAA6B;EAC7B,eAAe;EACf,kBAAkB;EAClB,8CAA8C;;EAE9C;IACE,oCAAoC;EACtC;AACF;;AAEA;EACE,4CAA4C;EAC5C,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,qBAAqB;EACrB,4CAA4C;EAC5C,kBAAkB;EAClB,eAAe;EACf,iCAAiC;AACnC;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,YAAY;EACZ,WAAW;AACb;;AAEA;EACE,qCAAqC;EACrC,uCAAuC;EACvC,wCAAwC;EACxC,iBAAiB;AACnB;;AAEA;EACE,eAAe;EACf,6BAA6B;EAC7B,YAAY;EACZ,aAAa;EACb,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,2DAA2D;AAC7D;;AAEA;EACE,oBAAoB;EACpB,kBAAkB;EAClB,yBAAyB;EACzB,+BAA+B;EAC/B,mCAAmC;EACnC,6CAA6C;EAC7C,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,WAAW;EACX,sCAAsC;EACtC,qCAAqC;EACrC,yCAAyC;EACzC,iBAAiB;AACnB;;AAEA;EACE,SAAS;EACT,qCAAqC;EACrC,mCAAmC;EACnC,iBAAiB;AACnB;;AAEA;EACE;IACE,aAAa;IACb,aAAa;IACb,eAAe;EACjB;;EAEA;IACE,+BAA+B;EACjC;AACF;;AC/MA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,wBAAwB;EAC1B;EACA;IACE,uBAAuB;EACzB;AACF;;AAEA;EACE,qBAAqB;EACrB,6BAA6B;EAC7B,SAAS;AACX;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,UAAU;EACV,WAAW;EACX,gBAAgB;AAClB;;AAEA;EACE,aAAa;;EAEb;IACE,gDAAgD;IAChD,oBAAoB;IACpB,kBAAkB;EACpB;AACF;;AAEA;EACE,gBAAgB;EAChB,iBAAiB;EACjB,eAAe;EACf,YAAY;EACZ,qBAAqB;EACrB,kBAAkB;EAClB,iBAAiB;EACjB,aAAa;EACb,mBAAmB;EACnB,YAAY;AACd;;AAEA;EACE,oBAAoB;EACpB,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,eAAe;EACf,yBAAyB;EACzB,gBAAgB;EAChB,kBAAkB;EAClB,iCAAiC;AACnC;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;;EAEE,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,cAAc;EACd,2EAAkD;AACpD;;AAEA;EACE,wBAAwB;EACxB,qBAAqB;EACrB,cAAc;EACd,mEAA0C;AAC5C;;AAEA;EACE,iBAAiB;EACjB,gBAAgB;EAChB,qBAAqB;AACvB;;AAEA;EACE;IACE;MACE,mCAAmC;IACrC;EACF;AACF;;AAEA;EACE,wBAAwB;AAC1B;;AAEA;EACE,yBAAyB;EACzB,gBAAgB;EAChB,kBAAkB;EAClB,iCAAiC;AACnC;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,qBAAqB;EACrB,0CAA0C;EAC1C,uBAAuB;EACvB,mBAAmB;EACnB,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AACzB;;AAEA;EACE,aAAa;EACb,QAAQ;EACR,kBAAkB;AACpB;;ACvJA;EACE,uBAAuB;EACvB,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,yBAAyB;EACzB,kBAAkB;AACpB;;AAEA;EACE,iBAAiB;EACjB,iBAAiB;AACnB;;AAEA;EACE,SAAS;AACX;;AAEA;EACE,YAAY;EACZ,YAAY;EACZ,yBAAyB;EACzB,oBAAoB;EACpB,uBAAuB;EACvB,mBAAmB;EACnB,sBAAsB;AACxB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,uCAAuC;EACvC,qCAAqC;EACrC,yCAAyC;EACzC,cAAc;EACd,gBAAgB;EAChB,yBAAyB;AAC3B;;AAEA;EACE,YAAY;EACZ,cAAc;EACd,YAAY;EACZ,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,qCAAqC;EACrC,gBAAgB;EAChB,WAAW;EACX,uBAAuB;EACvB,kBAAkB;EAClB,WAAW;EACX,0CAA0C;;EAE1C;IACE,cAAc;EAChB;;EAEA;IACE,iDAAiD;IACjD,sBAAsB;EACxB;AACF;;AAEA;;EAEE,yBAAyB;AAC3B;;AAEA;EACE,kBAAkB;EAClB,cAAc;EACd,gBAAgB;AAClB;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,YAAY;AACd;;ACzFA;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,aAAa;EACb,gBAAgB;EAChB,yBAAyB;EACzB,WAAW;EACX,YAAY;EACZ,8BAA8B;AAChC;;AAEA;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,aAAa;EACb,iCAAiC;EACjC,4BAA4B;EAC5B,kBAAkB;EAClB,eAAe;EACf,aAAa;EACb,uCAAuC;EACvC,gDAAgD;EAChD,oBAAoB;AACtB;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,aAAa;EACb,+BAA+B;EAC/B,mBAAmB;EACnB,gBAAgB;EAChB,aAAa;EACb,oCAAoC;EACpC,2DAA2D;EAC3D,gBAAgB,EAAE,kHAAkH;EACpI,aAAa;;EAEb;IACE,aAAa;IACb,mBAAmB;IACnB,YAAY;;IAEZ;MACE,YAAY;MACZ,WAAW;MACX,kBAAkB;MAClB,8CAA8C;MAC9C,4BAA4B;MAC5B,qBAAqB;MACrB,6BAA6B;MAC7B,gBAAgB;MAChB,iCAAiC;MACjC,kBAAkB;IACpB;;IAEA;MACE,aAAa;MACb,mBAAmB;MACnB,mBAAmB;MACnB,uBAAuB;MACvB,eAAe;MACf,UAAU;MACV,qBAAqB;MACrB,kBAAkB;MAClB,WAAW;MACX,YAAY;MACZ,iCAAiC;MACjC,kBAAkB;;MAElB;QACE,cAAc;MAChB;;MAEA;QACE,4CAA4C;MAC9C;IACF;EACF;AACF;;AAEA;EACE,qBAAqB;EACrB,WAAW;AACb;;AAEA;EACE,gBAAgB;EAChB,6BAA6B;EAC7B,iBAAiB;EACjB,SAAS;EACT,cAAc;EACd,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,0CAA0C;EAC1C,cAAc;EACd,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,oCAAoC;AACtC;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,cAAc;AAChB;;AAEA;EACE,aAAa;EACb,oBAAoB;EACpB,8BAA8B;EAC9B,gBAAgB;EAChB,aAAa;EACb,2DAA2D;AAC7D;;AAEA;EACE,kBAAkB;EAClB,SAAS;EACT,WAAW;EACX,aAAa;EACb,WAAW;EACX,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE;IACE,kCAAkC;EACpC;;EAEA;IACE,qCAAqC;EACvC;;EAEA;IACE,gCAAgC;IAChC,kBAAkB;EACpB;AACF;;AAEA;EACE;IACE,wBAAwB;EAC1B;;EAEA;;IAEE,gBAAgB;IAChB,YAAY;EACd;;EAEA;IACE,YAAY;EACd;AACF;;ACnLA;EACE;IACE,eAAe;IACf,kBAAkB;IAClB,YAAY;IACZ,WAAW;IACX,aAAa;IACb,uBAAuB;IACvB,mBAAmB;IACnB,iCAAiC;EACnC;;EAEA;IACE,4CAA4C;EAC9C;;EAEA;IACE,SAAS;IACT,SAAS;IACT,gDAAgD;IAChD,oBAAoB;EACtB;AACF;;AAEA;EACE,qDAAqD;EACrD,gDAAgD;;EAEhD;IACE,aAAa;IACb,cAAc;IACd,sCAAsC;IACtC,2CAA2C;IAC3C,gBAAgB;IAChB,UAAU;IACV,iEAAiE;IACjE,2BAA2B;EAC7B;;EAEA;IACE,cAAc;EAChB;;EAEA;IACE,aAAa;EACf;;EAEA;IACE,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,sBAAsB;IACtB,aAAa;IACb,SAAS;IACT,oCAAoC;IACpC,eAAe;IACf,yCAAyC;IACzC,sBAAsB;IACtB,YAAY;IACZ,WAAW;;IAEX;MACE;QACE,mCAAmC;MACrC;IACF;EACF;;EAEA;IACE,2BAA2B;IAC3B,4BAA4B;EAC9B;;EAEA;IACE,8BAA8B;IAC9B,+BAA+B;IAC/B,mBAAmB;EACrB;;EAEA;IACE;MACE,4CAA4C;IAC9C;;IAEA;MACE,mBAAmB;MACnB,wCAAwC;IAC1C;EACF;;EAEA;IACE,UAAU;IACV,yDAAyD;EAC3D;;EAEA;IACE,uCAAuC;IACvC,UAAU;IACV,yDAAyD;EAC3D;;EAEA;IACE,sCAAsC;IACtC,wDAAwD;;IAExD;MACE;QACE,qCAAqC;MACvC;IACF;EACF;AACF;;AC/GA;EACE,aAAa;EACb,sBAAsB;EACtB,kBAAkB;AACpB;;AAEA;EACE,oCAAoC;EACpC,iBAAiB;EACjB,gBAAgB;EAChB,kCAAkC;EAClC,mBAAmB;EACnB,cAAc;AAChB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,qBAAqB;AACvB;;AAEA;EACE,wCAAwC;AAC1C;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,UAAU;EACV,YAAY;EACZ,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,0CAA0C;AAC5C;;AAEA;EACE,cAAc;EACd,mBAAmB;EACnB,uBAAuB;EACvB,uBAAuB,EAAE,+CAA+C;AAC1E;;AAEA;EACE,mBAAmB,EAAE,wCAAwC;AAC/D;;AAEA;;EAEE,YAAY;AACd;;AAEA;EACE,kBAAkB;EAClB,WAAW;EACX,QAAQ;EACR,2BAA2B;EAC3B,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,eAAe;AACjB;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,kBAAkB;EAClB,8CAA8C;EAC9C,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,8BAA8B;EAC9B,WAAW;EACX,UAAU;EACV,SAAS;AACX;;AAEA;EACE,0CAA0C;AAC5C;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE,kBAAkB;EAClB,8CAA8C;AAChD;;AAEA;EACE,qBAAqB;EACrB,gBAAgB;EAChB,UAAU;EACV,SAAS;EACT,kBAAkB;EAClB,oDAAoD;EACpD,+CAA+C;;EAE/C;IACE,cAAc;EAChB;AACF;;AAEA;EACE,oBAAoB;EACpB,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;EAClB,UAAU;EACV,2CAA2C;AAC7C;;AAEA;EACE,qDAAuB;AACzB;;AAEA;EACE,aAAa;EACb,gBAAgB;EAChB,sBAAsB;EACtB,uBAAuB;EACvB,uBAAuB;EACvB,WAAW;EACX,WAAW;EACX,eAAe;EACf,uCAAuC;EACvC,oCAAoC;EACpC,yCAAyC;EACzC,oDAAoD;EACpD,sBAAsB;EACtB,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,4CAA4C;EAC5C,6CAA6C;EAC7C,oBAAoB;AACtB;;AAEA;EACE,YAAY;EACZ,oBAAoB;;EAEpB;IACE,+BAA+B;IAC/B,iBAAiB;IACjB,iCAAiC;EACnC;;EAEA;IACE,qBAAqB;EACvB;AACF;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,aAAa;EACb,oCAAoC;EACpC,iBAAiB;AACnB;;AAEA;;;;EAIE,4CAA4C;EAC5C,yCAAyC;AAC3C;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;AACb;;AAEA;EACE,cAAc;EACd,mBAAmB;AACrB;;AAEA;EACE,uCAAuC;EACvC,WAAW;EACX,+CAA+C;EAC/C,kBAAkB;EAClB,eAAe;EACf,iBAAiB;EACjB,iBAAiB;EACjB,gBAAgB;;EAEhB;IACE,+CAA+C;EACjD;;EAEA;IACE,oDAAoD;EACtD;AACF;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,WAAW;EACX,sBAAsB;EACtB,oDAAoD;EACpD,wBAAwB;EACxB,kBAAkB;;EAElB;IACE,kBAAkB;IAClB,QAAQ;IACR,2BAA2B;IAC3B,UAAU;IACV,uBAAuB;EACzB;AACF;;AAEA;EACE,mBAAmB;EACnB,sCAAsC;EACtC,qCAAqC;EACrC,sBAAsB;EACtB,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,WAAW;AACb;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,+BAA+B;EAC/B,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,QAAQ;AACV;;AAEA;EACE;IACE,sBAAsB;EACxB;AACF;;AAEA;EACE,iBAAiB;EACjB,gBAAgB;AAClB;;AAEA;EACE,gBAAgB;EAChB,SAAS;EACT,uCAAuC;EACvC,wDAAwD;EACxD,eAAe;EACf,UAAU;AACZ;;AAEA;EACE,WAAW;EACX,gBAAgB;EAChB,eAAe;EACf,gBAAgB;EAChB,YAAY;EACZ,UAAU;AACZ;;ACrSA;EACE,6BAA6B;EAC7B,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,oCAAoC;EACpC,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,YAAY;EACZ,eAAe;AACjB;;AAEA;;EAEE,mBAAmB;EACnB,qBAAqB;EACrB,wCAAwC;EACxC;IACE,uCAAuC;EACzC;AACF;;AAEA;EACE,gDAAgD;AAClD;;AAEA;;EAEE,WAAW;AACb;;AAEA;EACE,YAAY;AACd;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,qCAAqC;AACvC;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,qCAAqC;EACrC,UAAU;AACZ;;AAEA;EACE,0BAA0B;AAC5B;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AACzB;;AC9DA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,oBAAoB;EACpB,+CAA+C;EAC/C,kBAAkB;EAClB,iCAAiC;EACjC,YAAY;AACd;;AAEA;EACE,mBAAmB;EACnB,iCAAiC;EACjC,aAAa;EACb,WAAW;EACX,YAAY;EACZ,8BAA8B;AAChC;;AAEA;EACE,gBAAgB;EAChB,YAAY;EACZ,eAAe;EACf,UAAU;;EAEV;IACE;MACE,4CAA4C;IAC9C;EACF;AACF;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,WAAW;AACb;;AAEA;EACE,+BAA+B;EAC/B,iBAAiB;EACjB,oCAAoC;AACtC;;AAEA;EACE,sCAAsC;EACtC,6CAA6C;AAC/C;;AAEA;EACE,WAAW;;EAEX;IACE,eAAe;IACf,kCAAkC;IAClC,mBAAmB;EACrB;;EAEA;IACE,YAAY;IACZ,UAAU;EACZ;AACF;;AAEA;EACE;IACE,qBAAqB;IACrB,MAAM;EACR;;EAEA;IACE,mBAAmB;IACnB,sBAAsB;IACtB,gBAAgB;IAChB,iBAAiB;EACnB;;EAEA;IACE,gBAAgB;EAClB;AACF;;;;AAIA;EACE,WAAW;EACX,iBAAiB;AACnB;;AAEA;EACE,aAAa;EACb,2DAA2D;EAC3D,uBAAuB;EACvB,wBAAwB;EACxB,+CAA+C;EAC/C,kBAAkB;EAClB,qBAAqB;EACrB,iCAAiC;EACjC,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,cAAc;EACd,iBAAiB;AACnB;;AAEA;;;EAGE,kBAAkB;EAClB,YAAY;EACZ,cAAc;EACd,kBAAkB;EAClB,YAAY;EACZ,qBAAqB;AACvB;;AAEA,uDAAuD;AACvD;;EAEE,iDAAiD;AACnD;;AAEA;EACE,iDAAiD;AACnD;;AAEA;EACE,iDAAiD;AACnD;;AAEA,kCAAkC;AAClC;;EAEE,wBAAwB;AAC1B;;AAEA;;EAEE,wBAAwB;AAC1B;;AAEA,kDAAkD;AAClD;;;;EAIE,yBAAyB;EACzB,6BAA6B;EAC7B,6BAA6B;EAC7B,aAAa;EACb,mBAAmB;EACnB,mDAAmD;EACnD,gBAAgB;EAChB,mDAAmD;EACnD,mBAAmB;EACnB,iCAAiC;AACnC;;AAEA,wDAAwD;AACxD;;;;;;EAME,yBAAyB;EACzB,6BAA6B;AAC/B;;AAEA,2DAA2D;AAC3D;;;;;;EAME,yBAAyB;EACzB,6BAA6B;EAC7B,wBAAwB;EACxB,8BAA8B;EAC9B,iCAAiC;AACnC;;AAEA;;EAEE,gBAAgB;EAChB,yBAAyB;EACzB,6BAA6B;EAC7B,8BAA8B;AAChC;;AAEA,qEAAqE;AACrE;EACE,8BAA8B;EAC9B,+BAA+B;AACjC;;AAEA,gEAAgE;AAChE;EACE,yBAAyB;EACzB,6BAA6B;EAC7B,6BAA6B;EAC7B,wBAAwB;EACxB,4BAA4B;EAC5B,8BAA8B;EAC9B,mBAAmB;EACnB,sBAAsB;EACtB,8BAA8B;EAC9B,6BAA6B;EAC7B,iCAAiC;EACjC,8BAA8B;EAC9B,+BAA+B;AACjC;;;;AAIA,wEAAwE;AACxE;;EAEE,wBAAwB;EACxB,4BAA4B;EAC5B,8BAA8B;EAC9B,mBAAmB;EACnB,sBAAsB;EACtB,8BAA8B;EAC9B,6BAA6B;EAC7B,8BAA8B;EAC9B,+BAA+B;EAC/B,iCAAiC;AACnC;;AAEA,+CAA+C;AAC/C;EACE,wBAAwB;EACxB,4BAA4B;EAC5B,8BAA8B;EAC9B,mBAAmB;EACnB,sBAAsB;EACtB,8BAA8B;EAC9B,6BAA6B;EAC7B,8BAA8B;EAC9B,+BAA+B;EAC/B,iCAAiC;EACjC,0DAA0D;EAC1D,+BAA+B;EAC/B,gCAAgC;AAClC;;AAEA,+CAA+C;AAC/C;EACE,YAAY;EACZ,UAAU;AACZ;;AAEA,yDAAyD;AACzD;;;EAGE,8BAA8B;EAC9B,+BAA+B;EAC/B,+BAA+B;AACjC;;AAEA,0DAA0D;AAC1D;EACE,wBAAwB;EACxB,4BAA4B;EAC5B,8BAA8B;EAC9B,sBAAsB;EACtB,8BAA8B;EAC9B,6BAA6B;EAC7B,8BAA8B;EAC9B,+BAA+B;EAC/B,iCAAiC;AACnC;;AAEA,+DAA+D;AAC/D;;;EAGE,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA,kFAAkF;AAClF;;;EAGE,kBAAkB;EAClB,YAAY,EAAE,uCAAuC;EACrD,OAAO;EACP,QAAQ;EACR,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA,iCAAiC;AACjC;EACE,+BAA+B;EAC/B,mBAAmB;EACnB,gBAAgB;EAChB,uCAAuC;EACvC,mBAAmB;EACnB,gBAAgB;EAChB,0BAA0B;EAC1B,gBAAgB;EAChB,sDAAsD;EACtD,gBAAgB;EAChB,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,2DAA2D;EAC3D,kBAAkB;EAClB,cAAc;EACd,qCAAqC;EACrC,gBAAgB;EAChB,mBAAmB;EACnB,gBAAgB;EAChB,uBAAuB;AACzB;;AAEA;EACE,qCAAqC;EACrC,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;EAChB,YAAY;EACZ,eAAe;EACf,eAAe;EACf,kBAAkB;EAClB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,WAAW;EACX,YAAY;;EAEZ;IACE,4CAA4C;EAC9C;;EAEA;IACE,WAAW;IACX,YAAY;IACZ;MACE,8BAA8B;IAChC;EACF;AACF;;AAEA;EACE,kBAAkB;EAClB,qBAAqB;EACrB,aAAa;AACf;;AAEA;EACE,oCAAoC;EACpC,gBAAgB;EAChB,mBAAmB;EACnB,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,gBAAgB;EAChB,YAAY;EACZ,eAAe;;EAEf;IACE,0BAA0B;EAC5B;;EAEA;IACE,WAAW;IACX,YAAY;IACZ;MACE,mCAAmC;IACrC;EACF;AACF;;AAEA;EACE,eAAe;EACf,iCAAiC;EACjC,+CAA+C;EAC/C,kBAAkB;EAClB,0CAA0C;EAC1C,cAAc;EACd,gBAAgB;EAChB,gBAAgB;EAChB,iBAAiB;EACjB,oBAAoB;AACtB;;AAEA;EACE,cAAc;EACd,WAAW;EACX,qBAAqB;EACrB,gBAAgB;EAChB,YAAY;EACZ,gBAAgB;EAChB,mBAAmB;EACnB,oCAAoC;EACpC,eAAe;EACf,oBAAoB;EACpB,iBAAiB;;EAEjB;IACE,4CAA4C;EAC9C;;EAEA;IACE,2BAA2B;IAC3B,4BAA4B;EAC9B;;EAEA;IACE,8BAA8B;IAC9B,+BAA+B;EACjC;AACF;;AAEA;EACE,iCAAiC;AACnC;;AAEA;EACE,+CAA+C;EAC/C,kBAAkB;EAClB,sBAAsB;EACtB,oCAAoC;EACpC,oBAAoB;EACpB,+BAA+B;EAC/B,cAAc;EACd,mCAAmC;EACnC,WAAW;EACX,aAAa;EACb,oCAAoC;EACpC,mBAAmB;;EAEnB;IACE,2CAA2C;EAC7C;AACF;;AAEA;EACE,kBAAkB;EAClB,qBAAqB;EACrB,qCAAqC;EACrC,iBAAiB;EACjB,gBAAgB;EAChB,uCAAuC;EACvC,cAAc;EACd,iBAAiB;AACnB;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,iDAAiD;AACnD;;AAEA;EACE,uCAAuC;EACvC,+CAA+C;AACjD;;AAEA;EACE,+BAA+B;EAC/B,kBAAkB;EAClB,mBAAmB;EACnB,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,mBAAmB;EACnB,WAAW;EACX,iCAAiC;EACjC,yBAAyB;EACzB,kBAAkB;EAClB,wCAAwC;AAC1C;;AAEA,iEAAiE;AACjE;EACE,wBAAwB;EACxB,0BAA0B;AAC5B;;AAEA;EACE,wBAAwB;EACxB,6BAA6B;AAC/B;;AAEA,wDAAwD;AACxD;EACE,wBAAwB;EACxB,0BAA0B;AAC5B;;AAEA;EACE,wBAAwB;EACxB,6BAA6B;AAC/B;;AAEA,yBAAyB;AACzB;EACE,YAAY;EACZ,yBAAyB;AAC3B;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,YAAY;EACZ,uBAAuB;EACvB,gBAAgB;EAChB,aAAa;AACf;;AAEA;EACE,2CAA2C;EAC3C,iBAAiB;EACjB,yBAAyB;EACzB,2BAA2B;EAC3B,wCAAwC;AAC1C;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,cAAc;EACd,WAAW;EACX,YAAY;EACZ,YAAY;EACZ,6CAA6C;EAC7C,2BAA2B;AAC7B;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE,gBAAgB;AAClB;;AC7iBA;EACE,oBAAoB;EACpB,mBAAmB;EACnB,sBAAsB;EACtB,cAAc;EACd,eAAe;EACf,kBAAkB;EAClB,0CAA0C;EAC1C,qBAAqB;EACrB,sCAAsC;EACtC,sCAAsC;EACtC,eAAe;AACjB;;AAEA;EACE,0CAA0C;EAC1C,0CAA0C;AAC5C;;AAEA;EACE,0CAA0C;EAC1C,uCAAuC;EACvC,mBAAmB;;EAEnB;;;;IAIE,6BAA6B;IAC7B,mDAAmD;EACrD;AACF;;AAEA;EACE,WAAW;EACX,YAAY;EACZ,qBAAqB;EACrB,uCAAuC;EACvC,qDAAqD;AACvD;;AAEA;EACE,2BAA2B;AAC7B;;AAEA;EACE,sCAAsC;;EAEtC;;;;IAIE,4CAA4C;EAC9C;AACF;;AAEA;EACE,kBAAkB;EAClB,aAAa;EACb,YAAY;AACd;;AAEA;EACE,+CAA+C;EAC/C,oBAAoB;EACpB,YAAY;AACd;;AClEA;EACE,aAAa;EACb,SAAS;EACT,+BAA+B;EAC/B,kDAAkD;EAClD,kBAAkB;EAClB,qCAAqC;EACrC,0CAA0C;EAC1C,mBAAmB;AACrB;;AAEA;EACE,qCAAqC;AACvC;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,WAAW;AACb;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,kCAAkC;EAClC,mCAAmC;EACnC,gBAAgB;AAClB;;AAEA;;EAEE,SAAS;AACX;;AAEA;EACE,kDAAkD;AACpD;;AAEA;EACE,kDAAkD;AACpD;;AAEA;EACE,mDAAmD;AACrD;;AAEA;EACE,sDAAsD;AACxD;;AClDA;EACE,aAAa;EACb,mBAAmB;EACnB,YAAY;EACZ,eAAe;EACf,iCAAiC;EACjC,qBAAqB;EACrB,uDAAuD;AACzD;;AAEA;EACE,2BAA2B;EAC3B,4BAA4B;AAC9B;;AAEA;EACE,8BAA8B;EAC9B,+BAA+B;AACjC;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,qCAAqC;EACrC,mCAAmC;AACrC;;AAEA;EACE,gBAAgB;EAChB,oCAAoC;AACtC;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,kBAAkB;EAClB,sCAAsC;EACtC,gBAAgB;AAClB;;AC5CA;EACE,qBAAqB;EACrB,aAAa;EACb,mBAAmB;EACnB,YAAY;EACZ,WAAW;EACX,4BAA4B;EAC5B,eAAe;EACf,gBAAgB;EAChB,kBAAkB;EAClB,iCAAiC;;EAEjC;IACE,4BAA4B;IAC5B,qDAAqD;EACvD;;EAEA;IACE,2CAA2C;IAC3C,oBAAoB;EACtB;AACF;;AAEA;EACE,iBAAiB;EACjB,eAAe;AACjB;;AAEA;EACE,8BAA8B;AAChC;;AAEA;EACE,4BAA4B;EAC5B,qDAAqD;EACrD,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,sBAAsB;EACtB,aAAa;AACf;;AC1CA;EACE,eAAe;EACf,MAAM;EACN,OAAO;EACP,aAAa;EACb,gBAAgB;EAChB,uBAAuB;EACvB,WAAW;EACX,YAAY;EACZ,6BAA6B;AAC/B;;AAEA;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,yBAAyB;EACzB,YAAY;AACd;;AAEA;EACE,mBAAmB;EACnB,gBAAgB;EAChB,gBAAgB;EAChB,uBAAuB;EACvB,iBAAiB;EACjB,kBAAkB;EAClB,aAAa;AACf;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,eAAe;EACf,UAAU;EACV,uCAAuC;EACvC,qCAAqC;EACrC,8BAA8B;EAC9B,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,YAAY;EACZ,aAAa;AACf;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,wBAAwB;AAC1B;;AAEA;EACE,sDAAsD;EACtD,oBAAoB;AACtB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,WAAW;AACb;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,YAAY;EACZ,OAAO;AACT;;AAEA;EACE,oCAAoC;EACpC,SAAS;EACT,6BAA6B;EAC7B,iCAAiC;EACjC,gBAAgB;EAChB,aAAa;EACb,qBAAqB;EACrB,sBAAsB;EACtB,aAAa;AACf;;AAEA;EACE,mBAAmB,EAAE,yCAAyC;AAChE;;AAEA;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,gBAAgB;EAChB,0CAA0C;AAC5C;;AAEA;EACE,aAAa;EACb,gCAAgC;EAChC,sBAAsB;EACtB,mBAAmB;EACnB,kBAAkB;EAClB,wCAAwC;AAC1C;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,0CAA0C;AAC5C;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,iBAAiB;EACjB,eAAe;EACf,YAAY;EACZ,OAAO;EACP,aAAa;AACf;;AAEA;EACE,mBAAmB;EACnB,gBAAgB;AAClB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,gBAAgB;EAChB,iBAAiB;EACjB,iBAAiB;AACnB;;AAEA;EACE,aAAa;EACb,2BAA2B;EAC3B,kBAAkB;EAClB,uCAAuC;EACvC,oBAAoB;EACpB,mDAAmD;EACnD,SAAS;AACX;;AAEA;EACE,sBAAsB;AACxB;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,8BAA8B;;EAE9B;IACE,WAAW;EACb;AACF;;AAEA;EACE,6BAA6B;EAC7B,YAAY;EACZ,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,eAAe;EACf,oDAAoD;AACtD;;AAEA;EACE,aAAa;EACb,2BAA2B;EAC3B,kBAAkB;;EAElB;IACE,yBAAyB;EAC3B;;EAEA;IACE,oCAAoC;IACpC,+BAA+B;IAC/B,uCAAuC;IACvC,gBAAgB;IAChB,mCAAmC;EACrC;;EAEA;IACE,wCAAwC;IACxC,uCAAuC;IACvC,gBAAgB;IAChB,mCAAmC;IACnC,sCAAsC;EACxC;;EAEA;IACE,oCAAoC;IACpC,YAAY;IACZ,sDAAsD;EACxD;;EAEA;IACE,oBAAoB;EACtB;;EAEA;IACE,wBAAwB;EAC1B;;EAEA;IACE,gBAAgB;IAChB,oBAAoB;IACpB,kBAAkB;EACpB;;EAEA;IACE,YAAY;IACZ,sDAAsD;IACtD,WAAW;IACX,mBAAmB;EACrB;;EAEA;IACE,QAAQ;IACR,SAAS;IACT,aAAa;EACf;;EAEA;IACE,WAAW;IACX,YAAY;IACZ,UAAU;IACV,SAAS;IACT,aAAa;IACb,2BAA2B;IAC3B,gCAAgC;IAChC,kBAAkB;EACpB;;EAEA;IACE,gBAAgB;IAChB,qCAAqC;IACrC,oCAAoC;IACpC,gBAAgB;IAChB,kBAAkB;EACpB;;EAEA;IACE,eAAe;EACjB;AACF;;AAEA;EACE,kBAAkB;EAClB,aAAa;EACb,WAAW;EACX,SAAS;EACT,UAAU;EACV,mBAAmB;EACnB,eAAe;EACf,gBAAgB;EAChB,oCAAoC;EACpC,UAAU;;EAEV;IACE,0BAA0B;EAC5B;AACF;;AAEA;EACE,oCAAoC;AACtC;;AAEA;EACE;IACE,mBAAmB;EACrB;;EAEA;IACE,cAAc;IACd,iBAAiB;IACjB,kBAAkB;EACpB;;EAEA;IACE,oBAAoB;EACtB;;EAEA;IACE,sBAAsB;EACxB;;EAEA;IACE,aAAa;IACb,eAAe;IACf,aAAa;EACf;;EAEA;IACE,YAAY;IACZ,eAAe;EACjB;;EAEA;IACE,cAAc;IACd,eAAe;EACjB;;EAEA;IACE,YAAY;IACZ,eAAe;EACjB;;EAEA;IACE,eAAe;IACf,eAAe;EACjB;;EAEA;IACE,oBAAoB;EACtB;;EAEA;IACE,uBAAuB;EACzB;;EAEA;IACE,oBAAoB;IACpB,+BAA+B;IAC/B,8BAA8B;EAChC;;EAEA;IACE;MACE,WAAW;IACb;EACF;AACF;;AAEA;EACE,kBAAkB;EAClB,SAAS;EACT,WAAW;AACb;;AClWA;EACE,wBAAwB;EACxB,mBAAmB;EACnB,oBAAoB;EACpB,kBAAkB;AACpB;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,uCAAuC;EACvC,0CAA0C;AAC5C;;ACZA;EACE,aAAa;EACb,QAAQ;AACV;;AAEA;EACE,eAAe;EACf,oDAAoD;AACtD;;AAEA;EACE,eAAe;EACf,0CAA0C;EAC1C,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE;IACE,WAAW;IACX,cAAc;EAChB;;EAEA;IACE,0CAA0C;IAC1C,kBAAkB;;IAElB;MACE;QACE,WAAW;QACX,kBAAkB;QAClB,UAAU;QACV,WAAW;QACX,0CAA0C;QAC1C,MAAM;QACN,UAAU;MACZ;IACF;EACF;AACF;;AC1CA;EACE,yBAAyB;EACzB,kBAAkB;EAClB,aAAa;EACb,uCAAuC;EACvC,uCAAuC;AACzC;;AAEA;EACE,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,qBAAqB;AACvB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,kBAAkB;EAClB,gBAAgB;EAChB,oCAAoC;EACpC,SAAS;EACT,mCAAmC;AACrC;;AAEA;EACE,mBAAmB;EACnB,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,WAAW;EACX,mBAAmB;EACnB,oCAAoC;EACpC,gBAAgB;EAChB,eAAe;EACf,UAAU;;EAEV;IACE,0BAA0B;EAC5B;AACF;;AAEA;EACE,qBAAqB;EACrB,0CAA0C;AAC5C;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,qBAAqB;EACrB,8BAA8B;AAChC;;AAEA;EACE,mBAAmB;EACnB,gBAAgB;EAChB,oBAAoB;AACtB;;AAEA;EACE,aAAa;EACb,WAAW;AACb;;AAEA;EACE,YAAY;EACZ,YAAY;EACZ,kBAAkB;EAClB,uDAAuD;EACvD,gBAAgB;EAChB,kBAAkB;EAClB,WAAW;EACX,WAAW;EACX,gBAAgB;AAClB;;AAEA;EACE,UAAU;AACZ;;AAEA;EACE,YAAY;EACZ,+CAA+C;EAC/C,kBAAkB;EAClB,MAAM;EACN,OAAO;AACT;;AAEA;EACE,eAAe;EACf,6CAA6C;EAC7C,iBAAiB;AACnB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,WAAW;AACb;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,UAAU;AACZ;;AAEA;EACE,kBAAkB;EAClB,YAAY;EACZ,WAAW;AACb;;ACtHA;EACE,WAAW;EACX,sBAAsB;EACtB,oCAAoC;EACpC,uCAAuC;EACvC,2CAA2C;EAC3C,gBAAgB;EAChB,yBAAyB;EACzB,kBAAkB;EAClB,4CAA4C;EAC5C,sBAAsB;AACxB;;AAEA;EACE,2BAA2B;AAC7B;;ACfA;EACE,aAAa;EACb,oCAAoC;EACpC,oBAAoB;EACpB,WAAW;EACX,oBAAoB;EACpB,SAAS;EACT,kBAAkB;EAClB,+CAA+C;EAC/C,uCAAuC;EACvC,eAAe;EACf,sCAAsC;AACxC;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,uCAAuC;AACzC;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;EAC/B,mCAAmC;EACnC,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,0CAA0C;EAC1C,qCAAqC;EACrC,yCAAyC;EACzC,kBAAkB;EAClB,iBAAiB;AACnB;;AAEA;EACE,kBAAkB;EAClB,aAAa;EACb,cAAc;;EAEd;IACE,cAAc;IACd,eAAe;EACjB;AACF;;AAEA;EACE,aAAa;EACb,oBAAoB;AACtB;;AAEA;EACE,iBAAiB;EACjB,kBAAkB;EAClB,iBAAiB;EACjB,aAAa;EACb,cAAc;EACd,oCAAoC;;EAEpC;IACE,cAAc;IACd,eAAe;EACjB;AACF;;AAEA;EACE,mBAAmB;EACnB,sCAAsC;;EAEtC;IACE,6CAA6C;EAC/C;AACF;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,qCAAqC;AACvC;;AAEA;EACE,mCAAmC;AACrC;;AAEA;EACE,kCAAkC;AACpC;;AAEA;EACE,kCAAkC;AACpC;;ACnGA;EACE,WAAW;AACb;;AAEA;EACE,kBAAkB;EAClB,aAAa;EACb,mBAAmB;EACnB,qDAAqD;EACrD,kBAAkB;EAClB,oBAAoB;EACpB,uCAAuC;EACvC,iCAAiC;;EAEjC;IACE,wCAAwC;EAC1C;;EAEA;IACE,oDAAoD;EACtD;;EAEA;IACE,aAAa;;IAEb;MACE,gDAAgD;IAClD;EACF;AACF;;AAEA;EACE,WAAW;AACb;;AAEA;;EAEE,aAAa;EACb,+CAA+C;;EAE/C;IACE,gDAAgD;IAChD,mBAAmB;EACrB;AACF;;AAEA;;EAEE,6CAA6C;AAC/C;;AAEA;EACE,aAAa;EACb,eAAe;EACf,WAAW;EACX,WAAW;EACX,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,WAAW;EACX,eAAe;EACf,YAAY;AACd;;AAEA;EACE,kBAAkB;EAClB,sCAAsC;EACtC,gBAAgB;AAClB;;AAEA;EACE,6BAA6B;EAC7B,oCAAoC;EACpC,YAAY;EACZ,aAAa;EACb,+BAA+B;EAC/B,mCAAmC;EACnC,UAAU;EACV,QAAQ;EACR,eAAe;EACf,uCAAuC;AACzC;;AAEA;EACE,kBAAkB;EAClB,uCAAuC;EACvC,sBAAsB;EACtB,+CAA+C;EAC/C,oBAAoB;EACpB,gBAAgB;EAChB,WAAW;EACX,WAAW;EACX,SAAS;EACT,OAAO;EACP,gBAAgB;EAChB,UAAU;AACZ;;AAEA;EACE,eAAe;EACf,eAAe;EACf,oCAAoC;EACpC,iCAAiC;;EAEjC;IACE,4CAA4C;EAC9C;AACF;;AAEA;EACE,4CAA4C;AAC9C;;AAEA;EACE,eAAe;EACf,oCAAoC;EACpC,eAAe;EACf,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,WAAW;EACX,sCAAsC;EACtC,qCAAqC;EACrC,yCAAyC;EACzC,mBAAmB;AACrB;;AAEA;EACE,SAAS;EACT,qCAAqC;EACrC,mCAAmC;AACrC;;AC5IA;;;EAGE,kBAAkB;EAClB,aAAa;EACb,oCAAoC;EACpC,kBAAkB;EAClB,yCAAyC;EACzC,sBAAsB;EACtB,mBAAmB;EACnB,4CAA4C;EAC5C,oDAAoD;EACpD,oBAAoB;EACpB,iCAAiC;EACjC,qCAAqC;EACrC,yCAAyC;EACzC,0BAA0B;EAC1B,kBAAkB;;EAElB;IACE,iCAAiC;;IAEjC;MACE,4CAA4C;IAC9C;;IAEA;MACE,gDAAgD;MAChD,mBAAmB;IACrB;EACF;AACF;;AAEA;EACE,aAAa;AACf;;AAEA;;EAEE,6BAA6B;EAC7B,8CAA8C;AAChD;;AAEA;;EAEE,qCAAqC;AACvC;;AAEA;EACE,oCAAoC;EACpC,6CAA6C;AAC/C;;AAEA;EACE,gBAAgB;EAChB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,uBAAuB;EACvB,kBAAkB;EAClB,WAAW;EACX,2BAA2B;AAC7B;;AAEA;EACE,4CAA4C;EAC5C,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,WAAW;EACX,YAAY;EACZ,kBAAkB;AACpB;;AAEA;EACE,+BAA+B;AACjC;;AAEA;EACE,qCAAqC;EACrC,6CAA6C;AAC/C;;AAEA,yCAAyC;;AAEzC;EACE,iBAAiB;AACnB;;AAEA;EACE,aAAa;EACb,cAAc;EACd,0CAA0C;EAC1C,+CAA+C;EAC/C,UAAU;EACV,kBAAkB;EAClB,qCAAqC;EACrC,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;EAClB,UAAU;EACV,yDAAyD;AAC3D;;AAEA;EACE,uCAAuC;EACvC,mBAAmB;EACnB,UAAU;EACV,yDAAyD;AAC3D;;AAEA;EACE,eAAe;EACf,aAAa;EACb,eAAe;EACf,8BAA8B;EAC9B,mBAAmB;EACnB,mBAAmB;EACnB,oCAAoC;EACpC,6CAA6C;EAC7C,+BAA+B;EAC/B,kBAAkB;EAClB,gBAAgB;EAChB,mCAAmC;AACrC;;AAEA;EACE,YAAY;EACZ,iBAAiB;AACnB;;AAEA;EACE,sCAAsC;AACxC;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;AACrB;;AAEA;EACE,eAAe;EACf,oDAAoD;;EAEpD;IACE,oDAAoD;EACtD;AACF;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;AACrB;;AAEA;;EAEE,aAAa;AACf;;AAEA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;AACX;;AAEA;EACE,QAAQ;EACR,SAAS;AACX;;AAEA;EACE,2DAA+B;EAC/B,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,UAAU;AACZ;;AAEA;EACE;IACE,mBAAmB;IACnB,UAAU;EACZ;EACA;IACE,sBAAsB;IACtB,UAAU;EACZ;EACA;IACE,sBAAsB;IACtB,UAAU;EACZ;EACA;IACE,mBAAmB;IACnB,UAAU;EACZ;AACF;;ACrNA;EACE,aAAa;EACb,mBAAmB;EACnB,YAAY;EACZ,yCAAyC;EACzC,sBAAsB;EACtB,mBAAmB;EACnB,4CAA4C;EAC5C,+CAA+C;EAC/C,oBAAoB;EACpB,iCAAiC;EACjC,qCAAqC;EACrC,yCAAyC;EACzC,0BAA0B;AAC5B;;AAEA;EACE,eAAe;EACf,4CAA4C;EAC5C,4CAA4C;AAC9C;;AAEA;EACE,oCAAoC;EACpC,6CAA6C;AAC/C;;ACzBA;EACE,uBAAuB;EACvB,qBAAqB;EACrB,qCAAqC;EACrC,yCAAyC;EACzC,gBAAgB;EAChB,wCAAwC;EACxC,oCAAoC;EACpC,eAAe;EACf,gBAAgB;EAChB,qBAAqB;AACvB;;AAEA;EACE,wCAAwC;EACxC,4CAA4C;AAC9C;;AAEA;EACE,gDAAgD;EAChD,mBAAmB;AACrB;;AAEA;EACE,oCAAoC;EACpC,iDAAiD;AACnD;;AC1BA;EACE,kBAAkB;EAClB,oDAAoD;EACpD,uCAAuC;EACvC,WAAW;EACX,YAAY;AACd;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,WAAW;EACX,yBAAyB;EACzB,YAAY;EACZ,SAAS;EACT,OAAO;AACT;;AAEA;EACE,gBAAgB;EAChB,MAAM;EACN,yBAAyB;EACzB,oCAAoC;AACtC;;AAEA,yDAAyD;AACzD;EACE,WAAW;EACX,cAAc;EACd,kBAAkB;EAClB,yBAAyB;EACzB,OAAO;EACP,WAAW;EACX,WAAW;EACX,SAAS;AACX;;AAEA;;EAEE,gBAAgB;EAChB,YAAY;AACd;;AAEA;EACE,2DAA2D;;EAE3D;IACE,6BAA6B;EAC/B;;EAEA;IACE,4CAA4C;EAC9C;AACF;;AAEA;EACE,oDAAoD;AACtD;;AAEA;EACE,eAAe;;EAEf;IACE,gDAAgD;IAChD,oBAAoB;EACtB;AACF;;AAEA;EACE,gBAAgB;EAChB,gBAAgB;EAChB,yBAAyB;EACzB,0CAA0C;EAC1C,sCAAsC;EACtC,yBAAyB;EACzB,2DAA2D;EAC3D,mBAAmB;AACrB;;AAEA;;;;EAIE,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,uCAAuC;AACzC;;AAEA;EACE,gBAAgB;EAChB,0CAA0C;EAC1C,SAAS;EACT,gBAAgB;EAChB,6BAA6B;EAC7B,gBAAgB;EAChB,gBAAgB;EAChB,iCAAiC;;EAEjC;IACE,gDAAgD;IAChD,oBAAoB;EACtB;;EAEA;IACE,oDAAoD;EACtD;AACF;;AAEA;;;;EAIE,QAAQ,EAAE,sCAAsC;AAClD;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,2BAA2B;EAC3B,mBAAmB;EACnB,0CAA0C;AAC5C;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,SAAS;EACT,iBAAiB;AACnB;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,SAAS;EACT,OAAO;EACP,iBAAiB;AACnB;;AAEA;EACE,iBAAiB;EACjB,0CAA0C;AAC5C;;AAEA;EACE,qBAAqB;EACrB,mBAAmB;EACnB,oCAAoC;EACpC,sCAAsC;EACtC,gBAAgB;EAChB,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;AACrB;;AAEA;EACE,YAAY;EACZ,kBAAkB;EAClB,yCAAyC;EACzC,iBAAiB;AACnB;;AAEA;;EAEE,0CAA0C;AAC5C;;AAEA;EACE,aAAa;EACb,QAAQ;AACV;;AAEA;;;EAGE,YAAY;EACZ,kBAAkB;EAClB,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,sCAAsC;EACtC,iBAAiB;AACnB;;AAEA;EACE,yCAAyC;EACzC,YAAY;AACd;;AAEA;EACE,gBAAgB;EAChB,YAAY;EACZ,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,YAAY;AACd;;AAEA;EACE,YAAY;EACZ,0CAA0C;EAC1C,gBAAgB;AAClB;;AAEA;EACE,wCAAwC;EACxC,gBAAgB;AAClB;;AAEA;EACE,eAAe;EACf,4CAA4C;AAC9C;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;AACb;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,wCAAwC;EACxC,aAAa;EACb,YAAY;EACZ,kBAAkB;EAClB,mBAAmB;EACnB,uBAAuB;EACvB,WAAW;EACX,WAAW;AACb;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,YAAY;AACd;;AAEA;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,gBAAgB;AAClB;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;AAC3C;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,yBAAyB;AAC3B;;AAEA;;EAEE,aAAa;AACf;AACA;EACE,mBAAmB;EACnB,2DAA2D;AAC7D;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,kBAAkB;EAClB,gBAAgB;EAChB,4CAA4C;EAC5C,kBAAkB;EAClB,YAAY;EACZ,UAAU;AACZ;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,YAAY;EACZ,UAAU;EACV;;;;;GAKC;EACD,0BAA0B;EAC1B,4BAA4B;EAC5B,6DAAuC;AACzC;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,eAAe;AACjB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,SAAS;AACX;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,YAAY;AACd;;AAEA;EACE,qCAAqC;AACvC;;AAEA;EACE,4DAA4D;AAC9D;;AAEA;EACE;;IAEE,mBAAmB;EACrB;AACF;;AAEA;;EAEE,aAAa;AACf;;AAEA;EACE;IACE,mBAAmB;EACrB;AACF;;AAEA;EACE;IACE,mBAAmB;EACrB;AACF;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE;IACE,4BAA4B;EAC9B;EACA;IACE,2BAA2B;EAC7B;AACF;;AC9XA;EACE,kBAAkB;EAClB,WAAW;EACX,qBAAqB;EACrB,mBAAmB;EACnB,sBAAsB;EACtB,eAAe;;EAEf;IACE,gBAAgB;EAClB;AACF;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,aAAa;EACb,SAAS;EACT,sBAAsB;AACxB;;ACrBA;EACE,aAAa;EACb,gCAAgC;EAChC,mBAAmB;EACnB,uBAAuB;EACvB,kBAAkB;EAClB,aAAa;EACb,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,SAAS;EACT,4CAA4C;EAC5C,iCAAiC;EACjC,mBAAmB;EACnB,kBAAkB;AACpB;;AAEA,0DAA0D;AAC1D,oBAAoB;AACpB,gEAAgE;AAChE;;;;GAIG;;AAEH;;;GAGG;;AAEH,uEAAuE;AACvE;;;;;;GAMG;;AAEH,yCAAyC;;AAEzC;EACE,cAAc;AAChB;;AAEA;EACE,kBAAkB;EAClB,qCAAqC;EACrC,iBAAiB;AACnB;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,aAAa;EACb,+BAA+B;EAC/B,gBAAgB;EAChB,mBAAmB;EACnB,+BAA+B;EAC/B,iCAAiC;EACjC,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;;EAEE,4CAA4C;EAC5C,eAAe;AACjB;;ACvEA;EACE,aAAa;EACb,sBAAsB;EACtB,WAAW;EACX,WAAW;EACX,kBAAkB;AACpB;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,gBAAgB;EAChB,iCAAiC;EACjC,+BAA+B;AACjC;;AAEA;EACE,6CAA6C;EAC7C,mCAAmC;EACnC,kBAAkB;EAClB,QAAQ;AACV;;AAEA;EACE,sBAAsB;EACtB,YAAY;EACZ,gBAAgB;EAChB,+BAA+B;EAC/B,uCAAuC;EACvC,qBAAqB;EACrB,+CAA+C;EAC/C,kBAAkB;EAClB,gBAAgB;EAChB,uCAAuC;EACvC,oCAAoC;EACpC;yBACuB;;EAEvB;IACE,oDAAoD;EACtD;AACF;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,aAAa;EACb,+CAA+C;;EAE/C;IACE,+CAA+C;IAC/C,gDAAgD;IAChD,mBAAmB;EACrB;AACF;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,yCAAyC;AAC3C;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,QAAQ;EACR,qCAAqC;EACrC,oCAAoC;AACtC;;AAEA;EACE,+BAA+B;EAC/B,+BAA+B;AACjC;;AAEA;EACE,sCAAsC;AACxC;;ACnFA;EACE,aAAa;EACb,YAAY;EACZ,4BAA4B;EAC5B,sBAAsB;EACtB,uBAAuB;EACvB,SAAS;EACT,kBAAkB;EAClB,0CAA0C;EAC1C,uCAAuC;EACvC,gDAAgD;EAChD,gBAAgB;EAChB,kBAAkB;;EAElB;IACE,kBAAkB;IAClB,MAAM;IACN,UAAU;IACV,uCAAuC;IACvC,kBAAkB;IAClB,gBAAgB;IAChB,2CAA2C;IAC3C,mBAAmB;IACnB,yBAAyB;IACzB,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,iBAAiB;IACjB,uBAAuB;IACvB,QAAQ;IACR,8BAA8B;IAC9B;;;;;UAKM;EACR;;EAEA;IACE;MACE,kBAAkB;;MAElB;QACE,aAAa;QACb,qBAAqB;QACrB,6BAA6B;QAC7B,iCAAiC;QACjC,gBAAgB;MAClB;;MAEA;QACE,iCAAiC;QACjC,qCAAqC;QACrC,kBAAkB;QAClB,gBAAgB;QAChB,yCAAyC;QACzC,sBAAsB;MACxB;IACF;EACF;;EAEA;IACE,aAAa;IACb,mBAAmB;IACnB,SAAS;;IAET;MACE,WAAW;MACX,WAAW;IACb;;IAEA;MACE,0CAA0C;IAC5C;;IAEA;MACE,0CAA0C;IAC5C;EACF;AACF;;AChFA;EACE,aAAa;EACb,mBAAmB;EACnB,SAAS;EACT,qBAAqB;EACrB,kBAAkB;EAClB,oCAAoC;EACpC,eAAe;EACf,YAAY;EACZ,2BAA2B;EAC3B,gEAAgE;EAChE,UAAU;EACV,gBAAgB;AAClB;;AAEA;EACE,wBAAwB;EACxB,UAAU;AACZ;;AAEA;EACE,2BAA2B;EAC3B,UAAU;AACZ;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,SAAS;EACT,WAAW;AACb;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,YAAY;AACd;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,eAAe;EACf,oCAAoC;EACpC,iBAAiB;AACnB;;AAEA;;EAEE,+BAA+B;EAC/B,mCAAmC;EACnC,sBAAsB;EACtB,SAAS;AACX;;AAEA;EACE,aAAa;EACb,SAAS;AACX;;AAEA;EACE,+CAA+C;EAC/C,gDAAgD;AAClD;;AAEA;EACE,gDAAgD;EAChD,iDAAiD;AACnD;;AAEA;EACE,8CAA8C;EAC9C,+CAA+C;AACjD;;AAEA;EACE;IACE,wBAAwB;IACxB,UAAU;EACZ;;EAEA;IACE,2BAA2B;IAC3B,UAAU;EACZ;AACF;;AAEA;EACE;IACE,gBAAgB;EAClB;AACF;;AC7FA;EACE,aAAa;EACb,mBAAmB;EACnB,8BAA8B;EAC9B,mBAAmB;EACnB,oBAAoB;AACtB;;AAEA;EACE,uDAAuD;AACzD;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,mBAAmB;EACnB,oCAAoC;EACpC,gBAAgB;AAClB;;AAEA;EACE,oBAAoB;EACpB,+BAA+B;EAC/B,mCAAmC;EACnC,oCAAoC;AACtC;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,0CAA0C;AAC5C;;ACnCA;EACE,kBAAkB;EAClB,WAAW;AACb;;AAEA;EACE,cAAc;EACd,WAAW;EACX,uCAAuC;EACvC,+BAA+B;EAC/B,oCAAoC;EACpC,kBAAkB;EAClB,uDAAuD;EACvD,4CAA4C;EAC5C,oBAAoB;EACpB,cAAc;EACd,4EAA4E;AAC9E;;AAEA;EACE,6CAA6C;AAC/C;;AAEA;EACE,oDAAoD;AACtD;;AAEA;;EAEE,2CAA2C;EAC3C,aAAa;AACf;;AAEA;EACE,gDAAgD;EAChD,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,iBAAiB;EACjB,kBAAkB;EAClB,UAAU;EACV,QAAQ;EACR,2BAA2B;EAC3B,oBAAoB;AACtB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,uCAAuC;EACvC,gDAAgD;AAClD;;AAEA;EACE,uCAAuC;EACvC,+CAA+C;EAC/C,iBAAiB;EACjB,gBAAgB;EAChB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,qBAAqB;EACrB,mBAAmB;EACnB,oCAAoC;EACpC,gBAAgB;EAChB,aAAa;EACb,mBAAmB;EACnB,WAAW;AACb;;AAEA;EACE,4CAA4C;EAC5C,eAAe;AACjB;;AAEA;EACE,qBAAqB;EACrB,oCAAoC;EACpC,kBAAkB;AACpB;;AAEA;EACE,mBAAmB;EACnB,qBAAqB;EACrB,WAAW;EACX,gBAAgB;EAChB,kBAAkB;EAClB,mBAAmB;EACnB,UAAU;AACZ;;AClGA;EACE,aAAa;EACb,WAAW;EACX,gBAAgB;EAChB,uCAAuC;EACvC,iDAAiD;AACnD;;AAEA;EACE,uCAAuC;EACvC,6BAA6B;EAC7B,0CAA0C;EAC1C,kBAAkB;EAClB,eAAe;EACf,eAAe;AACjB;;AAEA;EACE,uDAAuD;EACvD,kDAAkD;AACpD;;AAEA;EACE,uDAAuD;EACvD,kDAAkD;AACpD;;ACzBA;EACE,+BAA+B;AACjC;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,WAAW;AACb;;AAEA;EACE,kBAAkB;EAClB,oDAAoD;EACpD,kBAAkB;EAClB,eAAe;EACf,WAAW;EACX,uCAAuC;;EAEvC;IACE,oDAAoD;EACtD;AACF;;AAEA;EACE,+CAA+C;EAC/C,uCAAuC;AACzC;;AAEA;EACE,+CAA+C;;EAE/C;IACE,gDAAgD;EAClD;AACF;;AAEA;;EAEE,iDAAiD;AACnD;;AAEA;EACE,+BAA+B;EAC/B,aAAa;EACb,kBAAkB;EAClB,sDAAsD;EACtD,cAAc;AAChB;;AAEA;EACE,qCAAqC;AACvC;;AAEA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,oBAAoB;EACpB,6CAA6C;EAC7C,mCAAmC;EACnC,qCAAqC;AACvC;;AAEA;EACE,WAAW;EACX,aAAa;EACb,sBAAsB;EACtB,+BAA+B;EAC/B,gBAAgB;EAChB,+CAA+C;EAC/C,kBAAkB;EAClB,gBAAgB;EAChB,sBAAsB;EACtB,YAAY;EACZ,aAAa;EACb,uCAAuC;EACvC,oCAAoC;AACtC;;AAEA;EACE,aAAa;;EAEb;IACE,2CAA2C;IAC3C,0CAA0C;EAC5C;AACF;;AAEA;EACE,aAAa;EACb,oBAAoB;EACpB,SAAS;EACT,oCAAoC;AACtC;;AAEA;EACE,qCAAqC;EACrC,yCAAyC;EACzC,6CAA6C;EAC7C,kBAAkB;AACpB;;AAEA;EACE,sCAAsC;AACxC;;ACxGA;EACE,WAAW;EACX,kBAAkB;EAClB,oBAAoB;AACtB;;AAEA;EACE,SAAS;EACT,0CAA0C;EAC1C,mCAAmC;EACnC,kBAAkB;AACpB;;AAEA;EACE,qBAAqB;EACrB,iBAAiB;AACnB;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,0BAA0B;AAC5B;;AAEA;EACE,uDAAuD;EACvD,uCAAuC;EACvC,gBAAgB;EAChB,kBAAkB;AACpB;;AAEA;EACE,uDAAuD;EACvD,uCAAuC;AACzC;;AAEA;EACE,sBAAsB;EACtB,2BAA2B;EAC3B,+BAA+B;EAC/B,gBAAgB;EAChB,kBAAkB;AACpB;;AAEA;EACE,sBAAsB;EACtB,2BAA2B;EAC3B,+BAA+B;AACjC;;AC1DA;EACE,SAAS;EACT,YAAY;EACZ,sCAAsC;EACtC,uDAAuD;EACvD,kBAAkB;EAClB,iBAAiB;EACjB,gBAAgB;EAChB,0CAA0C;EAC1C,YAAY;EACZ,gBAAgB;AAClB;;AAEA;EACE,YAAY;EACZ,4CAA4C;EAC5C,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,iBAAiB;EACjB,sCAAsC;EACtC,qEAAqE;AACvE;;AAEA;EACE,uDAAuD;EACvD,YAAY;AACd;;AAEA;EACE,+CAA+C;EAC/C,iCAAiC;AACnC;;AClCA;EACE,aAAa;EACb,sBAAsB;EACtB,eAAe;EACf,uCAAuC;AACzC;;AAEA;EACE,uBAAuB;EACvB,2DAA2D;EAC3D,wDAAwD;EACxD,aAAa;AACf;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,WAAW;EACX,iBAAiB;AACnB;;AAEA;EACE,aAAa;EACb,uBAAuB;EACvB,SAAS;EACT,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,SAAS;AACX;;AAEA;EACE,iBAAiB;EACjB,aAAa;EACb,WAAW;AACb;;AAEA;EACE,aAAa;EACb,uBAAuB;EACvB,SAAS;EACT,oBAAoB;AACtB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,YAAY;EACZ,YAAY;EACZ,aAAa;EACb,sBAAsB;AACxB;;AAEA;EACE,aAAa;EACb,WAAW;AACb;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,WAAW;AACb;;AAEA;EACE,0CAA0C;EAC1C,qCAAqC;EACrC,mCAAmC;EACnC,gBAAgB;EAChB,SAAS;AACX;;AAEA;EACE,wCAAwC;EACxC,qCAAqC;EACrC,mCAAmC;EACnC,gBAAgB;EAChB,SAAS;AACX;;AAEA;EACE,oCAAoC;EACpC,+BAA+B;EAC/B,mCAAmC;EACnC,gBAAgB;AAClB;;AAEA;EACE,0CAA0C;EAC1C,qCAAqC;EACrC,mCAAmC;EACnC,4BAA4B;AAC9B;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,aAAa;EACb,sBAAsB;EACtB,mBAAmB;EACnB,uBAAuB;EACvB,WAAW;EACX,eAAe;EACf,kBAAkB;EAClB,oDAAoD;;EAEpD;IACE,8BAA8B;EAChC;AACF;;AAEA;EACE,+BAA+B;EAC/B,mCAAmC;EACnC,6CAA6C;EAC7C,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;AACtB;;AAEA;EACE;IACE;MACE,4CAA4C;IAC9C;EACF;AACF;;AC3IA;EACE,uCAAuC;EACvC,aAAa;EACb,sBAAsB;EACtB,oDAAoD;AACtD;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,8BAA8B;AAChC;;AAEA;EACE,aAAa;EACb,WAAW;EACX,mBAAmB;EACnB,oCAAoC;EACpC,gBAAgB;EAChB,eAAe;EACf,UAAU;;EAEV;IACE,0BAA0B;EAC5B;AACF;;AAEA;EACE,aAAa;EACb,mBAAmB;EACnB,WAAW;EACX,gBAAgB;EAChB,eAAe;EACf,UAAU;AACZ;;AAEA;EACE,oCAAoC;EACpC,gBAAgB;EAChB,eAAe;AACjB;;AAEA;EACE,eAAe;EACf,aAAa;AACf;;AAEA;EACE,mBAAmB;EACnB,oBAAoB;EACpB,mBAAmB;AACrB;;AAEA;EACE,WAAW;AACb;;AAEA;EACE,aAAa;AACf;;AAEA;EACE,gBAAgB;EAChB,WAAW;EACX,uBAAuB;EACvB,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;EACnB,iCAAiC;;EAEjC;IACE,oDAAoD;EACtD;AACF;;AAEA;EACE,kBAAkB;EAClB,gBAAgB;EAChB,oCAAoC;EACpC,SAAS;EACT,mCAAmC;AACrC;;AAEA;EACE,aAAa;EACb,SAAS;EACT,WAAW;AACb;;AAEA;EACE,gBAAgB;EAChB,oCAAoC;AACtC;;AAEA;EACE,SAAS;EACT,6CAA6C;EAC7C,gBAAgB;AAClB;;AAEA;EACE,aAAa;EACb,qBAAqB;EACrB,6CAA6C;EAC7C,gBAAgB;AAClB;;AAEA;;EAEE,kBAAkB;EAClB,oBAAoB;AACtB;;AAEA;;EAEE,kBAAkB;EAClB,uBAAuB;AACzB;;AAEA;EACE,oBAAoB;EACpB,gBAAgB;EAChB,UAAU;AACZ;;AAEA;EACE,iBAAiB;AACnB;;AAEA;EACE,kBAAkB;EAClB,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;EAClB,uBAAuB;AACzB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,UAAU;EACV,YAAY;EACZ,sCAAsC;EACtC,SAAS;EACT,2BAA2B;AAC7B;;AAEA;EACE,kBAAkB;AACpB;AACA;EACE,kBAAkB;EAClB,aAAa;AACf;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,kBAAkB;EAClB,YAAY;EACZ,SAAS;EACT,uBAAuB;EACvB,YAAY;EACZ,YAAY;EACZ,YAAY;EACZ,+CAA+C;AACjD;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,6BAA6B;EAC7B,gBAAgB;EAChB,eAAe;AACjB;;AAEA;EACE,aAAa;EACb,6BAA6B;AAC/B;;AAEA;EACE,uBAAuB;AACzB;;AAEA;EACE,gBAAgB;EAChB,eAAe;EACf,iBAAiB;EACjB,kBAAkB;EAClB,sBAAsB;EACtB,cAAc;AAChB;;AAEA;EACE,gBAAgB;EAChB,eAAe;EACf,iBAAiB;EACjB,sBAAsB;EACtB,sBAAsB;EACtB,cAAc;AAChB;;AAEA;EACE,oBAAoB;EACpB,sBAAsB;AACxB", "file": "uikit.cjs.development.css", "sourcesContent": [".attachmentHistoryWrapper {\r\n  background-color: var(--color-bg-white);\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  padding: 1rem;\r\n  border-radius: 2px;\r\n\r\n  strong {\r\n    color: var(--color-primary-charcoal);\r\n    line-height: var(--lineheight-body);\r\n  }\r\n\r\n  .leftIconWrapper {\r\n    width: min-content;\r\n    padding: 0.5rem;\r\n    background-color: var(--color-bg-light-grey);\r\n    border-radius: 2px;\r\n\r\n    svg {\r\n      path {\r\n        fill: var(--color-primary-slate);\r\n      }\r\n    }\r\n  }\r\n\r\n  .historyItems {\r\n    margin: 1rem 0 0;\r\n    padding: 0;\r\n\r\n    .leftHandInfo,\r\n    .rightHandInfo,\r\n    .historyItem {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n    }\r\n\r\n    > :first-child {\r\n      border-top: 1px solid var(--color-primary-pale-charcoal);\r\n      border-top-left-radius: 2px;\r\n      border-top-right-radius: 2px;\r\n    }\r\n\r\n    > :last-child {\r\n      border-bottom-left-radius: 2px;\r\n      border-bottom-right-radius: 2px;\r\n    }\r\n\r\n    .historyItem {\r\n      align-items: center;\r\n      border-left: 1px solid var(--color-primary-pale-charcoal);\r\n      border-right: 1px solid var(--color-primary-pale-charcoal);\r\n      border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n\r\n      column-gap: 0.75rem;\r\n      display: grid;\r\n      grid-template-columns: 1fr auto;\r\n      padding: 0.75rem 1rem;\r\n\r\n      .leftHandInfo {\r\n        gap: 0.75rem;\r\n        min-width: 0;\r\n        min-height: 0;\r\n\r\n        .pipe {\r\n          color: var(--color-border-grey);\r\n          margin: 0 0.5rem;\r\n        }\r\n\r\n        .basicInfo {\r\n          color: var(--color-bg-light-charcoal);\r\n        }\r\n      }\r\n\r\n      .rightHandInfo {\r\n        color: var(--color-bg-inactive-charcoal);\r\n        gap: 0.5rem;\r\n\r\n        svg {\r\n          cursor: pointer;\r\n          color: var(--color-primary-charcoal);\r\n        }\r\n      }\r\n\r\n      .rightHandInfo--primary {\r\n        svg {\r\n          color: var(--color-bg-white);\r\n        }\r\n      }\r\n\r\n      .rightHandInfo--secondary {\r\n        svg {\r\n          color: var(--color-primary-charcoal);\r\n        }\r\n      }\r\n\r\n      .documentName {\r\n        color: var(--color-primary-charcoal);\r\n        font-size: var(--fontsize-body);\r\n        line-height: var(--lineheight-body);\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .historyItem {\r\n    min-width: 520px;\r\n  }\r\n}\r\n\r\n.nameContainer {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--fontsize-body-small);\r\n  color: var(--color-bg-inactive-charcoal);\r\n  justify-self: end;\r\n  white-space: nowrap;\r\n}\r\n\r\n.title {\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n}\r\n\r\n.downloadIcon {\r\n  padding: 0.5rem;\r\n  border-radius: 50%;\r\n\r\n  &:hover {\r\n    background-color: var(--color-bg-light-grey);\r\n  }\r\n\r\n  &:focus-visible {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: 2px;\r\n  }\r\n}\r\n\r\n.noDocumentsUploaded {\r\n  padding: 1rem 1.5rem;\r\n  border-radius: 2px;\r\n  background-color: #fafafa;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-primary-inactive-charcoal);\r\n  text-align: center;\r\n  margin-top: 1rem;\r\n}\r\n", ".iconComponent {\r\n  display: flex;\r\n  align-items: center;\r\n}", ".button {\r\n  border-radius: 2px;\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-style: normal;\r\n  font-weight: 600;\r\n  line-height: var(--lineheight-body);\r\n  gap: 0.5rem;\r\n  font-size: var(--fontsize-body);\r\n  cursor: pointer;\r\n  font-family: \"Proxi<PERSON><PERSON><PERSON>\", Aria<PERSON>, sans-serif;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.button:focus-visible {\r\n  outline-offset: 2px;\r\n  outline: 3px solid var(--color-secondary-cobalt);\r\n}\r\n\r\n.button.large {\r\n  padding: 0.5rem 1.5rem;\r\n  height: 56px;\r\n}\r\n\r\n.button.small {\r\n  padding: 0.5rem 1rem;\r\n  height: 40px;\r\n}\r\n\r\n.button.long {\r\n  width: 208px;\r\n  height: 44px;\r\n  text-align: left;\r\n}\r\n\r\n.button.primary:disabled,\r\n.button.primary:disabled:hover {\r\n  cursor: not-allowed;\r\n  background-color: var(--color-primary-pale-charcoal);\r\n  color: var(--color-bg-inactive-charcoal);\r\n  path {\r\n    fill: var(--color-bg-inactive-charcoal);\r\n  }\r\n}\r\n\r\n.button.primary {\r\n  border: none;\r\n  color: var(--color-bg-white);\r\n  background-color: var(--color-primary-red);\r\n}\r\n\r\n.button.primary:hover {\r\n  background-color: var(--color-primary-slate);\r\n}\r\n\r\n.button.secondary {\r\n  border: 1px solid var(--color-primary-charcoal);\r\n  background-color: var(--color-bg-white);\r\n  color: var(--color-primary-charcoal);\r\n  path {\r\n    fill: var(--color-primary-charcoal);\r\n  }\r\n}\r\n\r\n.button.secondary:hover {\r\n  background-color: var(--color-primary-charcoal);\r\n  color: var(--color-bg-white);\r\n  path {\r\n    fill: var(--color-bg-white);\r\n  }\r\n}\r\n\r\n.button.secondary:disabled,\r\n.button.secondary:disabled:hover {\r\n  cursor: not-allowed;\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  color: var(--color-bg-inactive-charcoal);\r\n  background-color: transparent;\r\n  path {\r\n    fill: var(--color-bg-inactive-charcoal);\r\n  }\r\n}\r\n\r\n.button.tertiary {\r\n  border: none;\r\n  color: var(--color-primary-charcoal);\r\n  background-color: transparent;\r\n  path {\r\n    fill: var(--color-primary-charcoal);\r\n  }\r\n\r\n  &:hover {\r\n    text-decoration: underline;\r\n  }\r\n}\r\n\r\n.button.tertiary:disabled,\r\n.button.tertiary:disabled:hover {\r\n  cursor: not-allowed;\r\n  color: var(--color-primary-inactive-charcoal);\r\n  path {\r\n    fill: var(--color-primary-inactive-charcoal);\r\n  }\r\n}\r\n\r\n.button.warningButton {\r\n  color: var(--color-secondary-burgundy);\r\n  svg {\r\n    path {\r\n      fill: var(--color-secondary-burgundy);\r\n    }\r\n  }\r\n}\r\n\r\n.iconWrapper {\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.iconWrapper.small {\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.button.border {\r\n  border: 1px solid var(--color-primary-charcoal);\r\n}\r\n.button.noBorder {\r\n  border: none;\r\n}\r\n\r\n.isHighLighted {\r\n  background-color: var(--color-bg-light-grey) !important;\r\n  color: var(--color-primary-light-charcoal) !important;\r\n}\r\n\r\n.spinner {\r\n  display: flex;\r\n  padding: 0 1rem;\r\n}\r\n\r\n.spinnerWithLabel {\r\n  padding: 0;\r\n}\r\n", ".loader {\r\n  border: 5px solid var(--color-primary-red);\r\n  border-top-color: var(--color-border-white);\r\n  border-radius: 50%;\r\n  display: inline-block;\r\n  box-sizing: border-box;\r\n  animation: rotation 1s linear infinite;\r\n}\r\n\r\n.dropdownLoader {\r\n  border-color: var(--color-primary-red);\r\n  border-top-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.tableLoader {\r\n  border-color: var(--color-primary-red);\r\n  border-top-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.buttonLoader {\r\n  border-color: var(--color-primary-red);\r\n  border-top-color: var(--color-primary-pale-charcoal);\r\n}\r\n\r\n@keyframes rotation {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.spinnerWrapper {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.spinnerWrapper::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: inherit;\r\n  z-index: var(--z-index-spinner);\r\n}\r\n\r\n.spinnerCentered {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  z-index: var(--z-index-spinner);\r\n}\r\n", ".chip {\r\n  display: grid;\r\n  grid-template-columns: auto 1fr auto;\r\n  align-items: center;\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-border-grey);\r\n  text-wrap-mode: nowrap;\r\n}\r\n\r\n.chip--priority,\r\n.chip--request {\r\n  padding: 0.25rem 0.5rem 0.25rem 0.25rem;\r\n  background-color: var(--color-bg-light-grey);\r\n  border: none;\r\n}\r\n\r\n.chip--readOnly {\r\n  padding: 0.125rem 0.25rem;\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-border-grey);\r\n  gap: 0.25rem;\r\n  width: fit-content;\r\n  background-color: transparent;\r\n}\r\n\r\n.chip--warning {\r\n  padding: 0.25rem 0.5rem;\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-secondary-gold);\r\n}\r\n\r\n.chip--error {\r\n  border: none;\r\n  border-radius: 0;\r\n  border-bottom: 1px solid var(--color-secondary-burgundy);\r\n  padding: 0.25rem 0;\r\n}\r\n\r\n.chip--action {\r\n  grid-template-columns: auto 1fr;\r\n  justify-content: start;\r\n  border: none;\r\n  border-radius: 100px;\r\n  background-color: var(--color-bg-light-grey);\r\n  color: var(--color-primary-slate);\r\n  transition: all 100ms ease-out;\r\n  width: fit-content;\r\n  padding: 0.125rem 0.5rem 0.125rem 0.25rem;\r\n}\r\n\r\n.chip--location {\r\n  grid-template-columns: auto 1fr;\r\n  justify-content: start;\r\n  column-gap: 0.5rem;\r\n  border: none;\r\n  border-radius: 100px;\r\n  padding: 0.125rem 0.5rem;\r\n  background-color: var(--color-bg-light-grey);\r\n  color: var(--color-primary-slate);\r\n  transition: all 100ms ease-out;\r\n}\r\n\r\n.chip--overview {\r\n  grid-template-columns: auto 1fr;\r\n  justify-content: start;\r\n  column-gap: 0.5rem;\r\n  border: none;\r\n  border-radius: 100px;\r\n  padding: 0.125rem 0.5rem;\r\n  background-color: white;\r\n  color: var(--color-primary-slate);\r\n  transition: all 100ms ease-out;\r\n  border: 1px solid #e7e7e7;\r\n}\r\n\r\n.chip--status {\r\n  align-items: center;\r\n  justify-content: start;\r\n  column-gap: 0.5rem;\r\n  border: none;\r\n  border-radius: 2px;\r\n  padding: 0.125rem 0.5rem;\r\n  background-color: #e5eff8;\r\n  color: var(--color-secondary-cobalt) !important;\r\n  transition: all 100ms ease-out;\r\n}\r\n\r\n.chip--dueDate {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  background-color: var(--color-bg-white);\r\n  border-radius: 6.25rem;\r\n  padding: 0.125rem 0.5rem;\r\n  font-size: var(--fontsize-body-small);\r\n  border: none;\r\n}\r\n\r\n.textOnly {\r\n  grid-template-columns: none;\r\n  padding: 0.25rem 0.5rem;\r\n}\r\n\r\n.chip--location {\r\n  &.interactive {\r\n    &:hover {\r\n      background-color: var(--color-bg-primary-pale-charcoal);\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.buttonReset {\r\n  padding: 0;\r\n\r\n  &:focus-visible {\r\n    outline: none;\r\n\r\n    .chip--location {\r\n      outline: 2px solid var(--color-secondary-cobalt);\r\n      outline-offset: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n/* TODO: remove when avatar component used when available */\r\n.avatar {\r\n  display: grid;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--fontsize-body-label);\r\n  line-height: var(--lineheight-body-label);\r\n  width: 1.5rem;\r\n  height: 1.5rem;\r\n  margin-right: 0.5rem;\r\n  border-radius: 9999px;\r\n}\r\n\r\n.avatar--priority,\r\n.avatar--request {\r\n  display: grid;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--fontsize-body-label);\r\n  line-height: var(--lineheight-body-label);\r\n  color: var(--color-bg-white);\r\n  height: 1.25rem;\r\n  border-radius: 2px;\r\n}\r\n\r\n/*\r\nThe two stylings below for request and location chip types are added to combat a bug relating to currentColor\r\nwhere all chip icons were being set to have the color red, these should be removed eventually.\r\n*/\r\n.avatar--request {\r\n  width: 1.25rem;\r\n  svg {\r\n    path {\r\n      fill: white;\r\n    }\r\n  }\r\n}\r\n\r\n/* TODO - Remove if risk colors are fine */\r\n/* .avatar--overview,\r\n.avatar--location {\r\n  svg {\r\n    path {\r\n      fill: var(--color-primary-slate);\r\n    }\r\n  }\r\n} */\r\n\r\n.avatar--priority {\r\n  padding-right: 0.5rem;\r\n  svg {\r\n    path {\r\n      fill: var(--color-primary-red);\r\n    }\r\n  }\r\n}\r\n\r\n.avatar--request:has([aria-label=\"up-arrow\"]) {\r\n  background-color: var(--color-secondary-ocean);\r\n  margin-right: 0.5rem;\r\n}\r\n\r\n.avatar--request:has([aria-label=\"signature-request\"]) {\r\n  background-color: var(--color-secondary-jade);\r\n  margin-right: 0.5rem;\r\n}\r\n\r\n.avatar--warning {\r\n  display: grid;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--fontsize-body-label);\r\n  line-height: var(--lineheight-body-label);\r\n  color: var(--color-bg-white);\r\n  background-color: var(--color-secondary-ocean);\r\n  width: 1.5rem;\r\n  height: 1.5rem;\r\n  margin-right: 0.5rem;\r\n  border-radius: 9999px;\r\n  background-color: var(--color-secondary-gold);\r\n}\r\n\r\n.avatar--error {\r\n  margin-right: 0.5rem;\r\n}\r\n\r\n.avatar--action {\r\n  background-color: none;\r\n  padding-right: 4px;\r\n  padding-left: 4px;\r\n  align-items: center;\r\n  color: var(--color-primary-light-slate);\r\n}\r\n\r\n.avatar--status {\r\n  display: none;\r\n}\r\n\r\n.text {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  font-weight: 600;\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.text--request {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  font-weight: 400;\r\n  color: var(--color-primary-charcoal);\r\n  font-family: var(--primary-font-family);\r\n}\r\n\r\n.text--readOnly {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  font-weight: 400;\r\n  color: var(--color-primary-inactive-charcoal);\r\n  font-family: var(--primary-font-family);\r\n}\r\n\r\n.text--priority {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  font-weight: 400;\r\n  color: var(--color-primary-light-charcoal);\r\n  font-family: var(--primary-font-family);\r\n}\r\n\r\n.text--action {\r\n  font-family: var(--primary-font-family);\r\n  color: var(--color-primary-light-charcoal);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.text--overview,\r\n.text--location {\r\n  font-family: \"ProximaNova\", Arial, sans-serif;\r\n  color: var(--color-primary-light-charcoal);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n}\r\n\r\n.text--location {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n.text--status {\r\n  font-family: \"ProximaNova\", Arial, sans-serif;\r\n  color: var(--color-primary-light-charcoal);\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  line-height: 16px;\r\n  text-align: center;\r\n  color: #0062b8;\r\n}\r\n\r\n.dismissButton {\r\n  display: grid;\r\n  align-items: center;\r\n  margin-left: 0.5rem;\r\n  border: none;\r\n  background: none;\r\n  padding: 0;\r\n  outline: 0;\r\n  cursor: pointer;\r\n  color: var(--color-primary-slate);\r\n  padding: 0.25rem;\r\n  border-radius: 50%;\r\n  transition: background-color 0.2s;\r\n\r\n  &:hover {\r\n    background-color: var(--color-bg-light-grey);\r\n  }\r\n}\r\n", ".avatar {\r\n  border-radius: 50%;\r\n  width: 4rem;\r\n  height: 4rem;\r\n  display: block;\r\n}\r\n\r\n.avatar--x-small {\r\n  height: 1.5rem;\r\n  width: 1.5rem;\r\n  font-size: var(--fontsize-body-xsmall);\r\n}\r\n\r\n.avatar--small {\r\n  height: 2rem;\r\n  width: 2rem;\r\n  font-size: var(--fontsize-body-xsmall);\r\n}\r\n\r\n.avatar--small-medium {\r\n  height: 2.5rem;\r\n  width: 2.5rem;\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.avatar--medium-small {\r\n  height: 3.5rem;\r\n  width: 3.5rem;\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.avatar--medium {\r\n  height: 4rem;\r\n  width: 4rem;\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.avatar--large {\r\n  height: 7.5rem;\r\n  width: 7.5rem;\r\n  font-weight: 600;\r\n  font-size: var(--fontsize-h5);\r\n}\r\n\r\n.avatar--x-large {\r\n  height: 9.375rem;\r\n  width: 9.375rem;\r\n  font-weight: 600;\r\n  font-size: var(--fontsize-h5);\r\n}\r\n\r\n.clickable {\r\n  cursor: pointer;\r\n}\r\n\r\n.fallback {\r\n  background-color: var(--color-bg-primary-pale-charcoal);\r\n  color: var(--color-primary-charcoal);\r\n  border-radius: 50%;\r\n  width: 3rem;\r\n  height: 3rem;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: var(--fontsize-body-small);\r\n  font-weight: 600;\r\n}\r\n\r\n.fallback--chip {\r\n  font-size: var(--fontsize-body-xsmall);\r\n  width: 1rem;\r\n  height: 1rem;\r\n}\r\n\r\n.fallback--x-small {\r\n  font-size: 0.75rem;\r\n  width: 1.5rem !important;\r\n  height: 1.5rem !important;\r\n}\r\n\r\n.fallback--small {\r\n  font-size: 1rem;\r\n  width: 2rem;\r\n  height: 2rem;\r\n}\r\n\r\n.fallback-small-medium {\r\n  height: 2.5rem;\r\n  width: 2.5rem;\r\n}\r\n\r\n.fallback-medium-small {\r\n  font-size: 1.25rem;\r\n  height: 3.5rem;\r\n  width: 3.5rem;\r\n}\r\n\r\n.fallbackBorder {\r\n  border: 1px solid var(--color-primary-white);\r\n}\r\n\r\n.initials {\r\n  margin: 0;\r\n  user-select: none;\r\n}\r\n\r\n.initials--x-small {\r\n  font-size: var(--fontsize-body-xsmall);\r\n  font-weight: 600;\r\n}\r\n\r\n.initials--small {\r\n  font-size: var(--fontsize-body-small);\r\n  font-weight: 600;\r\n}\r\n\r\n.initials--large {\r\n  font-size: var(--fontsize-h6);\r\n}\r\n\r\n.initials--bold {\r\n  font-weight: 600;\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .fallback {\r\n    width: 4rem;\r\n    height: 4rem;\r\n  }\r\n\r\n  .fallback--chip {\r\n    width: 1.5rem;\r\n    height: 1.5rem;\r\n  }\r\n\r\n  .fallback--small {\r\n    width: 2rem;\r\n    height: 2rem;\r\n  }\r\n\r\n  .fallback--small-medium {\r\n    font-size: 1.25rem;\r\n    height: 2.5rem;\r\n    width: 2.5rem;\r\n  }\r\n\r\n  .fallback--medium-small {\r\n    height: 3.5rem;\r\n    width: 3.5rem;\r\n  }\r\n}\r\n", ".accordion {\r\n  width: 100%;\r\n  min-width: 22rem;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .accordionHeader {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    padding: 1rem;\r\n    gap: 1rem;\r\n    border-top: 1px solid var(--color-primary-pale-charcoal);\r\n    border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n    cursor: pointer;\r\n    border-radius: 0;\r\n\r\n    .title {\r\n      font-size: var(--fontsize-body-small);\r\n      font-weight: 600;\r\n      line-height: var(--lineheight-body);\r\n      color: var(--color-primary-charcoal);\r\n    }\r\n\r\n    .subtitle {\r\n      color: var(--color-primary-charcoal);\r\n      font-size: 0.875rem;\r\n    }\r\n  }\r\n\r\n  .accordionHeader:hover {\r\n    background-color: #f5f5f5;\r\n  }\r\n\r\n  .chevronWrapper {\r\n    svg {\r\n      path {\r\n        fill: var(--color-primary-slate);\r\n      }\r\n    }\r\n  }\r\n\r\n  [data-scope=\"accordion\"][data-part=\"chevron\"][data-state=\"open\"] {\r\n    transform: rotate(90deg);\r\n    transition: transform 100ms ease-in-out;\r\n  }\r\n\r\n  [data-scope=\"accordion\"][data-part=\"chevron\"][data-state=\"closed\"] {\r\n    transition: transform 100ms ease-in-out;\r\n  }\r\n\r\n  [data-scope=\"accordion\"][data-part=\"content\"] {\r\n    background-color: var(--color-bg-white);\r\n    border-top: 1px solid var(--color-primary-pale-charcoal);\r\n    border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n    border-top: none;\r\n    padding: 1rem 1rem 1rem 3rem;\r\n  }\r\n\r\n  [data-scope=\"accordion\"][data-part=\"content\"][data-state=\"open\"] {\r\n    opacity: 1;\r\n    display: flex;\r\n    visibility: visible;\r\n    transition: all 100ms ease-in-out;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: flex-start;\r\n    gap: 1rem;\r\n    align-self: stretch;\r\n  }\r\n\r\n  [data-scope=\"accordion\"][data-part=\"content\"][data-state=\"closed\"] {\r\n    opacity: 0;\r\n    height: 0;\r\n    padding: 0 1rem 0 3rem;\r\n    visibility: hidden;\r\n    transition: all 100ms ease-in-out;\r\n  }\r\n}\r\n", ".tooltip {\r\n  background-color: var(--color-primary-charcoal);\r\n  color: var(--color-primary-pale-charcoal);\r\n  border-radius: 3px;\r\n  font-size: var(--fontsize-body);\r\n  font-family: \"Proxima<PERSON><PERSON>\", Aria<PERSON>, sans-serif;\r\n  line-height: var(--lineheight-body-small);\r\n  padding: 16px;\r\n  max-width: 235px;\r\n  z-index: var(--z-index-tooltip);\r\n  white-space: pre-line;\r\n}\r\n\r\n.smallSize {\r\n  padding: 4px 8px;\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.wide {\r\n  max-width: 280px;\r\n}\r\n\r\n.arrow {\r\n  --arrow-size: 9px;\r\n  --arrow-background: var(--color-primary-charcoal);\r\n}\r\n", ".inputWrapper {\r\n  width: 100%;\r\n  position: relative;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.inputField {\r\n  background-color: var(--color-bg-white);\r\n  width: 100%;\r\n  padding: 1rem;\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 2px;\r\n  color: var(--color-primary-charcoal);\r\n  font-size: var(--fontsize-body);\r\n  font-family: inherit;\r\n  line-height: var(--lineheight-body);\r\n  height: 48px;\r\n  box-sizing: border-box;\r\n  transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-soft-charcoal);\r\n  }\r\n}\r\n\r\n.inputField:read-only {\r\n  cursor: pointer;\r\n}\r\n\r\n.inputThin {\r\n  height: auto;\r\n  padding: 0.5rem;\r\n}\r\n\r\n.searchTerm {\r\n  border: 1px solid var(--color-primary-charcoal);\r\n}\r\n\r\n.inputField:disabled {\r\n  background-color: var(--color-bg-light-grey);\r\n  border-color: var(--color-border-pale-grey);\r\n  color: var(--color-bg-inactive-charcoal);\r\n  cursor: not-allowed;\r\n}\r\n\r\n.baseAlign {\r\n  padding: 1rem;\r\n  padding-top: 2.25rem;\r\n}\r\n\r\n.centerAlign {\r\n  padding: 1.625rem 1rem;\r\n}\r\n\r\n.inputField:focus {\r\n  outline: none;\r\n  border: 1px solid var(--color-primary-charcoal);\r\n\r\n  &:global(.focusedByKeyboard) {\r\n    border: 1px solid var(--color-border-pale-grey);\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: 2px;\r\n  }\r\n}\r\n\r\n.disappearingLabel,\r\n.floatingLabel {\r\n  position: absolute;\r\n  top: 1rem;\r\n  left: 1rem;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-bg-inactive-charcoal);\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n\r\n.floatingLabel {\r\n  transition: 0.2s ease-in-out;\r\n}\r\n\r\n.disappearingLabel {\r\n  visibility: visible;\r\n  opacity: 1;\r\n  transition: opacity 2s linear;\r\n  transform: translate(15px, -10px);\r\n}\r\n\r\n.floatingLabelActive {\r\n  top: 8px;\r\n  font-size: var(--fontsize-body-xsmall);\r\n  line-height: var(--lineheight-body-xsmall);\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.inputWrapper:focus-within .floatingLabel,\r\n.inputWrapper input:not(:placeholder-shown) + .floatingLabel {\r\n  top: 8px;\r\n  font-size: var(--fontsize-body-xsmall);\r\n  line-height: var(--lineheight-body-xsmall);\r\n}\r\n\r\n.hidden {\r\n  display: none;\r\n}\r\n\r\n.inputError {\r\n  border: 1px solid var(--color-primary-red);\r\n}\r\n\r\n.errorTextContainer {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  color: var(--color-secondary-burgundy);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  margin-top: 0.75rem;\r\n}\r\n\r\n.additionalErrorMessage {\r\n  color: var(--color-secondary-burgundy);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n}\r\n\r\n.errorText {\r\n  margin: 0;\r\n  line-height: 1;\r\n  text-align: left;\r\n}\r\n\r\n.iconWrapper {\r\n  position: absolute;\r\n  color: var(--color-primary-slate);\r\n  top: 16px;\r\n  left: 10px;\r\n  transition: opacity 2s linear;\r\n}\r\n\r\n.requiredFieldsTextAsterisk {\r\n  font-size: var(--fontsize-body);\r\n  color: var(--color-primary-red);\r\n}\r\n\r\n.searchIconWrapper {\r\n  position: absolute;\r\n  right: 25px;\r\n  top: 14px;\r\n\r\n  svg {\r\n    path {\r\n      fill: var(--color-primary-charcoal);\r\n    }\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .inputWrapper {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n", ".autoCompleteWrapper {\r\n  position: relative;\r\n  width: 90%;\r\n}\r\n\r\n.autoCompleteInputWrapper {\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.autoCompleteInput {\r\n  background-color: var(--color-bg-white);\r\n  width: 100%;\r\n  padding: 1rem;\r\n  padding-top: 2.25rem;\r\n  border: 1px solid var(--color-border-grey);\r\n  border-radius: 2px;\r\n  color: var(--color-primary-charcoal);\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  height: 48px;\r\n  box-sizing: border-box;\r\n  transition: border-color 0.2s ease-in-out;\r\n}\r\n\r\n.autoCompleteInput:focus-visible {\r\n  outline: none;\r\n  border: 1px solid var(--color-primary-charcoal);\r\n}\r\n\r\n.floatingLabel {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 1rem;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-bg-inactive-charcoal);\r\n  transform: translateY(-50%);\r\n  pointer-events: none;\r\n  transition: 0.2s ease-in-out;\r\n  z-index: 1;\r\n}\r\n\r\n.floatingLabelActive {\r\n  top: 15px;\r\n  font-size: var(--fontsize-body-xsmall);\r\n  line-height: var(--lineheight-body-xsmall);\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.autoCompleteInputWrapper:focus-within .floatingLabel,\r\n.autoCompleteInputWrapper input:not(:placeholder-shown) + .floatingLabel {\r\n  top: 15px;\r\n  font-size: var(--fontsize-body-xsmall);\r\n  line-height: var(--lineheight-body-xsmall);\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.autoCompleteDropdown {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 0.5rem;\r\n  width: 100%;\r\n  max-width: 100%;\r\n  padding: 1rem;\r\n  background-color: var(--color-bg-white);\r\n  border: 1px solid var(--color-border-grey);\r\n  border-radius: 2px;\r\n  color: black;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.checkMark {\r\n  display: flex;\r\n  position: absolute;\r\n  right: 10px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n", ".avatarCard {\r\n  align-items: center;\r\n  display: flex;\r\n  background-color: var(--color-bg-white);\r\n  border-radius: 8px;\r\n  gap: 32px;\r\n  width: 100%;\r\n  max-width: 574px;\r\n  max-height: 100px;\r\n  color:var(--color-primary-charcoal);\r\n}\r\n\r\n.name {\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n  margin: 0;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.cardInfo p {\r\n  margin: 0;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n}\r\n\r\n.cardInfoItem {\r\n  align-items: center;\r\n  padding-bottom: 0.25rem;\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n@media (min-width: 1025px) {\r\n  .avatarCard {\r\n    padding: 1.5rem;\r\n    box-shadow: 0px 4px 24px 0px #00000026;\r\n    max-height: 136px;\r\n  }\r\n\r\n  .name {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n}\r\n", ".buttonDropdownWrapper {\r\n  position: relative;\r\n  z-index: var(--z-index-btn-dropdown);\r\n\r\n  [data-scope=\"menu\"][data-part=\"trigger\"] {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    border-radius: 2px;\r\n    padding: 0.5rem 1rem;\r\n    color: var(--color-primary-charcoal);\r\n    line-height: var(--lineheight-body);\r\n    border: 1px solid var(--color-primary-charcoal);\r\n    font-weight: 600;\r\n    background-color: var(--color-primary-soft-charcoal);\r\n    transition: 0.2s background-color;\r\n    outline: none;\r\n\r\n    svg {\r\n      path {\r\n        fill: var(--color-primary-charcoal);\r\n      }\r\n    }\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"trigger\"]:hover {\r\n    color: var(--color-bg-white);\r\n    background-color: var(--color-primary-charcoal);\r\n    cursor: pointer;\r\n\r\n    svg {\r\n      path {\r\n        fill: var(--color-bg-white);\r\n      }\r\n    }\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"trigger\"]:focus-visible {\r\n    margin: 0;\r\n    border: 1px solid var(--color-primary-charcoal);\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: 2px;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"content\"] {\r\n    top: calc(100% + 0.25rem);\r\n    right: 0;\r\n    border: 0;\r\n    width: 15rem;\r\n    padding: 0;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"item\"] {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: start;\r\n    padding: 1rem;\r\n    gap: 1rem;\r\n    color: var(--color-primary-charcoal);\r\n    cursor: pointer;\r\n    transition: background-color 300ms linear;\r\n    text-wrap-mode: nowrap;\r\n    height: auto;\r\n    width: auto;\r\n\r\n    svg {\r\n      path {\r\n        fill: var(--color-bg-white);\r\n      }\r\n    }\r\n  }\r\n\r\n  .charcoalIconItem {\r\n    svg {\r\n      path {\r\n        fill: var(--color-primary-charcoal) !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"item\"]:hover {\r\n    background-color: var(--color-primary-pale-charcoal);\r\n  }\r\n}\r\n", ".taskType {\r\n  display: grid;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 2px;\r\n  color: var(--color-primary-white);\r\n  width: 1.25rem;\r\n  min-width: 1.25rem;\r\n  height: 1.25rem;\r\n}\r\n\r\n.burgundy {\r\n  background-color: var(--color-secondary-burgundy);\r\n}\r\n\r\n.emerald {\r\n  background-color: var(--color-secondary-emerald);\r\n}\r\n\r\n.ocean {\r\n  background-color: var(--color-secondary-ocean);\r\n}\r\n\r\n.gold {\r\n  background-color: var(--color-secondary-gold);\r\n}\r\n\r\n.jade {\r\n  background-color: var(--color-secondary-jade);\r\n}\r\n", ".selector {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.selectorWrapperRegularRadio,\r\n.selectorWrapper {\r\n  background-color: var(--color-bg-white);\r\n  width: 100%;\r\n}\r\n\r\n.selectorWrapper {\r\n  border: 1px solid var(--color-border-grey);\r\n  border-radius: 2px;\r\n  padding: 1rem;\r\n  min-height: 104px;\r\n}\r\n\r\n.selectorWrapperRegularRadio {\r\n  padding-bottom: 1.5rem;\r\n  padding-right: 1rem;\r\n}\r\n\r\n.selectorWrapperRegularRadio:last-child {\r\n  padding-bottom: 0px;\r\n}\r\n\r\n.selectorWrapper:focus-within {\r\n  &:global(.focusedByKeyboard) {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: 2px;\r\n  }\r\n}\r\n.selectorWrapperActive {\r\n  border-color: var(--color-border-primary-charcoal);\r\n}\r\n\r\n.selectorSelect {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n\r\n.selectorLabel {\r\n  display: none;\r\n}\r\n\r\n.selectorItem {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  height: 100%;\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    .selectorInput {\r\n      border-color: var(--color-primary-charcoal);\r\n    }\r\n  }\r\n}\r\n\r\n.selectorControlWrapper {\r\n  display: flex;\r\n  width: 1.5rem;\r\n  justify-content: center;\r\n}\r\n\r\n.selectorTextRegularRadio,\r\n.selectorText {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  height: 100%;\r\n  flex: 2;\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.selectorText {\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n}\r\n\r\n.selectorTextRegularRadio {\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  margin: 0;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body-small);\r\n}\r\n\r\n.description {\r\n  margin: 0;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  text-align: left;\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.selectorWrapperActive .selectorText .title {\r\n  font-weight: 600;\r\n}\r\n\r\n.selectorInput {\r\n  height: 1.5rem;\r\n  width: 1.5rem;\r\n  border: 1px solid var(--color-border-grey);\r\n  border-radius: 50%;\r\n  position: relative;\r\n  transition: outline-offset 0.3s ease, outline-width 0.3s ease,\r\n    height 0.3s ease, width 0.3s ease;\r\n\r\n  &:global(.focusedByKeyboard) {\r\n    &[data-focus] {\r\n      &:before {\r\n        content: \"\";\r\n        outline: 2px solid var(--color-secondary-cobalt);\r\n        position: absolute;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        top: 0;\r\n        border-radius: 50%;\r\n        outline-offset: 1px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.selectorInput:is(:checked, [data-checked], [aria-checked=\"true\"], [data-state=\"checked\"]) {\r\n  height: 0.75rem;\r\n  width: 0.75rem;\r\n  outline-offset: 3px;\r\n  outline-width: 2px;\r\n  background: var(--color-border-primary-charcoal);\r\n  border-color: var(--color-border-primary-charcoal);\r\n  outline-color: var(--color-border-primary-charcoal);\r\n  outline-style: solid;\r\n\r\n  &[data-focus] {\r\n    &:before {\r\n      outline-offset: 0.5rem;\r\n    }\r\n  }\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .selectorWrapper {\r\n    flex-basis: calc(50% - 10px);\r\n    max-width: calc(50% - 10px);\r\n  }\r\n}\r\n", ".cardSelectorRegularRadio,\r\n.cardSelector {\r\n  border-radius: 0;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n}\r\n\r\n.cardSelector {\r\n  gap: 1.25rem;\r\n}\r\n", ".carousel {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-end;\r\n  height: 100%;\r\n  width: 100%;\r\n  position: relative;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.carousel-viewport {\r\n  position: relative;\r\n}\r\n\r\n.carousel-img {\r\n  width: 100%;\r\n  max-height: 550px;\r\n  object-fit: contain;\r\n  border-top-left-radius: 0.5rem;\r\n  border-bottom-left-radius: 0.5rem;\r\n  position: relative;\r\n  opacity: 0;\r\n  transition: opacity 0.5s ease-in-out, transform 0.25s ease-in-out;\r\n  object-fit: contain;\r\n  overflow: hidden;\r\n}\r\n\r\n.carousel-item-active .carousel-img {\r\n  opacity: 1;\r\n  transform: none;\r\n}\r\n\r\n.carousel-controls {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: flex-end;\r\n  align-items: center;\r\n  padding-right: 1rem;\r\n  position: relative;\r\n  z-index: 2;\r\n  flex-wrap: wrap;\r\n  margin-top: 2rem;\r\n}\r\n\r\n.carousel-buttons {\r\n  display: flex;\r\n  gap: 1rem;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.carouselButton {\r\n  background-color: transparent;\r\n  border: none;\r\n  cursor: pointer;\r\n  padding: 10px;\r\n  outline: none;\r\n  color: var(--color-bg-white);\r\n}\r\n\r\n.carouselButtonControlColors {\r\n  border-radius: 50%;\r\n  background-color: var(--color-bg-primary-charcoal);\r\n}\r\n\r\n.carouselButton:hover {\r\n  background-color: rgba(0, 0, 0, 0.1);\r\n  border-radius: 50%;\r\n}\r\n\r\n.carouselButtonControlColors:hover {\r\n  border-radius: 50%;\r\n  background-color: var(--color-bg-primary-charcoal);\r\n}\r\n\r\n.carouselButton:focus-visible {\r\n  outline: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.pausePlayButton {\r\n  display: flex;\r\n  justify-content: center;\r\n  background-color: transparent;\r\n  z-index: 2;\r\n}\r\n\r\n.pausePlayButton button {\r\n  background-color: transparent;\r\n  padding: 10px;\r\n  border: none;\r\n  cursor: pointer;\r\n  outline: none;\r\n}\r\n\r\n.pausePlayButton button:hover {\r\n  background-color: rgba(0, 0, 0, 0.1);\r\n  border-radius: 50%;\r\n}\r\n\r\n.pausePlayButton button:focus-visible {\r\n  outline: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.pausePlayButtonControlColors button,\r\n.pausePlayButtonControlColors:hover,\r\n.carouselButton:focus-visible,\r\n.pausePlayButton button:focus-visible {\r\n  background-color: var(--color-bg-primary-charcoal);\r\n  border-radius: 50%;\r\n}\r\n\r\n.carousel-item-group {\r\n  display: flex;\r\n  height: 100%;\r\n  transition: transform 0.5s ease-in-out;\r\n}\r\n\r\n.carousel-item {\r\n  flex: 0 0 100%;\r\n  position: relative;\r\n  opacity: 0;\r\n  transition: opacity 0.25s ease-in-out, transform 0.25s ease-in-out;\r\n}\r\n\r\n.carousel-item-active {\r\n  opacity: 1;\r\n  transform: none;\r\n}\r\n\r\n.carousel-text {\r\n  width: 65%;\r\n  color: var(--color-bg-white);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  flex-shrink: 1;\r\n  justify-self: flex-start;\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .carousel-img {\r\n    max-height: 800px;\r\n  }\r\n\r\n  .carousel-controls {\r\n    flex-direction: row;\r\n    margin-top: 0;\r\n  }\r\n\r\n  .carousel-buttons {\r\n    margin-top: 0;\r\n  }\r\n}\r\n", ".checkboxWrapper {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: row;\r\n  gap: 0.75rem;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  &:focus-within {\r\n    outline: none;\r\n\r\n    &:global(.focusedByKeyboard) {\r\n      .checkbox {\r\n        outline: 2px solid var(--color-secondary-cobalt);\r\n        outline-offset: -2px;\r\n        border-radius: 2px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.checkbox {\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.checkbox.large {\r\n  height: 1.5rem;\r\n  width: 1.5rem;\r\n}\r\n\r\n.checkbox.small {\r\n  height: 1.25rem;\r\n  width: 1.25rem;\r\n}\r\n\r\n.checkbox.disabled {\r\n  background-color: var(--color-border-light-grey) !important;\r\n}\r\n\r\n.checkbox[data-state=\"unchecked\"] {\r\n  background-color: var(--color-bg-white);\r\n  border: 1.5px solid var(--color-border-grey);\r\n  margin: 0;\r\n  transition: background-color 100ms linear;\r\n\r\n  svg {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.checkbox[data-state=\"unchecked\"]:focus-visible {\r\n  border: 1.5px solid var(--color-secondary-cobalt);\r\n}\r\n\r\n.checkbox[data-state=\"checked\"] {\r\n  background-color: var(--color-primary-charcoal);\r\n  border: 1.5px solid var(--color-primary-charcoal);\r\n  transition: background-color 100ms linear;\r\n}\r\n\r\n.checkbox.disabled[data-state=\"checked\"] {\r\n  border: 1.5px solid var(--color-border-grey);\r\n  background-color: var(--color-border-grey) !important;\r\n}\r\n\r\n.checkbox.disabled[data-state=\"checked\"]:hover,\r\n.checkbox.disabled[data-state=\"unchecked\"]:hover {\r\n  border: 1.5px solid var(--color-border-grey) !important;\r\n}\r\n\r\n.checkbox:hover {\r\n  border: 1.5px solid var(--color-primary-charcoal) !important;\r\n}\r\n\r\n.checkmark {\r\n  height: 24px;\r\n  width: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.checkboxLabel {\r\n  font-weight: 400;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.checkboxLabel.disabled {\r\n  color: var(--color-bg-inactive-charcoal);\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .checkboxLabel {\r\n    font-size: var(--fontsize-default);\r\n  }\r\n}\r\n", ".checkboxCard {\r\n  position: relative;\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  background-color: var(--color-bg-white);\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-border-grey);\r\n  padding: 1rem;\r\n  width: 100%;\r\n  color: var(--color-primary-charcoal);\r\n\r\n  .labelWrapper {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    margin-right: 1rem;\r\n\r\n    svg {\r\n      margin-right: 1rem;\r\n    }\r\n  }\r\n\r\n  .checkbox {\r\n    width: 1.5rem;\r\n  }\r\n\r\n  [data-scope=\"checkbox\"][data-part=\"control\"]:hover {\r\n    border: 1.5px solid var(--color-border-grey);\r\n  }\r\n\r\n  [data-scope=\"checkbox\"][data-part=\"label\"] {\r\n    display: none;\r\n  }\r\n\r\n  &:focus-within {\r\n    outline: 3px solid var(--color-secondary-cobalt);\r\n  }\r\n}\r\n\r\n.checkboxCard[data-state=\"unchecked\"]:focus-visible::before {\r\n  content: \"\";\r\n  display: block;\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  left: -1.5px;\r\n  top: -1.5px;\r\n  border: 1.5px solid var(--color-secondary-cobalt);\r\n}\r\n\r\n.checkboxCard[data-state=\"unchecked\"]:focus-visible {\r\n  border: 3px solid var(--color-outline-dark-bg);\r\n}\r\n\r\n.checkboxCard[data-scope=\"checkbox-card\"][data-state=\"checked\"],\r\n.checkboxCard:hover,\r\n[data-scope=\"checkbox\"][data-part=\"control\"][data-state=\"checked\"]:hover {\r\n  border: 1px solid var(--color-primary-charcoal);\r\n}\r\n\r\n.checkboxCard[data-scope=\"checkbox-card\"][data-state=\"checked\"][data-disabled=\"active\"] {\r\n  .labelWrapper {\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.checkboxCard[data-scope=\"checkbox-card\"][data-disabled=\"disabled\"] {\r\n  border: 1px solid var(--color-border-grey);\r\n  background: var(--color-bg-light-grey);\r\n  color: var(--color-bg-inactive-charcoal);\r\n\r\n  [data-part=\"indicator\"]:hover {\r\n    cursor: not-allowed;\r\n  }\r\n\r\n  .labelWrapper {\r\n    svg {\r\n      path {\r\n        fill: var(--color-bg-inactive-charcoal);\r\n      }\r\n    }\r\n  }\r\n}\r\n", ".chipDropdown {\r\n  position: relative;\r\n  z-index: var(--z-index-dropdown-list);\r\n\r\n  .trigger {\r\n    display: flex;\r\n    flex-direction: row;\r\n    padding: 0.5rem 1rem;\r\n    align-items: center;\r\n    gap: 0.5rem;\r\n    border-radius: 6.25rem;\r\n    border: 1px solid var(--color-primary-pale-charcoal);\r\n    background: var(--color-bg-white);\r\n    cursor: pointer;\r\n    transition: border 100ms linear;\r\n    color: var(--color-primary-light-charcoal);\r\n\r\n    .chipDownLabel {\r\n      font-size: var(--fontsize-body-small);\r\n      line-height: var(--lineheight-body-small);\r\n      display: flex;\r\n      gap: 0.5rem;\r\n      align-items: center;\r\n    }\r\n\r\n    .chipDownLabelDivider {\r\n      width: 1px;\r\n      height: 1rem;\r\n      background-color: var(--color-primary-pale-charcoal);\r\n    }\r\n\r\n    .triggerChevronWrapper,\r\n    .triggerIconWrapper {\r\n      height: 1rem;\r\n    }\r\n\r\n    .triggerIconWrapper {\r\n      svg {\r\n        path {\r\n          fill: var(--color-primary-slate);\r\n        }\r\n      }\r\n    }\r\n\r\n    &[data-state=\"closed\"] {\r\n      .triggerChevronWrapper {\r\n        svg {\r\n          path {\r\n            fill: var(--color-primary-slate);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &[data-state=\"open\"] {\r\n      .triggerChevronWrapper {\r\n        transform: rotate(180deg);\r\n        svg {\r\n          path {\r\n            fill: var(--color-primary-charcoal);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .trigger[data-state=\"open\"] {\r\n    border: 1px solid var(--color-primary-charcoal);\r\n    transition: border 100ms linear;\r\n    outline: none;\r\n  }\r\n\r\n  .trigger:focus-visible {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: 2px;\r\n  }\r\n\r\n  .searchBarWrapper {\r\n    display: flex;\r\n    margin: 1rem 1rem 0rem 1rem;\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 0.5rem;\r\n    align-self: stretch;\r\n    position: relative;\r\n  }\r\n\r\n  .searchBar {\r\n    display: flex;\r\n    padding: 0.75rem;\r\n    padding-left: 2rem;\r\n    align-items: center;\r\n    gap: 0.25rem;\r\n    align-self: stretch;\r\n    border: 1px solid var(--color-primary-pale-charcoal);\r\n    border-radius: 100px;\r\n    background: var(--color-bg-white);\r\n    font-size: 0.875rem;\r\n    position: relative;\r\n\r\n    &:hover {\r\n      border-color: var(--color-primary-charcoal);\r\n    }\r\n\r\n    &:focus-visible {\r\n      outline: none;\r\n      border: 1px solid var(--color-primary-charcoal);\r\n\r\n      &:global(.focusedByKeyboard) {\r\n        outline: 2px solid var(--color-outline-dark-bg);\r\n      }\r\n    }\r\n  }\r\n\r\n  .searchBarIconWrapper {\r\n    position: absolute;\r\n    left: 0.5rem;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    z-index: 200;\r\n    color: var(--color-primary-slate);\r\n    pointer-events: none;\r\n  }\r\n\r\n  .searchBar::placeholder {\r\n    color: var(--color-bg-inactive-charcoal);\r\n    line-height: var(--lineheight-h6);\r\n  }\r\n\r\n  [data-scope=\"popover\"][data-part=\"content\"] {\r\n    border: none;\r\n    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);\r\n    min-width: 17.5rem;\r\n    max-width: 37.5rem;\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    border-radius: 0.125rem;\r\n    background-color: var(--color-bg-white);\r\n    padding: 0;\r\n    max-height: 300px;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .resetFilter {\r\n    border-top: 1px solid var(--color-primary-pale-charcoal);\r\n    width: 100%;\r\n    padding: 1rem;\r\n    color: var(--color-bg-inactive-charcoal);\r\n    cursor: pointer;\r\n    border-radius: 0;\r\n    width: 100%;\r\n    position: sticky;\r\n    bottom: 0;\r\n    background-color: var(--color-bg-white);\r\n\r\n    &:focus-visible {\r\n      outline-offset: -2px;\r\n    }\r\n  }\r\n}\r\n\r\n.trigger:hover {\r\n  background-color: #f2f2f2;\r\n  border-color: var(--color-primary-charcoal);\r\n}\r\n\r\n.dropDownItemsContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n  margin-top: 0.5rem;\r\n  max-width: 100%;\r\n\r\n  label {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n    cursor: pointer;\r\n  }\r\n}\r\n", ".checkboxDropdownItem {\r\n  display: flex;\r\n  padding: 0.25rem 1rem;\r\n  height: 3rem;\r\n  align-items: center;\r\n  align-self: stretch;\r\n  gap: 1rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.avatarAndLabel {\r\n  display: flex;\r\n  flex-direction: row;\r\n  gap: 0.5rem;\r\n  align-items: center;\r\n  overflow: hidden;\r\n}\r\n\r\n.label {\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n", ".radioDropdownItem {\r\n  display: flex;\r\n  padding: 0.25rem 1rem;\r\n  height: 3rem;\r\n  align-items: center;\r\n  align-self: stretch;\r\n  gap: 1rem;\r\n}", ".circleButton {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 2.5rem;\r\n  height: 2.5rem;\r\n  background-color: var(--color-bg-white);\r\n  padding: 0;\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s ease-in-out;\r\n}\r\n\r\n.circleButtonSmall {\r\n  width: 2rem;\r\n  height: 2rem;\r\n}\r\n\r\n.circleButtonWithBorder {\r\n  border: 1px solid var(--color-border-grey);\r\n}\r\n\r\n.circleButton:hover,\r\n.circleButton:focus-visible {\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.circleButtonEnabled {\r\n  background-color: var(--color-primary-red);\r\n\r\n  svg {\r\n    color: var(--color-bg-white);\r\n  }\r\n}\r\n\r\n.circleButtonDisabled {\r\n  cursor: default;\r\n}\r\n\r\n.circleButtonDisabled:hover {\r\n  background-color: var(--color-bg-white);\r\n}\r\n", ".tooltip {\r\n  background-color: var(--color-primary-charcoal);\r\n  color: var(--color-primary-pale-charcoal);\r\n  border-radius: 3px;\r\n  font-size: var(--fontsize-body);\r\n  font-family: \"Proxima<PERSON><PERSON>\", Arial, sans-serif;\r\n  line-height: var(--lineheight-body-small);\r\n  padding: 16px;\r\n  max-width: 235px;\r\n  z-index: var(--z-index-tooltip);\r\n  white-space: pre-line;\r\n}\r\n\r\n.smallSize {\r\n  padding: 4px 8px;\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.wide {\r\n  max-width: 280px;\r\n}\r\n\r\n.arrow {\r\n  --arrow-size: 9px;\r\n  --arrow-background: var(--color-primary-charcoal);\r\n}\r\n\r\n.closerToDrawerButton [data-scope=\"tooltip\"] {\r\n  --y: 42px !important;\r\n}\r\n", "@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-regular-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-regular-webfont.woff\") format(\"woff\");\r\n  font-weight: 400;\r\n  font-style: normal;\r\n  font-display: swap;\r\n}\r\n\r\n@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-extrabold-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-extrabold-webfont.woff\") format(\"woff\");\r\n  font-weight: 800;\r\n  font-style: normal;\r\n  font-display: swap;\r\n}\r\n\r\n@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-bold-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-bold-webfont.woff\") format(\"woff\");\r\n  font-weight: 700;\r\n  font-style: normal;\r\n  font-display: swap;\r\n}\r\n\r\n@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-semibold-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-semibold-webfont.woff\") format(\"woff\");\r\n  font-weight: 600;\r\n  font-style: normal;\r\n  font-display: swap;\r\n}\r\n\r\n@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-light-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-light-webfont.woff\") format(\"woff\");\r\n  font-weight: 300;\r\n  font-style: normal;\r\n  font-display: swap;\r\n}\r\n\r\n@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-thin-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-thin-webfont.woff\") format(\"woff\");\r\n  font-weight: 100;\r\n  font-style: normal;\r\n  font-display: swap;\r\n}\r\n\r\n@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-thinit-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-thinit-webfont.woff\") format(\"woff\");\r\n  font-weight: 100;\r\n  font-style: italic;\r\n  font-display: swap;\r\n}\r\n\r\n@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-boldit-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-boldit-webfont.woff\") format(\"woff\");\r\n  font-weight: 600;\r\n  font-style: italic;\r\n  font-display: swap;\r\n}\r\n\r\n@font-face {\r\n  font-family: \"ProximaNova\";\r\n  src: url(\"/fonts/proximanova-regularit-webfont.woff2\") format(\"woff2\"),\r\n    url(\"/fonts/proximanova-regularit-webfont.woff\") format(\"woff\");\r\n  font-weight: 400;\r\n  font-style: italic;\r\n  font-display: swap;\r\n}\r\n", ":root {\r\n  /* Header Font Sizes */\r\n  --fontsize-h1: 2.25rem;\r\n  --fontsize-h2: 1.875rem;\r\n  --fontsize-h3: 1.625rem;\r\n  --fontsize-h4: 1.375rem;\r\n  --fontsize-h5: 1.25rem;\r\n  --fontsize-h6: 1.125rem;\r\n\r\n  /* Body Font Sizes */\r\n  --fontsize-body: 1rem;\r\n  --fontsize-body-small: 0.875rem;\r\n  --fontsize-body-xsmall: 0.75rem;\r\n  --fontsize-body-badge: 0.75rem;\r\n  --fontsize-body-label: 0.75rem;\r\n  --fontsize-body-eyebrow: 0.625rem;\r\n  --fontsize-default: 1rem;\r\n\r\n  /* Header Line Heights */\r\n  --lineheight-h1: 3rem;\r\n  --lineheight-h2: 2.5rem;\r\n  --lineheight-h3: 2rem;\r\n  --lineheight-h4: 2rem;\r\n  --lineheight-h5: 1.5rem;\r\n  --lineheight-h6: 1.5rem;\r\n\r\n  /* Body Line Heights */\r\n  --lineheight-body: 1.5rem;\r\n  --lineheight-body-small: 1.25rem;\r\n  --lineheight-body-xsmall: 1rem;\r\n  --lineheight-body-badge: 1rem;\r\n  --lineheight-body-label: 1rem;\r\n  --lineheight-body-eyebrow: 1.125rem;\r\n  --lineheight-default: 1.5rem;\r\n\r\n  /* Primary Colors */\r\n  --color-primary-red: #e81a3b;\r\n  --color-primary-red-transparent: #e81a3b14;\r\n  --color-primary-red-disabled: #e81a3b4d;\r\n  --color-primary-charcoal: #333333;\r\n  --color-primary-pale-charcoal: #e7e7e7;\r\n  --color-primary-light-charcoal: #404040;\r\n  --color-primary-slate: #5b6e7f;\r\n  --color-primary-white: #ffffff;\r\n  --color-primary-inactive-charcoal: #666666;\r\n  --color-primary-soft-charcoal: #fafafa;\r\n\r\n  /* Secondary Colors */\r\n  --color-secondary-burgundy: #98002e;\r\n  --color-secondary-cobalt: #0062b8;\r\n  --color-secondary-cobalt-transparent: #0062b814;\r\n  --color-secondary-emerald: #218f8b;\r\n  --color-secondary-ocean: #008fd2;\r\n  --color-secondary-gold: #d67900;\r\n  --color-secondary-gold-transparent: #d679001a;\r\n  --color-secondary-jade: #009966;\r\n\r\n  /* Badge Colors */\r\n  --color-secondary-emerald-badge: #218f8b33;\r\n  --color-secondary-gold-badge: #d6790033;\r\n  --color-secondary-burgundy-badge: #98002e33;\r\n\r\n  /* Background Colors */\r\n  --color-bg-white: #ffffff;\r\n  --color-bg-primary-charcoal: var(--color-primary-charcoal);\r\n  --color-bg-primary-pale-charcoal: var(--color-primary-pale-charcoal);\r\n  --color-bg-secondary-cobalt: var(--color-secondary-cobalt);\r\n  --color-bg-grey: #cccfd2;\r\n  --color-bg-light-grey: #f2f2f2;\r\n  --color-bg-light-charcoal: var(--color-primary-light-charcoal);\r\n  --color-bg-inactive-charcoal: #666666;\r\n  --color-bg-light-emerald: #e9f4f3;\r\n  --color-bg-light-cobalt: #e9f1f7;\r\n  --color-bg-light-burgundy: #f7ebee;\r\n  --color-bg-light-cobalt-translucent: #f2f7fb;\r\n  --color-bg-warning-yellow: #f3d429;\r\n  --color-bg-warning-yellow-translucent: #f3d42914;\r\n\r\n  /* Border Colors */\r\n  --color-border-pale-grey: var(--color-primary-pale-charcoal);\r\n  --color-border-primary-charcoal: var(--color-primary-charcoal);\r\n  --color-border-light-charcoal: var(--color-primary-light-charcoal);\r\n  --color-border-grey: var(--color-bg-grey);\r\n  --color-border-light-grey: var(--color-bg-light-grey);\r\n  --color-border-white: var(--color-bg-white);\r\n  --color-outline-dark-bg: #cce0f1;\r\n  --z-index-dropdown-list-in-table: 50;\r\n  --z-index-table-header: 80;\r\n  --z-index-dropdown-list: 100;\r\n  --z-index-modal: 700;\r\n  --z-index-dropdown-menu: 650;\r\n  --z-index-drawer: 600;\r\n  --z-index-drawer-overlay: 500;\r\n  --z-index-toast: 800;\r\n  --z-index-spinner: 800;\r\n  --z-index-chip-dropdown: 1000;\r\n  --z-index-btn-dropdown: 200;\r\n  --z-index-popup: 300;\r\n  --z-index-tooltip: 300;\r\n\r\n  /* Breakpoints - min-widths */\r\n  --screensize-xxs: 320px; /* mobile devices */\r\n  --screensize-xs: 481px; /* mobile devices, tablet devices */\r\n  --screensize-s: 769px; /* tablet devices, smaller laptop screens*/\r\n  --screensize-m: 1025px; /* desktops, larger screens */\r\n  --screensize-l: 1201px; /* desktops, larger screens*/\r\n  --screensize-xl: 1441px; /* desktops, larger screens */\r\n  --screensize-xxl: 1601px; /* extra large screens */\r\n\r\n  /* Primary font-family */\r\n  --primary-font-family: \"ProximaNova\", Arial, sans-serif;\r\n\r\n  /* Sizes */\r\n  --main-header-height: 3.5rem;\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  :root {\r\n    --fontsize-h1: 2.812rem;\r\n    --fontsize-h2: 2.25rem;\r\n    --fontsize-h3: 1.875rem;\r\n    --fontsize-h4: 1.375rem;\r\n    --fontsize-h5: 1.375rem;\r\n    --fontsize-h6: 1.25rem;\r\n\r\n    --lineheight-h1: 3.5rem;\r\n    --lineheight-h2: 3rem;\r\n    --lineheight-h3: 2.5rem;\r\n    --lineheight-h4: 2rem;\r\n    --lineheight-h5: 2rem;\r\n    --lineheight-h6: 1.5rem;\r\n\r\n    /* Sizes */\r\n    --main-header-height: 3.75rem;\r\n  }\r\n}\r\n\r\nbody {\r\n  font-family: var(--primary-font-family);\r\n}\r\n\r\nh1 {\r\n  font-size: var(--fontsize-h1);\r\n  line-height: var(--lineheight-h1);\r\n  font-weight: 600;\r\n}\r\n\r\nh2 {\r\n  font-size: var(--fontsize-h2);\r\n  line-height: var(--lineheight-h2);\r\n  font-weight: 600;\r\n}\r\n\r\nh3 {\r\n  font-size: var(--fontsize-h3);\r\n  line-height: var(--lineheight-h3);\r\n  font-weight: 600;\r\n}\r\n\r\nh4 {\r\n  font-size: var(--fontsize-h4);\r\n  line-height: var(--lineheight-h4);\r\n  font-weight: 600;\r\n}\r\n\r\nh5 {\r\n  font-size: var(--fontsize-h5);\r\n  line-height: var(--lineheight-h5);\r\n  font-weight: 600;\r\n}\r\n\r\nh6 {\r\n  font-size: var(--fontsize-h6);\r\n  line-height: var(--lineheight-h6);\r\n  font-weight: 600;\r\n}\r\n\r\np {\r\n  font-size: var(--fontsize-default);\r\n  line-height: var(--lineheight-default);\r\n}\r\n", ".copyToClipboard {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.copyToClipboardText {\r\n  margin: 0;\r\n}\r\n\r\n.copyButton {\r\n  display: flex;\r\n}\r\n\r\n.copyButton {\r\n  border: none;\r\n  padding: 0.5rem;\r\n  border-radius: 50%;\r\n  transition: background-color 0.2s;\r\n  background-color: transparent;\r\n}\r\n\r\n.copyButton:hover {\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n", ".datePickerLabel {\r\n  display: none;\r\n}\r\n\r\n.datePicker,\r\n.datePickerCalendar,\r\n.datePickerCalendarRow:nth-child(even),\r\n.datePickerCalendarDayRow {\r\n  background-color: var(--color-bg-white);\r\n}\r\n\r\n.datePicker {\r\n  font-family: \"ProximaNova\", Aria<PERSON>, sans-serif;\r\n  box-shadow: 0px 2px 4px 0px #00000026;\r\n}\r\n\r\n.control :global(button) {\r\n  transition: background-color 0.2s;\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-soft-charcoal);\r\n  }\r\n}\r\n\r\n.datePickerInput {\r\n  width: 100%;\r\n}\r\n\r\n.datePickerButton {\r\n  cursor: pointer;\r\n  border: none;\r\n  padding: 0;\r\n}\r\n\r\n/* Styling added to give fixed height to date picker to stop dropdown switching positions */\r\n[data-scope=\"date-picker\"][data-part=\"positioner\"] {\r\n  display: flex;\r\n  min-height: 25rem;\r\n}\r\n\r\n/* Avoid extra space for if date picker pops upwards */\r\n[data-scope=\"date-picker\"][data-part=\"content\"][data-placement=\"top-start\"],\r\n[data-scope=\"date-picker\"][data-part=\"content\"][data-placement=\"top-end\"],\r\n[data-scope=\"date-picker\"][data-part=\"content\"][data-placement=\"top\"] {\r\n  align-self: flex-end;\r\n}\r\n\r\n/* Avoid extra space for if date picker pops downwards */\r\n[data-scope=\"date-picker\"][data-part=\"content\"][data-placement=\"bottom-start\"],\r\n[data-scope=\"date-picker\"][data-part=\"content\"][data-placement=\"bottom-end\"],\r\n[data-scope=\"date-picker\"][data-part=\"content\"][data-placement=\"bottom\"] {\r\n  align-self: flex-start;\r\n}\r\n\r\n.datePickerButton svg path {\r\n  fill: var(--color-primary-charcoal);\r\n}\r\n\r\n.datePickerTrigger {\r\n  background-color: var(--color-bg-white);\r\n  color: var(--color-primary-charcoal);\r\n  font-weight: 600;\r\n  padding: 0;\r\n  border: none;\r\n}\r\n\r\n.datePickerInput {\r\n  width: 100%;\r\n  background-color: var(--color-bg-white);\r\n  font-family: \"ProximaNova\", Arial, sans-serif;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-primary-charcoal);\r\n  padding: 0.5rem 1rem;\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  outline: none;\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-soft-charcoal);\r\n  }\r\n}\r\n\r\n.datePickerInput:focus-visible {\r\n  outline: none;\r\n\r\n  &:global(.focusedByKeyboard) {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: 2px;\r\n  }\r\n}\r\n\r\n.datePickerInput[data-state=\"open\"] {\r\n  border: 1px solid var(--color-bg-primary-charcoal);\r\n}\r\n\r\n.datePickerControl {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0.5rem 0.75rem;\r\n}\r\n\r\n.datePickerBody {\r\n  padding: 0.5rem 1rem 1rem;\r\n}\r\n\r\n.datePickerCalendarDays,\r\n.datePickerDate {\r\n  text-align: center;\r\n  border: none;\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.datePickerDate {\r\n  padding: 0;\r\n  width: 2.5rem;\r\n  height: 2rem;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  border-radius: 2px;\r\n}\r\n\r\n.datePickerDate:hover {\r\n  cursor: pointer;\r\n  color: var(--color-primary-charcoal);\r\n  background-color: var(--color-primary-soft-charcoal);\r\n}\r\n\r\n.datePickerCalendarDays {\r\n  font-weight: 400;\r\n  font-size: 0.85rem;\r\n  color: var(--color-bg-inactive-charcoal);\r\n  width: 14.285%; /* 1/7 */\r\n}\r\n\r\n.highlightedDate {\r\n  position: relative;\r\n  font-weight: 600;\r\n}\r\n\r\n.hiddenDate {\r\n  visibility: hidden;\r\n}\r\n\r\n.highlightedDate::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  bottom: 4px;\r\n  left: 50%;\r\n  transform: translate(-50%, 50%);\r\n  width: 4px;\r\n  height: 4px;\r\n  background-color: var(--color-primary-red);\r\n  border-radius: 50%;\r\n}\r\n\r\n.disabledDate,\r\n.outsideMonthDate {\r\n  color: var(--color-bg-grey);\r\n  pointer-events: none;\r\n  cursor: not-allowed;\r\n  opacity: 0.5;\r\n}\r\n\r\n.tableCellTrigger {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n[data-scope=\"date-picker\"][data-part=\"content\"][data-state=\"open\"] {\r\n  z-index: var(--z-index-modal);\r\n  position: relative;\r\n}\r\n\r\n.customRange {\r\n  text-align: center;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-bg-inactive-charcoal);\r\n}\r\n\r\n.rangeTriggers {\r\n  border-radius: 100px;\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  padding: 0.5rem 1rem;\r\n  background-color: var(--color-bg-white);\r\n  color: var(--color-primary-light-charcoal);\r\n  cursor: pointer;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.customRanges {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  justify-content: center;\r\n  padding: 0.5rem 0.75rem;\r\n  border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n}\r\n\r\n.rangeStart,\r\n.rangeStart:hover,\r\n.rangeEnd,\r\n.rangeEnd:hover,\r\n.selectedDate,\r\n.selectedDate:hover {\r\n  background-color: var(--color-primary-red);\r\n  color: var(--color-bg-white);\r\n  border-radius: 2px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Lighter red for the selected range */\r\n.rangeBetween,\r\n.rangeBetween:hover {\r\n  background-color: var(\r\n    --color-secondary-burgundy-badge\r\n  ); /* Your lighter red */\r\n  color: var(--color-primary-charcoal);\r\n  font-weight: 400;\r\n}\r\n\r\n.clickable,\r\n.hover:hover,\r\n.datePickerButton,\r\n.datePickerCalendarDays,\r\n.datePickerDate:not(.disabledDate) {\r\n  cursor: pointer;\r\n}\r\n\r\n[data-scope=\"date-picker\"][data-part=\"table\"] {\r\n  width: 100%;\r\n}\r\n\r\n[data-scope=\"date-picker\"][data-part=\"table-cell\"][aria-disabled=\"true\"] {\r\n  color: var(--color-bg-grey);\r\n  pointer-events: none;\r\n  cursor: not-allowed;\r\n  opacity: 0.5;\r\n}\r\n\r\n[data-scope=\"date-picker\"][data-part=\"table-header\"] {\r\n  padding: 0.5rem;\r\n}\r\n\r\n.rangeSelected {\r\n  border: 1px solid var(--color-primary-charcoal);\r\n}\r\n\r\n.actions {\r\n  border-top: 1px solid var(--color-primary-pale-charcoal);\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.resetButton {\r\n  color: var(--color-bg-inactive-charcoal);\r\n  cursor: pointer;\r\n  padding: 1rem;\r\n  border-radius: 0;\r\n  text-align: left;\r\n}\r\n", "[data-scope=\"file-upload\"][data-part=\"root\"] {\r\n  display: grid;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: var(--color-primary-white);\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 2px;\r\n}\r\n\r\n.textContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  padding: 1rem;\r\n}\r\n\r\n.title {\r\n  color: var(--color-primary-charcoal);\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n  margin: 0;\r\n}\r\n\r\n.titleAsterisk {\r\n  color: var(--color-secondary-burgundy);\r\n  margin-left: 0.25rem;\r\n}\r\n\r\n.subtitle {\r\n  color: var(--color-primary-light-charcoal);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  margin: 0;\r\n}\r\n\r\n.dropzoneContainer {\r\n  padding: 0 1rem 1rem;\r\n}\r\n\r\n.dropzoneContainerError {\r\n  [data-scope=\"file-upload\"][data-part=\"dropzone\"] {\r\n    border-color: var(--color-secondary-burgundy);\r\n  }\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"root\"] {\r\n  height: auto;\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"dropzone\"] {\r\n  display: grid;\r\n  justify-items: center;\r\n  align-items: center;\r\n  padding: 1rem 1.5rem;\r\n  border: 1px solid var(--color-bg-primary-pale-charcoal);\r\n  border-style: dashed;\r\n  border-radius: 2px;\r\n  background-color: #fafafa;\r\n  color: var(--color-bg-inactive-charcoal);\r\n  transition: all 0.5s ease;\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"dropzone\"]:hover {\r\n  cursor: pointer;\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"dropzone\"][data-dragging] {\r\n  background-color: var(--color-bg-light-cobalt-transparent);\r\n  border-color: var(--color-secondary-cobalt);\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"dropzone\"]:hover {\r\n  cursor: pointer;\r\n}\r\n\r\n.dropdownText {\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n}\r\n\r\n.dropdownTextBlue {\r\n  color: var(--color-secondary-cobalt);\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"item-group\"] {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"item\"] {\r\n  display: grid;\r\n  align-items: center;\r\n  border-top: 1px solid var(--color-border-pale-grey);\r\n  border-top-left-radius: 2px;\r\n  border-top-right-radius: 2px;\r\n  grid-template-columns: auto 1fr auto auto;\r\n  padding: 0.75rem 1rem;\r\n  column-gap: 0.75rem;\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"item-name\"] {\r\n  color: var(--color-primary-charcoal);\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--fontsize-body);\r\n  word-break: break-word;\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"item-delete-trigger\"] {\r\n  border: none;\r\n  outline: none;\r\n  padding: 0.5rem;\r\n  margin: 0;\r\n  width: auto;\r\n  background-color: transparent;\r\n  cursor: pointer;\r\n  border-radius: 50%;\r\n  transition: background-color 0.25s ease-in-out;\r\n\r\n  svg {\r\n    color: var(--color-primary-charcoal);\r\n  }\r\n}\r\n\r\n[data-scope=\"file-upload\"][data-part=\"item-delete-trigger\"]:hover {\r\n  background-color: var(--color-bg-light-grey);\r\n  cursor: pointer;\r\n}\r\n\r\n.documentIcon {\r\n  display: grid;\r\n  align-items: center;\r\n  justify-items: center;\r\n  background-color: var(--color-bg-light-grey);\r\n  border-radius: 2px;\r\n  padding: 0.5rem;\r\n  color: var(--color-primary-slate);\r\n}\r\n\r\n.pendingIcon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 2rem;\r\n  width: 2rem;\r\n}\r\n\r\n.nameContainer {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--fontsize-body-small);\r\n  color: var(--color-bg-inactive-charcoal);\r\n  justify-self: end;\r\n}\r\n\r\n.toastWrapper {\r\n  position: fixed;\r\n  transform: translate(-50%, 0);\r\n  bottom: 1rem;\r\n  right: 1.5rem;\r\n  min-width: 358px;\r\n}\r\n\r\n.noDocumentsContainer {\r\n  padding: 1rem;\r\n  border-top: 1px solid var(--color-bg-primary-pale-charcoal);\r\n}\r\n\r\n.noDocumentsInfo {\r\n  padding: 1rem 1.5rem;\r\n  border-radius: 2px;\r\n  background-color: #fafafa;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-primary-inactive-charcoal);\r\n  text-align: center;\r\n}\r\n\r\n.errorTextContainer {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  color: var(--color-secondary-burgundy);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  padding: 0.5rem 0;\r\n}\r\n\r\n.errorText {\r\n  margin: 0;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body);\r\n  text-wrap: nowrap;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .toastWrapper {\r\n    bottom: 128px;\r\n    left: default;\r\n    transform: none;\r\n  }\r\n\r\n  .labels {\r\n    grid-template-columns: 1fr auto;\r\n  }\r\n}\r\n", "@keyframes rotation-clockwise {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(90deg);\r\n  }\r\n}\r\n\r\n@keyframes rotation-counter-clockwise {\r\n  0% {\r\n    transform: rotate(90deg);\r\n  }\r\n  100% {\r\n    transform: rotate(0deg);\r\n  }\r\n}\r\n\r\n.treeViewRoot ul {\r\n  list-style-type: none;\r\n  padding-inline-start: 0.75rem;\r\n  margin: 0;\r\n}\r\n\r\n.treeViewRoot ul li {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.branch {\r\n  padding: 0;\r\n  width: 100%;\r\n  text-align: left;\r\n}\r\n\r\n.focusableBranch:focus-visible {\r\n  outline: none;\r\n\r\n  .indicator {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: -3px;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.indicator {\r\n  margin-bottom: 0;\r\n  line-height: 20px;\r\n  font-size: 14px;\r\n  height: 44px;\r\n  align-content: center;\r\n  padding-right: 8px;\r\n  padding-left: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.disabledLink {\r\n  pointer-events: none;\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.readOnly {\r\n  padding-left: 2rem;\r\n}\r\n\r\n.indicator svg {\r\n  vertical-align: middle;\r\n}\r\n\r\n.indicator:hover {\r\n  cursor: pointer;\r\n  background-color: #f2f2f2;\r\n  max-width: 210px;\r\n  width: fit-content;\r\n  min-width: -webkit-fill-available;\r\n}\r\n\r\n.treeViewSelected {\r\n  background-color: #f2f2f2;\r\n}\r\n\r\n.indicator:focus-visible {\r\n  background-color: #f2f2f2;\r\n}\r\n\r\n.icons,\r\n.icons .dropdown {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.icons .dropdown {\r\n  color: #5b6e7f;\r\n  animation: rotation-counter-clockwise 0.25s linear;\r\n}\r\n\r\n.icons .dropdownRotated {\r\n  transform: rotate(90deg);\r\n  display: inline-block;\r\n  color: #5b6e7f;\r\n  animation: rotation-clockwise 0.25s linear;\r\n}\r\n\r\n.icons .folder {\r\n  margin-right: 8px;\r\n  margin-left: 8px;\r\n  display: inline-block;\r\n}\r\n\r\n.folder {\r\n  svg {\r\n    path {\r\n      fill: var(--color-primary-charcoal);\r\n    }\r\n  }\r\n}\r\n\r\n.icons .dropdown:focus-visible {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.highlighted {\r\n  background-color: #f2f2f2;\r\n  font-weight: 600;\r\n  width: fit-content;\r\n  min-width: -webkit-fill-available;\r\n}\r\n\r\n.labelParent {\r\n  width: 100%;\r\n  height: 100%;\r\n  align-content: center;\r\n  color: var(--color-primary-light-charcoal);\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 114px;\r\n  overflow: hidden;\r\n}\r\n\r\n.loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.restrictedAndReadonly {\r\n  display: flex;\r\n  gap: 8px;\r\n  padding-right: 4px;\r\n}\r\n", ".parentDiv {\r\n  background-color: white;\r\n  width: 240px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border: 1px solid #e7e7e7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.inputParent {\r\n  margin-left: 16px;\r\n  padding-top: 16px;\r\n}\r\n\r\n.inputParent [data-forspacing=\"forSearchIcon\"] {\r\n  top: 12px;\r\n}\r\n\r\n.input {\r\n  width: 208px;\r\n  height: 40px;\r\n  padding: 0 1.75rem 0 1rem;\r\n  border-radius: 100px;\r\n  text-overflow: ellipsis;\r\n  font-size: 0.875rem;\r\n  letter-spacing: -0.1px;\r\n}\r\n\r\n.labelParent {\r\n  margin: 0 1rem;\r\n}\r\n\r\n.label {\r\n  font-family: var(--primary-font-family);\r\n  font-size: var(--fontsize-body-label);\r\n  line-height: var(--lineheight-body-label);\r\n  color: #666666;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.docNavButton {\r\n  height: 44px;\r\n  flex: 0 1 auto;\r\n  margin: 1rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.documentsTitleLink {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: var(--fontsize-body-small);\r\n  font-weight: 400;\r\n  gap: 0.5rem;\r\n  padding: 0.85rem 0.5rem;\r\n  border-radius: 2px;\r\n  width: 100%;\r\n  color: var(--color-primary-light-charcoal);\r\n\r\n  &:hover {\r\n    color: initial;\r\n  }\r\n\r\n  &:focus-visible {\r\n    outline-color: var(--color-primary-pale-charcoal);\r\n    outline-width: 0.25rem;\r\n  }\r\n}\r\n\r\n.documentsHighlight,\r\n.documentsTitleLink:hover {\r\n  background-color: #f2f2f2;\r\n}\r\n\r\n.folders {\r\n  margin-right: 16px;\r\n  flex: 1 1 auto;\r\n  overflow-y: auto;\r\n}\r\n\r\n.folders li {\r\n  display: inline;\r\n}\r\n\r\n.spinnerContainer {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n}\r\n", ".drawerWrapper {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  display: grid;\r\n  align-items: end;\r\n  justify-content: flex-end;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: var(--z-index-drawer);\r\n}\r\n\r\n.overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #000000;\r\n  opacity: 0.5;\r\n}\r\n\r\n.drawer {\r\n  display: grid;\r\n  grid-template-rows: auto 1fr auto;\r\n  align-content: space-between;\r\n  min-width: 32.5rem;\r\n  max-width: 100%;\r\n  height: 100vh;\r\n  background-color: var(--color-bg-white);\r\n  box-shadow: -4px 0px 20px 0px rgba(0, 0, 0, 0.1);\r\n  pointer-events: auto;\r\n}\r\n\r\n.drawer.large {\r\n  width: 42rem;\r\n}\r\n\r\n.drawerHeader {\r\n  display: grid;\r\n  grid-template-columns: 1fr auto;\r\n  align-items: center;\r\n  overflow: hidden;\r\n  padding: 1rem;\r\n  color: var(--color-primary-charcoal);\r\n  border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n  min-height: 5rem; /* subject to rework - min height to allow for spacing around absolute positioned X button and no title/subtitle */\r\n  outline: none;\r\n\r\n  .drawerHeaderLeft {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 0.75rem;\r\n\r\n    .avatar {\r\n      height: 56px;\r\n      width: 56px;\r\n      border-radius: 50%;\r\n      background-color: var(--color-secondary-ocean);\r\n      color: var(--color-bg-white);\r\n      margin-right: 0.75rem;\r\n      font-size: var(--fontsize-h5);\r\n      font-weight: 600;\r\n      line-height: var(--lineheight-h5);\r\n      text-align: center;\r\n    }\r\n\r\n    .backButton {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      padding: 0;\r\n      margin-right: 0.25rem;\r\n      border-radius: 50%;\r\n      width: 2rem;\r\n      height: 2rem;\r\n      transition: background-color 0.2s;\r\n      align-self: center;\r\n\r\n      > svg {\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      &:hover {\r\n        background-color: var(--color-bg-light-grey);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.drawerHeaderContent {\r\n  align-content: center;\r\n  width: 100%;\r\n}\r\n\r\n.drawerTitle {\r\n  font-weight: 600;\r\n  font-size: var(--fontsize-h4);\r\n  line-height: 2rem;\r\n  margin: 0;\r\n  height: 2.5rem;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.drawerPretitle {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  color: var(--color-primary-light-charcoal);\r\n  height: 2.5rem;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.drawerSubtitle {\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.drawerContent {\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow: auto;\r\n}\r\n\r\n.drawerFooter {\r\n  display: grid;\r\n  justify-content: end;\r\n  grid-template-columns: 1fr 1fr;\r\n  column-gap: 1rem;\r\n  padding: 1rem;\r\n  border-top: 1px solid var(--color-bg-primary-pale-charcoal);\r\n}\r\n\r\n.headerButtons {\r\n  position: absolute;\r\n  top: 24px;\r\n  right: 24px;\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  align-items: center;\r\n}\r\n\r\n.copyButton:hover {\r\n  cursor: pointer;\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .drawerHeader {\r\n    padding: 1.5rem 1.5rem 1rem 1.5rem;\r\n  }\r\n\r\n  .drawerHeaderTitleOnly {\r\n    padding: 1.5rem 1.5rem 1.25rem 1.5rem;\r\n  }\r\n\r\n  .drawerFooter {\r\n    grid-template-columns: auto auto;\r\n    padding: 1rem 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .backButton {\r\n    display: none !important;\r\n  }\r\n\r\n  .drawer,\r\n  .drawer.large {\r\n    min-width: unset;\r\n    width: 100vw;\r\n  }\r\n\r\n  .drawerContent {\r\n    width: 100vw;\r\n  }\r\n}\r\n", ".dropdownMenuWrapper {\r\n  [data-scope=\"menu\"][data-part=\"trigger\"] {\r\n    cursor: pointer;\r\n    border-radius: 50%;\r\n    height: 2rem;\r\n    width: 2rem;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    transition: background-color 0.2s;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"trigger\"]:hover {\r\n    background-color: var(--color-bg-light-grey);\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"trigger\"]:focus-visible {\r\n    border: 0;\r\n    margin: 0;\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: -3px;\r\n  }\r\n}\r\n\r\n.dropdownMenuPositioner {\r\n  /* Override library's z-index to show above drawers */\r\n  z-index: var(--z-index-dropdown-menu) !important;\r\n\r\n  [data-scope=\"menu\"][data-part=\"content\"] {\r\n    outline: none;\r\n    display: block;\r\n    border: 1px solid var(--color-bg-grey);\r\n    box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);\r\n    min-width: 15rem;\r\n    padding: 0;\r\n    /* Temporary to fix global styles leaking from other components */\r\n    position: static !important;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"content\"][data-state=\"open\"] {\r\n    display: block;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"content\"][data-state=\"closed\"] {\r\n    display: none;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"item\"] {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: start;\r\n    padding: 1rem;\r\n    gap: 1rem;\r\n    color: var(--color-primary-charcoal);\r\n    cursor: pointer;\r\n    transition: background-color 300ms linear;\r\n    text-wrap-mode: nowrap;\r\n    height: auto;\r\n    width: auto;\r\n\r\n    svg {\r\n      path {\r\n        fill: var(--color-primary-charcoal);\r\n      }\r\n    }\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"item\"]:first-of-type {\r\n    border-top-left-radius: 2px;\r\n    border-top-right-radius: 2px;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"item\"]:last-of-type {\r\n    border-bottom-left-radius: 2px;\r\n    border-bottom-right-radius: 2px;\r\n    border-bottom: none;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"item\"] {\r\n    &:not([data-disabled]):hover {\r\n      background-color: var(--color-bg-light-grey);\r\n    }\r\n\r\n    &[data-disabled] {\r\n      cursor: not-allowed;\r\n      color: var(--color-bg-inactive-charcoal);\r\n    }\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"content\"][data-state=\"closed\"] {\r\n    opacity: 0;\r\n    transition: opacity 200ms linear, visibility 200ms linear;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"content\"][data-state=\"open\"] {\r\n    background-color: var(--color-bg-white);\r\n    opacity: 1;\r\n    transition: opacity 200ms linear, visibility 200ms linear;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"item\"].critical {\r\n    color: var(--color-secondary-burgundy);\r\n    border-top: 1px solid var(--color-primary-pale-charcoal);\r\n\r\n    svg {\r\n      path {\r\n        fill: var(--color-secondary-burgundy);\r\n      }\r\n    }\r\n  }\r\n}\r\n", ".dropdownWrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n}\r\n\r\n.dropdownLabel {\r\n  color: var(--color-primary-charcoal);\r\n  line-height: 24px;\r\n  font-weight: 600;\r\n  font-size: var(--fontsize-default);\r\n  margin-bottom: 1rem;\r\n  display: block;\r\n}\r\n\r\n.dropdownSelection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: baseline;\r\n}\r\n\r\n.dropdownPlaceholder {\r\n  color: var(--color-bg-inactive-charcoal);\r\n}\r\n\r\n.dropdownPlaceholder[data-focus] {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  left: 1rem;\r\n  top: 0.25rem;\r\n  position: absolute;\r\n}\r\n\r\n.dropdownControl {\r\n  position: relative;\r\n}\r\n\r\n.dropdownControl.error {\r\n  border: 1px solid var(--color-primary-red);\r\n}\r\n\r\n.input {\r\n  height: 3.5rem;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n  padding-right: 1.725rem; /* Extra padding to accomodate dropdown arrow */\r\n}\r\n\r\n.inputSpinner {\r\n  padding-right: 2rem; /* Extra padding to accomodate spinner */\r\n}\r\n\r\n.inputError,\r\n.inputError:focus-visible {\r\n  border: none;\r\n}\r\n\r\n.dropdownIndicator {\r\n  position: absolute;\r\n  right: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.dropdownIndicator.disabled {\r\n  pointer-events: none;\r\n}\r\n\r\n.dropdownTrigger {\r\n  position: relative;\r\n  z-index: var(--z-index-dropdown-list-in-table);\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  padding: 0;\r\n  border: 0;\r\n}\r\n\r\n.arrowUp {\r\n  transform: translateY(-50%) rotate(180deg);\r\n}\r\n\r\n@keyframes fadeIn {\r\n  0% {\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n.dropdownItemsWrapper {\r\n  position: relative;\r\n  z-index: var(--z-index-dropdown-list-in-table);\r\n}\r\n\r\n.dropDownContent {\r\n  max-height: 15.625rem;\r\n  overflow: hidden;\r\n  padding: 0;\r\n  margin: 0;\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);\r\n\r\n  &.largeContent {\r\n    width: 20.3rem;\r\n  }\r\n}\r\n\r\n.dropdownItemsWrapper.hidden {\r\n  pointer-events: none;\r\n  visibility: hidden;\r\n}\r\n\r\n.dropdownPositioner.hidden {\r\n  visibility: hidden;\r\n  opacity: 0;\r\n  transition: opacity 200ms, visibility 200ms;\r\n}\r\n\r\n.dropdownPositioner.visible {\r\n  animation: fadeIn 200ms;\r\n}\r\n\r\n.dropdownItem {\r\n  display: flex;\r\n  min-height: 48px;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  flex: 1 0 0;\r\n  gap: 0.5rem;\r\n  padding: 0 1rem;\r\n  background-color: var(--color-bg-white);\r\n  color: var(--color-primary-charcoal);\r\n  transition: background-color 200ms linear;\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  box-sizing: border-box;\r\n  cursor: pointer;\r\n}\r\n\r\n.noResults {\r\n  padding: 1rem;\r\n  background-color: var(--color-bg-light-grey);\r\n  color: var(--color-primary-inactive-charcoal);\r\n  pointer-events: none;\r\n}\r\n\r\n.dropdownItem.itemWithDescription {\r\n  border: none;\r\n  padding: 0.5rem 1rem;\r\n\r\n  .labelWithDescription {\r\n    font-size: var(--fontsize-body);\r\n    font-weight: bold;\r\n    line-height: var(--lineheight-h6);\r\n  }\r\n\r\n  .itemDescription {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n}\r\n\r\n.dropdownItem.itemWithoutBorder {\r\n  border: none;\r\n}\r\n\r\n.ctaOnNoResults {\r\n  padding: 1rem;\r\n  color: var(--color-secondary-cobalt);\r\n  font-weight: bold;\r\n}\r\n\r\n.dropdownItem:hover,\r\n.dropdownItem.isSelected,\r\n.dropdownItem[data-highlighted],\r\n.customOption.isSelected {\r\n  background-color: var(--color-bg-light-grey);\r\n  transition: background-color 200ms linear;\r\n}\r\n\r\n.customOption button {\r\n  width: 100%;\r\n}\r\n\r\n.titleContainer {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.itemDescription {\r\n  display: block;\r\n  margin-left: 1.5rem;\r\n}\r\n\r\n.chipContainer {\r\n  background-color: var(--color-bg-white);\r\n  width: 100%;\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 2px;\r\n  cursor: pointer;\r\n  padding: 0.938rem;\r\n  line-height: 24px;\r\n  text-align: left;\r\n\r\n  &.selected {\r\n    border: 1px solid var(--color-primary-charcoal);\r\n  }\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-soft-charcoal);\r\n  }\r\n}\r\n\r\n.itemChip {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  border-radius: 6.25rem;\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  padding: 0.125rem 0.5rem;\r\n  width: fit-content;\r\n\r\n  &.selectedChip {\r\n    position: absolute;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    left: 1rem;\r\n    background-color: white;\r\n  }\r\n}\r\n\r\n.errorMessage {\r\n  margin-top: 0.75rem;\r\n  color: var(--color-secondary-burgundy);\r\n  font-size: var(--fontsize-body-small);\r\n  vertical-align: center;\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.renderTop {\r\n  top: -325px !important;\r\n}\r\n\r\n.isRequired{\r\n  font-size: var(--fontsize-body);\r\n  color: var(--color-primary-red);\r\n}\r\n\r\n.labelContainer{\r\n  display: flex;\r\n  gap: 3px;\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .renderTop {\r\n    top: -308px !important;\r\n  }\r\n}\r\n\r\n.dropdownScrollContainer {\r\n  max-height: 15rem;\r\n  overflow-y: auto;\r\n}\r\n\r\n.fixedCTAContainer {\r\n  position: sticky;\r\n  bottom: 0;\r\n  background-color: var(--color-bg-white);\r\n  border-top: 1px solid var(--color-primary-pale-charcoal);\r\n  padding: 0.5rem;\r\n  z-index: 1;\r\n}\r\n\r\n.customOption {\r\n  width: 100%;\r\n  text-align: left;\r\n  cursor: pointer;\r\n  background: none;\r\n  border: none;\r\n  padding: 0;\r\n}\r\n", ".textButton {\r\n  background-color: transparent;\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  color: var(--color-secondary-cobalt);\r\n  font-style: normal;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  border: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.textButton:disabled,\r\n.textButton:disabled:hover {\r\n  cursor: not-allowed;\r\n  text-decoration: none;\r\n  color: var(--color-bg-inactive-charcoal);\r\n  path {\r\n    fill: var(--color-bg-inactive-charcoal);\r\n  }\r\n}\r\n\r\n.textButton:focus-visible {\r\n  outline: 2px solid var(--color-secondary-cobalt);\r\n}\r\n\r\n.textButton.large,\r\n.textButton.medium {\r\n  gap: 0.5rem;\r\n}\r\n\r\n.textButton.small {\r\n  gap: 0.25rem;\r\n}\r\n\r\n.textButton.large {\r\n  font-size: var(--fontsize-body);\r\n}\r\n\r\n.textButton.medium {\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.textButton.small {\r\n  font-size: var(--fontsize-body-xsmall);\r\n}\r\n\r\n.textButton.inline {\r\n  font-size: var(--fontsize-body-small);\r\n  padding: 0;\r\n}\r\n\r\n.textButton:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.iconWrapper {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n", ".container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 0.5rem 1rem;\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 2px;\r\n  background: var(--color-bg-white);\r\n  gap: 0.75rem;\r\n}\r\n\r\n.firstStage {\r\n  align-items: center;\r\n  background: var(--color-bg-white);\r\n  display: flex;\r\n  width: 100%;\r\n  gap: 0.75rem;\r\n  justify-content: space-between;\r\n}\r\n\r\n.expandButton {\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  padding: 0;\r\n\r\n  svg {\r\n    path {\r\n      fill: var(--color-primary-inactive-charcoal);\r\n    }\r\n  }\r\n}\r\n\r\n.details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  width: 100%;\r\n}\r\n\r\n.stageName {\r\n  font-size: var(--fontsize-body);\r\n  font-weight: bold;\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.subStages {\r\n  font-size: var(--fontsize-body-xsmall);\r\n  color: var(--color-primary-inactive-charcoal);\r\n}\r\n\r\n.statusDropdown {\r\n  width: 100%;\r\n\r\n  :global(.dropdown-input-field) {\r\n    height: 2.25rem;\r\n    padding: 0.5rem 2.5rem 0.5rem 1rem;\r\n    font-size: 0.875rem;\r\n  }\r\n\r\n  :global(.input-left-icon) {\r\n    top: 0.65rem;\r\n    left: 1rem;\r\n  }\r\n}\r\n\r\n.statusDropdownWrapper {\r\n  :global(.dropdown-item) {\r\n    padding: 0.75rem 1rem;\r\n    gap: 0;\r\n  }\r\n\r\n  :global(.item-description.item-description) {\r\n    font-size: 0.875rem;\r\n    letter-spacing: -0.1px;\r\n    margin-bottom: 0;\r\n    line-height: 20px;\r\n  }\r\n\r\n  :global(.labelWithDescription) {\r\n    font-weight: 400;\r\n  }\r\n}\r\n\r\n\r\n\r\n.subStageContainer {\r\n  width: 100%;\r\n  padding: 0.5rem 0;\r\n}\r\n\r\n.subStage {\r\n  display: flex;\r\n  /* Keep flex-start but ensure input fields align properly */\r\n  align-items: flex-start;\r\n  padding: 0.75rem 0.75rem;\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 4px;\r\n  margin-bottom: 0.5rem;\r\n  background: var(--color-bg-white);\r\n  position: relative;\r\n  min-height: 48px;\r\n}\r\n\r\n.subStage > button:last-child {\r\n  flex: 0 0 auto;\r\n  margin-left: auto;\r\n}\r\n\r\n.substageNameField,\r\n.substageeDateField,\r\n.substageStatusField {\r\n  position: relative;\r\n  min-width: 0;\r\n  display: block;\r\n  min-height: 2.5rem;\r\n  height: auto;\r\n  padding-left: 0.75rem;\r\n}\r\n\r\n/* Override error styling to use consistent red color */\r\n.substageNameField :global(.input-field.error),\r\n.substageNameField :global(.dropdown-input-field.error) {\r\n  border-color: var(--color-primary-red) !important;\r\n}\r\n\r\n.substageeDateField .editProjectDateInput.errorState {\r\n  border-color: var(--color-primary-red) !important;\r\n}\r\n\r\n.substageStatusField :global(.dropdown-input-field.error) {\r\n  border-color: var(--color-primary-red) !important;\r\n}\r\n\r\n/* Hide error icons and messages */\r\n.substageNameField :global(.error-icon),\r\n.substageStatusField :global(.error-icon) {\r\n  display: none !important;\r\n}\r\n\r\n.substageNameField :global(.error-message),\r\n.substageStatusField :global(.error-message) {\r\n  display: none !important;\r\n}\r\n\r\n/* Ensure consistent field heights and alignment */\r\n.substageNameField :global(.input-field),\r\n.substageNameField :global(.input-wrapper),\r\n.substageStatusField :global(.dropdown-input-field),\r\n.substageeDateField .editProjectDateInput {\r\n  height: 2.5rem !important;\r\n  min-height: 2.5rem !important;\r\n  max-height: 2.5rem !important;\r\n  display: flex;\r\n  align-items: center;\r\n  /* Ensure input fields align at the same baseline */\r\n  margin-bottom: 0;\r\n  /* Create consistent baseline for all input types */\r\n  vertical-align: top;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n/* Additional specific styling for dropdown components */\r\n.substageNameField :global(.dropdown-input-field),\r\n.substageStatusField :global(.dropdown-input-field),\r\n.substageNameField :global(.dropdown-wrapper),\r\n.substageStatusField :global(.dropdown-wrapper),\r\n.substageNameField :global([data-scope=\"select\"]),\r\n.substageStatusField :global([data-scope=\"select\"]) {\r\n  height: 2.5rem !important;\r\n  min-height: 2.5rem !important;\r\n}\r\n\r\n/* Ensure dropdown trigger buttons have consistent height */\r\n.substageNameField :global(.dropdown-trigger),\r\n.substageStatusField :global(.dropdown-trigger),\r\n.substageNameField :global([data-part=\"trigger\"]),\r\n.substageStatusField :global([data-part=\"trigger\"]),\r\n.substageNameField :global(.trigger),\r\n.substageStatusField :global(.trigger) {\r\n  height: 2.5rem !important;\r\n  min-height: 2.5rem !important;\r\n  display: flex !important;\r\n  align-items: center !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n.substageNameField :global(.input-wrapper),\r\n.substageStatusField :global(.dropdown-input-wrapper) {\r\n  margin-bottom: 0;\r\n  height: 2.5rem !important;\r\n  max-height: 2.5rem !important;\r\n  font-size: 0.875rem !important;\r\n}\r\n\r\n/* Target all possible input text elements for consistent font size */\r\n.substageNameField :global(*) {\r\n  font-size: 0.875rem !important;\r\n  line-height: 1.25rem !important;\r\n}\r\n\r\n/* Specific styling for Input component to match dropdown size */\r\n.substageNameField :global(input) {\r\n  height: 2.5rem !important;\r\n  min-height: 2.5rem !important;\r\n  max-height: 2.5rem !important;\r\n  display: flex !important;\r\n  padding: 8px 12px !important;\r\n  align-items: center !important;\r\n  gap: 6px !important;\r\n  flex: 1 0 0 !important;\r\n  align-self: stretch !important;\r\n  border-radius: 2px !important;\r\n  box-sizing: border-box !important;\r\n  font-size: 0.875rem !important;\r\n  line-height: 1.25rem !important;\r\n}\r\n\r\n\r\n\r\n/* Ensure consistent padding and border styling across all input types */\r\n.substageNameField :global(.input-field),\r\n.substageNameField :global(.dropdown-input-field) {\r\n  display: flex !important;\r\n  padding: 8px 12px !important;\r\n  align-items: center !important;\r\n  gap: 6px !important;\r\n  flex: 1 0 0 !important;\r\n  align-self: stretch !important;\r\n  border-radius: 2px !important;\r\n  font-size: 0.875rem !important;\r\n  line-height: 1.25rem !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n/* Status field styling to match parent stage */\r\n.substageStatusField :global(.dropdown-input-field) {\r\n  display: flex !important;\r\n  padding: 8px 12px !important;\r\n  align-items: center !important;\r\n  gap: 6px !important;\r\n  flex: 1 0 0 !important;\r\n  align-self: stretch !important;\r\n  border-radius: 2px !important;\r\n  font-size: 0.875rem !important;\r\n  line-height: 1.25rem !important;\r\n  box-sizing: border-box !important;\r\n  /* Adjust padding for icon space - using balanced values */\r\n  padding-left: 2.2rem !important;\r\n  padding-right: 2.2rem !important;\r\n}\r\n\r\n/* Icon positioning for substage status field */\r\n.substageStatusField :global(.input-left-icon) {\r\n  top: 0.75rem;\r\n  left: 1rem;\r\n}\r\n\r\n/* Ensure Input component text has consistent font size */\r\n.substageNameField :global(.input-field),\r\n.substageNameField :global(.input-field input),\r\n.substageNameField :global(.input-wrapper input) {\r\n  font-size: 0.875rem !important;\r\n  line-height: 1.25rem !important;\r\n  font-family: inherit !important;\r\n}\r\n\r\n/* Ensure date picker button matches other input heights */\r\n.substageeDateField .editProjectDateInput {\r\n  display: flex !important;\r\n  padding: 8px 12px !important;\r\n  align-items: center !important;\r\n  flex: 1 0 0 !important;\r\n  align-self: stretch !important;\r\n  border-radius: 2px !important;\r\n  font-size: 0.875rem !important;\r\n  line-height: 1.25rem !important;\r\n  box-sizing: border-box !important;\r\n}\r\n\r\n/* Ensure all input elements have the same baseline alignment */\r\n.substageNameField > :first-child,\r\n.substageStatusField > :first-child,\r\n.substageeDateField > :first-child {\r\n  align-items: center;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* Ensure validation messages are positioned consistently across all field types */\r\n.substageNameField .inlineErrorMessage,\r\n.substageeDateField .inlineErrorMessage,\r\n.substageStatusField .inlineErrorMessage {\r\n  position: relative;\r\n  top: 0.25rem; /* Small spacing from the input field */\r\n  left: 0;\r\n  right: 0;\r\n  margin-top: 0.25rem;\r\n  margin-bottom: 0;\r\n}\r\n\r\n/* Inline error message styling */\r\n.inlineErrorMessage {\r\n  color: var(--color-primary-red);\r\n  font-size: 0.875rem;\r\n  font-weight: 400;\r\n  font-family: var(--primary-font-family);\r\n  margin-top: 0.65rem;\r\n  margin-bottom: 0;\r\n  /* padding-left: 0.6rem; */\r\n  line-height: 1.2;\r\n  /* Reserve consistent height to prevent layout shift */\r\n  min-height: 1rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  /* Position error messages in reserved space below inputs */\r\n  position: relative;\r\n  flex-shrink: 0;\r\n  /* Ensure consistent text rendering */\r\n  text-align: left;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.subStageName {\r\n  font-size: var(--fontsize-body-small);\r\n  font-weight: bold;\r\n}\r\n\r\n.deleteButton {\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n  padding: 0.5rem;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 32px;\r\n  height: 32px;\r\n\r\n  &:hover {\r\n    background-color: var(--color-bg-light-grey);\r\n  }\r\n\r\n  svg {\r\n    width: 16px;\r\n    height: 16px;\r\n    path {\r\n      fill: var(--color-primary-red);\r\n    }\r\n  }\r\n}\r\n\r\n.addSubstageContainer {\r\n  position: relative;\r\n  display: inline-block;\r\n  z-index: 1001;\r\n}\r\n\r\n.addSubstageButton {\r\n  color: var(--color-secondary-cobalt);\r\n  font-weight: 600;\r\n  font-size: 0.875rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  background: none;\r\n  border: none;\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    text-decoration: underline;\r\n  }\r\n\r\n  svg {\r\n    width: 12px;\r\n    height: 12px;\r\n    path {\r\n      fill: var(--color-secondary-cobalt);\r\n    }\r\n  }\r\n}\r\n\r\n.addSubstageDropdown {\r\n  position: fixed;\r\n  background: var(--color-bg-white);\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 4px;\r\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\r\n  z-index: 99999;\r\n  min-width: 200px;\r\n  max-width: 250px;\r\n  overflow: visible;\r\n  pointer-events: auto;\r\n}\r\n\r\n.dropdownOption {\r\n  display: block;\r\n  width: 100%;\r\n  padding: 0.75rem 1rem;\r\n  background: none;\r\n  border: none;\r\n  text-align: left;\r\n  font-size: 0.875rem;\r\n  color: var(--color-primary-charcoal);\r\n  cursor: pointer;\r\n  pointer-events: auto;\r\n  user-select: none;\r\n\r\n  &:hover {\r\n    background-color: var(--color-bg-light-grey);\r\n  }\r\n\r\n  &:first-child {\r\n    border-top-left-radius: 4px;\r\n    border-top-right-radius: 4px;\r\n  }\r\n\r\n  &:last-child {\r\n    border-bottom-left-radius: 4px;\r\n    border-bottom-right-radius: 4px;\r\n  }\r\n}\r\n\r\n.dateTrigger {\r\n  background: var(--color-bg-white);\r\n}\r\n\r\n.editProjectDateInput {\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 2px;\r\n  box-sizing: border-box;\r\n  color: var(--color-primary-charcoal);\r\n  font-family: inherit;\r\n  font-size: var(--fontsize-body);\r\n  height: 2.5rem;\r\n  line-height: var(--lineheight-body);\r\n  width: 100%;\r\n  display: flex;\r\n  padding: 0.5rem 0.625rem 0.5rem 1rem;\r\n  align-items: center;\r\n\r\n  &[data-state=\"open\"] {\r\n    border-color: var(--color-primary-charcoal);\r\n  }\r\n}\r\n\r\n.datePlaceholder {\r\n  align-self: center;\r\n  margin-left: 0.875rem;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: 20px;\r\n  font-weight: 400;\r\n  font-family: var(--primary-font-family);\r\n  width: 7.75rem;\r\n  text-align: start;\r\n}\r\n\r\n.dropdownArrow {\r\n  align-content: center;\r\n}\r\n\r\n.faded {\r\n  color: var(--color-primary-inactive-charcoal);\r\n}\r\n\r\n.dateInputError {\r\n  border-color: var(--color-primary-red) !important;\r\n}\r\n\r\n.customSubstageRow {\r\n  background-color: var(--color-bg-white);\r\n  border: 1px solid var(--color-border-pale-grey);\r\n}\r\n\r\n.errorMessage {\r\n  color: var(--color-primary-red);\r\n  font-size: 0.75rem;\r\n  margin-top: 0.25rem;\r\n  position: absolute;\r\n  top: 100%;\r\n  left: 0;\r\n  white-space: nowrap;\r\n  z-index: 10;\r\n  background: var(--color-bg-white);\r\n  padding: 0.125rem 0.25rem;\r\n  border-radius: 2px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* Fix datepicker z-index and positioning to appear above modal */\r\n.container :global([data-scope=\"date-picker\"][data-part=\"positioner\"]) {\r\n  z-index: 9999 !important;\r\n  position: fixed !important;\r\n}\r\n\r\n.container :global([data-scope=\"date-picker\"][data-part=\"content\"]) {\r\n  z-index: 9999 !important;\r\n  position: relative !important;\r\n}\r\n\r\n/* Ensure substage datepickers also appear above modal */\r\n.subStageContainer :global([data-scope=\"date-picker\"][data-part=\"positioner\"]) {\r\n  z-index: 9999 !important;\r\n  position: fixed !important;\r\n}\r\n\r\n.subStageContainer :global([data-scope=\"date-picker\"][data-part=\"content\"]) {\r\n  z-index: 9999 !important;\r\n  position: relative !important;\r\n}\r\n\r\n/* Drag and drop styles */\r\n.subStage {\r\n  cursor: grab;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.subStage:active {\r\n  cursor: grabbing;\r\n}\r\n\r\n.subStage.dragging {\r\n  opacity: 0.5;\r\n  transform: rotate(2deg);\r\n  cursor: grabbing;\r\n  z-index: 1000;\r\n}\r\n\r\n.subStage.dragOver {\r\n  border-color: var(--color-secondary-cobalt);\r\n  border-width: 2px;\r\n  background-color: #f2f7fb;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.dragHandle {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 0 0 auto;\r\n  width: 16px;\r\n  height: 42px;\r\n  cursor: grab;\r\n  color: var(--color-primary-inactive-charcoal);\r\n  transition: color 0.2s ease;\r\n}\r\n\r\n.dragHandle:hover {\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.subStage:active .dragHandle {\r\n  cursor: grabbing;\r\n}\r\n", ".control {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  box-sizing: border-box;\r\n  width: 2.25rem;\r\n  height: 1.25rem;\r\n  padding: 0.0625rem;\r\n  border: 1px solid var(--color-border-grey);\r\n  border-radius: 9999px;\r\n  background-color: var(--color-bg-grey);\r\n  transition: background-color 0.2s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.control:is(:checked, [data-checked], [aria-checked=\"true\"], [data-state=\"checked\"]) {\r\n  border: 1px solid var(--color-primary-red);\r\n  background-color: var(--color-primary-red);\r\n}\r\n\r\n.control:is(:disabled, [data-disabled]) {\r\n  border: 1px solid var(--color-border-grey);\r\n  background-color: var(--color-bg-white);\r\n  cursor: not-allowed;\r\n\r\n  &[data-state=\"checked\"],\r\n  [data-checked],\r\n  [aria-checked=\"true\"],\r\n  [data-state=\"checked\"] {\r\n    border: 1px solid transparent;\r\n    background-color: var(--color-primary-red-disabled);\r\n  }\r\n}\r\n\r\n.thumb {\r\n  width: 1rem;\r\n  height: 1rem;\r\n  border-radius: 9999px;\r\n  background-color: var(--color-bg-white);\r\n  transition: transform 0.2s cubic-bezier(0.2, 0, 0, 1);\r\n}\r\n\r\n.thumb:is(:checked, [data-checked], [aria-checked=\"true\"], [data-state=\"checked\"]) {\r\n  transform: translateX(100%);\r\n}\r\n\r\n.thumb:is(:disabled, [data-disabled]) {\r\n  background-color: var(--color-bg-grey);\r\n\r\n  &[data-state=\"checked\"],\r\n  [data-checked],\r\n  [aria-checked=\"true\"],\r\n  [data-state=\"checked\"] {\r\n    background-color: var(--color-bg-light-grey);\r\n  }\r\n}\r\n\r\n.root {\r\n  position: relative;\r\n  display: grid;\r\n  padding: 1px;\r\n}\r\n\r\n.root:is(:focus-visible, [data-focus]) {\r\n  border: 2px solid var(--color-secondary-cobalt);\r\n  border-radius: 100px;\r\n  margin: -2px;\r\n}\r\n", ".inlineMessage {\r\n  display: flex;\r\n  gap: 1rem;\r\n  padding: 0.75rem 1rem 1rem 1rem;\r\n  border-left: 4px solid var(--color-secondary-gold);\r\n  border-radius: 1px;\r\n  box-shadow: 0px 4px 8px 0px #00000026;\r\n  color: var(--color-primary-light-charcoal);\r\n  align-items: center;\r\n}\r\n\r\np.inlineMessageText {\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.inlineMessageContent {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.inlineMessageNoTitle {\r\n  padding-top: 0;\r\n}\r\n\r\n.inlineMessageTitle {\r\n  font-size: var(--fontsize-default);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n}\r\n\r\n.inlineMessageTitle,\r\n.inlineMessageText {\r\n  margin: 0;\r\n}\r\n\r\n.warning {\r\n  border-left: 4px solid var(--color-secondary-gold);\r\n}\r\n\r\n.success {\r\n  border-left: 4px solid var(--color-secondary-jade);\r\n}\r\n\r\n.info {\r\n  border-left: 4px solid var(--color-secondary-ocean);\r\n}\r\n\r\n.error {\r\n  border-left: 4px solid var(--color-secondary-burgundy);\r\n}\r\n", ".card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  padding: 0.75rem 1rem;\r\n  border-bottom: 1px solid var(--color-border-light-grey);\r\n}\r\n\r\n.card:first-of-type {\r\n  border-top-left-radius: 4px;\r\n  border-top-right-radius: 4px;\r\n}\r\n\r\n.card:last-of-type {\r\n  border-bottom-left-radius: 4px;\r\n  border-bottom-right-radius: 4px;\r\n}\r\n\r\n.card:hover {\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.userInfo {\r\n  display: flex;\r\n  flex-direction: column;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body);\r\n}\r\n\r\n.userName {\r\n  font-weight: 600;\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.userEmail {\r\n  color: var(--color-primary-inactive-charcoal);\r\n}\r\n\r\n.unassignedUser {\r\n  border-radius: 50%;\r\n  background-color: var(--color-bg-grey);\r\n  padding: 0.25rem;\r\n}\r\n", ".link {\r\n  text-decoration: none;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  width: 100%;\r\n  color: var(--color-bg-white);\r\n  font-size: 14px;\r\n  padding: 0.75rem;\r\n  border-radius: 2px;\r\n  transition: background-color 0.2s;\r\n\r\n  &:hover {\r\n    color: var(--color-bg-white);\r\n    background-color: var(--color-primary-light-charcoal);\r\n  }\r\n\r\n  &:focus-visible {\r\n    outline-color: var(--color-outline-dark-bg);\r\n    outline-offset: -3px;\r\n  }\r\n}\r\n\r\n.link i {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n}\r\n\r\n.active {\r\n  border-left: 1px solid #e81a3b;\r\n}\r\n\r\n.active {\r\n  color: var(--color-bg-white);\r\n  background-color: var(--color-primary-light-charcoal);\r\n  border-radius: 2px;\r\n}\r\n\r\n.longLabel {\r\n  white-space: normal;\r\n  word-break: break-word;\r\n  hyphens: auto;\r\n}\r\n", ".modalWrapper {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  display: flex;\r\n  align-items: end;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: var(--z-index-modal);\r\n}\r\n\r\n.overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: #000000;\r\n  opacity: 0.5;\r\n}\r\n\r\n.truncateTitle {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  max-width: 400px;\r\n  text-overflow: ellipsis;\r\n  word-wrap: normal;\r\n  word-break: normal;\r\n  hyphens: none;\r\n}\r\n\r\n.modal {\r\n  width: 100%;\r\n  position: relative;\r\n  max-height: 80%;\r\n  z-index: 1;\r\n  background-color: var(--color-bg-white);\r\n  box-shadow: 0px 4px 4px 0px #00000040;\r\n  border-top-left-radius: 1.5rem;\r\n  border-top-right-radius: 1.5rem;\r\n}\r\n\r\n.modalContent {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 65vh;\r\n  min-height: 0;\r\n}\r\n\r\n.modalHeader {\r\n  display: grid;\r\n  align-items: center;\r\n  padding: 1.5rem 1.5rem 0;\r\n}\r\n\r\n.modalHeader--withBorder {\r\n  border-bottom: 1px solid var(--color-border-pale-grey);\r\n  padding-bottom: 1rem;\r\n}\r\n\r\n.modalHeaderTextContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.modalTitleContainer {\r\n  display: flex;\r\n  align-items: center;\r\n  min-width: 0; \r\n  flex: 1;\r\n}\r\n\r\n.modalTitle {\r\n  color: var(--color-primary-charcoal);\r\n  margin: 0;\r\n  font-size: var(--fontsize-h4);\r\n  line-height: var(--lineheight-h4);\r\n  font-weight: 600;\r\n  outline: none;\r\n  word-wrap: break-word;\r\n  word-break: break-word;\r\n  hyphens: auto;\r\n}\r\n\r\n.padTitle {\r\n  padding-right: 3rem; /* Compensate for delete button overlap */\r\n}\r\n\r\n.modalSubtitle {\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 400;\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.informationContainer {\r\n  display: grid;\r\n  grid-template-columns: auto auto;\r\n  justify-content: start;\r\n  align-items: center;\r\n  column-gap: 0.5rem;\r\n  color: var(--color-bg-inactive-charcoal);\r\n}\r\n\r\n.informationContainer--error {\r\n  color: var(--color-secondary-burgundy);\r\n}\r\n\r\n.informationText {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.informationText--error {\r\n  color: var(--color-secondary-burgundy);\r\n}\r\n\r\n.slottedContent {\r\n  max-height: 25rem;\r\n  padding: 1.5rem;\r\n  height: 100%;\r\n  flex: 1; \r\n  min-height: 0;\r\n}\r\n\r\n.sectionedContent {\r\n  max-height: 2.75rem;\r\n  padding: 0.75rem;\r\n}\r\n\r\n.overflow {\r\n  overflow: auto;\r\n}\r\n\r\n.scroll {\r\n  overflow-y: auto;\r\n  min-height: 20rem;\r\n  max-height: 20rem;\r\n}\r\n\r\n.modalFooter {\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  position: relative;\r\n  background-color: var(--color-bg-white);\r\n  padding: 1.5rem 2rem;\r\n  border-top: 1px solid var(--color-border-pale-grey);\r\n  gap: 1rem;\r\n}\r\n\r\n.modalFooter--start {\r\n  justify-content: start;\r\n}\r\n\r\n.modalFooter--end {\r\n  justify-content: end;\r\n}\r\n\r\n.modalFooter--between {\r\n  justify-content: space-between;\r\n\r\n  button {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.infoButton {\r\n  background-color: transparent;\r\n  border: none;\r\n  display: flex;\r\n  border-radius: 50px;\r\n}\r\n\r\n.infoButton:hover {\r\n  cursor: pointer;\r\n  background-color: var(--color-primary-pale-charcoal);\r\n}\r\n\r\n.DocModal {\r\n  display: flex;\r\n  flex-direction: row-reverse;\r\n  position: relative;\r\n\r\n  .modalHeader {\r\n    padding: 16px 16px 0 16px;\r\n  }\r\n\r\n  .modalTitle {\r\n    color: var(--color-primary-charcoal);\r\n    font-size: var(--fontsize-body);\r\n    font-family: var(--primary-font-family);\r\n    font-weight: 600;\r\n    line-height: var(--lineheight-body);\r\n  }\r\n\r\n  .modalSubtitle {\r\n    color: var(--color-bg-inactive-charcoal);\r\n    font-family: var(--fontsize-body-small);\r\n    font-weight: 600;\r\n    line-height: var(--lineheight-body);\r\n    font-size: var(--fontsize-body-xsmall);\r\n  }\r\n\r\n  .informationContainer {\r\n    color: var(--color-primary-charcoal);\r\n    width: 268px;\r\n    border-bottom: 1px solid var(--color-border-pale-grey);\r\n  }\r\n\r\n  .informationText {\r\n    padding-bottom: 12px;\r\n  }\r\n\r\n  .slottedContent {\r\n    padding: 12px 0 8px 16px;\r\n  }\r\n\r\n  .chipStyling {\r\n    padding-top: 8px;\r\n    padding-bottom: 12px;\r\n    width: fit-content;\r\n  }\r\n\r\n  .bottomBorder {\r\n    width: 268px;\r\n    border-bottom: 1px solid var(--color-border-pale-grey);\r\n    height: 1px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  .overlay {\r\n    width: 0;\r\n    height: 0;\r\n    display: flex;\r\n  }\r\n\r\n  .modalWrapper {\r\n    width: auto;\r\n    height: auto;\r\n    left: auto;\r\n    top: auto;\r\n    display: flex;\r\n    flex-direction: row-reverse;\r\n    transform: translate(35px, 26px);\r\n    position: absolute;\r\n  }\r\n\r\n  .manageAccess {\r\n    font-weight: 600;\r\n    font-size: var(--fontsize-body-small);\r\n    color: var(--color-secondary-cobalt);\r\n    padding-top: 8px;\r\n    width: fit-content;\r\n  }\r\n\r\n  .manageAccess:hover {\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.newFolderButton {\r\n  position: absolute;\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  top: 40px;\r\n  left: 30px;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-weight: 600;\r\n  color: var(--color-secondary-cobalt);\r\n  padding: 0;\r\n\r\n  &:hover {\r\n    text-decoration: underline;\r\n  }\r\n}\r\n\r\n.slottedContentNoHeaderBorder {\r\n  padding: 0.5rem 1.5rem 1.5rem 1.5rem;\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .modalWrapper {\r\n    align-items: center;\r\n  }\r\n\r\n  .modal {\r\n    margin: 0 2rem;\r\n    max-height: 50rem;\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .modalHeader {\r\n    padding: 2rem 2rem 0;\r\n  }\r\n\r\n  .modalHeader--withBorder {\r\n    padding-bottom: 1.5rem;\r\n  }\r\n\r\n  .modalContent {\r\n    display: grid;\r\n    height: initial;\r\n    min-height: 0;\r\n  }\r\n\r\n  .modalLarge {\r\n    width: 49rem;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .modalMedium {\r\n    width: 37.5rem;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .modalSmall {\r\n    width: 35rem;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .modalMini {\r\n    width: 18.75rem;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .slottedContent {\r\n    padding: 1rem 1.5rem;\r\n  }\r\n\r\n  .slottedContentNoHeaderBorder {\r\n    padding: 1rem 2rem 2rem;\r\n  }\r\n\r\n  .modalFooter {\r\n    padding: 1.5rem 2rem;\r\n    border-bottom-right-radius: 2px;\r\n    border-bottom-left-radius: 2px;\r\n  }\r\n\r\n  .modalFooter--between {\r\n    button {\r\n      width: auto;\r\n    }\r\n  }\r\n}\r\n\r\n.closeModalButton {\r\n  position: absolute;\r\n  top: 2rem;\r\n  right: 2rem;\r\n}\r\n", ".notificationTypeBadge {\r\n  padding: 0.125rem 0.5rem;\r\n  align-items: center;\r\n  border-radius: 100px;\r\n  width: fit-content;\r\n}\r\n\r\n.notificationTypeBadgeText {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  font-family: var(--primary-font-family);\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n", ".progressContainer {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.progressSection {\r\n  height: 0.25rem;\r\n  background-color: var(--color-primary-pale-charcoal);\r\n}\r\n\r\n.merged {\r\n  height: 0.25rem;\r\n  background-color: var(--color-primary-red);\r\n  margin-left: -8px;\r\n}\r\n\r\n.merged:first-of-type {\r\n  margin-left: 0px;\r\n}\r\n\r\n@media (min-width: 769px) {\r\n  .progressSection {\r\n    width: 3rem;\r\n    height: 0.5rem;\r\n  }\r\n  \r\n  .active {\r\n    background-color: var(--color-primary-red);\r\n    position: relative;\r\n\r\n    & + .active {\r\n      &:before {\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 8px;\r\n        height: 8px;\r\n        background-color: var(--color-primary-red);\r\n        top: 0;\r\n        left: -8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n", ".container {\r\n  border: 1px solid #e0e0e0;\r\n  border-radius: 4px;\r\n  padding: 1rem;\r\n  background-color: var(--color-bg-white);\r\n  font-family: var(--primary-font-family);\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: var(--color-primary-charcoal);\r\n  margin: 0;\r\n  line-height: var(--lineheight-body);\r\n}\r\n\r\n.infoIcon {\r\n  font-size: 0.875rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.editContainer {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  align-items: center;\r\n  color: var(--color-secondary-cobalt);\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  padding: 0;\r\n\r\n  &:hover {\r\n    text-decoration: underline;\r\n  }\r\n}\r\n\r\n.lastUpdatedContainer {\r\n  margin: 1rem 0 2rem 0;\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.progressWrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 0.5rem;\r\n  justify-content: space-between;\r\n}\r\n\r\n.percentage {\r\n  font-size: 1.875rem;\r\n  font-weight: 600;\r\n  margin-right: 0.5rem;\r\n}\r\n\r\n.modalChildren {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.progressBarContainer {\r\n  flex-grow: 1;\r\n  height: 12px;\r\n  border-radius: 4px;\r\n  background-color: var(--color-bg-primary-pale-charcoal);\r\n  overflow: hidden;\r\n  position: relative;\r\n  float: left;\r\n  width: 100%;\r\n  margin-top: 21px;\r\n}\r\n\r\n.progressBarContainerModal {\r\n  width: 80%;\r\n}\r\n\r\n.progressBar {\r\n  height: 100%;\r\n  background-color: var(--color-secondary-cobalt);\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n\r\n.endDateContainer {\r\n  font-size: 1rem;\r\n  color: var(--color-primary-inactive-charcoal);\r\n  text-align: right;\r\n}\r\n\r\n.endDate {\r\n  font-weight: 600;\r\n}\r\n\r\n.modalChildren {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n}\r\n\r\n.inputWrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  width: 20%;\r\n}\r\n\r\n.successToast {\r\n  position: absolute;\r\n  bottom: 1rem;\r\n  right: 2rem;\r\n}\r\n", ".sectionBanner {\r\n  width: 100%;\r\n  padding: 0.5rem 1.5rem;\r\n  color: var(--color-primary-charcoal);\r\n  font-size: var(--fontsize-body-eyebrow);\r\n  line-height: var(--lineheight-body-eyebrow);\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  border-radius: 2px;\r\n  background-color: var(--color-bg-light-grey);\r\n  letter-spacing: 0.1rem;\r\n}\r\n\r\n.sectionBanner--highlighted {\r\n  background-color: #0062b81a;\r\n}\r\n", ".selectionCard {\r\n  display: grid;\r\n  grid-template-columns: auto 1fr auto;\r\n  justify-items: start;\r\n  width: 100%;\r\n  padding: 1.5rem 1rem;\r\n  margin: 0;\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  background-color: var(--color-bg-white);\r\n  cursor: pointer;\r\n  transition: background-color 0.5s ease;\r\n}\r\n\r\n.selectionCard:hover {\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.selectionCard--charcoal {\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.selectionCard--charcoal:hover {\r\n  background-color: var(--color-bg-white);\r\n}\r\n\r\n.title {\r\n  color: var(--color-primary-charcoal);\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n  text-align: left;\r\n}\r\n\r\n.subtitle {\r\n  color: var(--color-primary-light-charcoal);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  margin-top: 0.5rem;\r\n  text-align: start;\r\n}\r\n\r\n.leftIconContainer {\r\n  margin-right: 1rem;\r\n  width: 1.5rem;\r\n  height: 1.5rem;\r\n\r\n  svg {\r\n    width: 1.25rem;\r\n    height: 1.25rem;\r\n  }\r\n}\r\n\r\n.textContainer {\r\n  display: grid;\r\n  justify-items: start;\r\n}\r\n\r\n.rightIconContainer {\r\n  justify-self: end;\r\n  align-self: center;\r\n  margin-left: 1rem;\r\n  width: 1.5rem;\r\n  height: 1.5rem;\r\n  color: var(--color-secondary-cobalt);\r\n\r\n  svg {\r\n    width: 1.25rem;\r\n    height: 1.25rem;\r\n  }\r\n}\r\n\r\n.selectionCardDisabled {\r\n  cursor: not-allowed;\r\n  background: var(--color-bg-light-grey);\r\n\r\n  .title, .subtitle {\r\n    color: var(--color-primary-inactive-charcoal);\r\n  }\r\n}\r\n\r\n.burgundy {\r\n  color: var(--color-secondary-burgundy);\r\n}\r\n\r\n.emerald {\r\n  color: var(--color-secondary-emerald);\r\n}\r\n\r\n.ocean {\r\n  color: var(--color-secondary-ocean);\r\n}\r\n\r\n.gold {\r\n  color: var(--color-secondary-gold);\r\n}\r\n\r\n.jade {\r\n  color: var(--color-secondary-jade);\r\n}\r\n", ".fieldRoot {\r\n  width: 100%;\r\n}\r\n\r\n.userSelectInput {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  border: 0.0625rem solid var(--color-border-pale-grey);\r\n  border-radius: 2px;\r\n  padding: 0.5rem 1rem;\r\n  background-color: var(--color-bg-white);\r\n  transition: background-color 0.2s;\r\n\r\n  &::placeholder {\r\n    color: var(--color-bg-inactive-charcoal);\r\n  }\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-soft-charcoal);\r\n  }\r\n\r\n  &:focus-within {\r\n    outline: none;\r\n\r\n    &:global(.focusedByKeyboard) {\r\n      outline: 2px solid var(--color-secondary-cobalt);\r\n    }\r\n  }\r\n}\r\n\r\n.useMaxWidth {\r\n  width: 100%;\r\n}\r\n\r\n.userSelectInput:focus-visible,\r\n.userSelectInput[data-state=\"focus\"] {\r\n  outline: none;\r\n  border: 1px solid var(--color-primary-charcoal);\r\n\r\n  &:global(.focusedByKeyboard) {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: 2px;\r\n  }\r\n}\r\n\r\n.userSelectInputError,\r\n.userSelectInputError[data-state=\"focus\"] {\r\n  border-color: var(--color-secondary-burgundy);\r\n}\r\n\r\n.selectedUsers {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.5rem;\r\n  width: 100%;\r\n  max-height: 7.5rem;\r\n  overflow-y: auto;\r\n}\r\n\r\n.label {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  min-width: 1rem;\r\n  flex-grow: 1;\r\n}\r\n\r\n.unassignedUser {\r\n  border-radius: 50%;\r\n  background-color: var(--color-bg-grey);\r\n  padding: 0.25rem;\r\n}\r\n\r\n.input {\r\n  background-color: transparent;\r\n  color: var(--color-primary-charcoal);\r\n  border: none;\r\n  outline: none;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  padding: 0;\r\n  width: 0;\r\n  min-width: 100%;\r\n  font-family: var(--primary-font-family);\r\n}\r\n\r\n.dropdown {\r\n  position: absolute;\r\n  background-color: var(--color-bg-white);\r\n  border-radius: 0.25rem;\r\n  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);\r\n  max-height: 11.75rem;\r\n  overflow-y: auto;\r\n  width: 100%;\r\n  z-index: 10;\r\n  top: 100%;\r\n  left: 0;\r\n  list-style: none;\r\n  padding: 0;\r\n}\r\n\r\n.dropdownItem {\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  color: var(--color-primary-charcoal);\r\n  transition: background-color 0.2s;\r\n\r\n  &[data-highlighted] {\r\n    background-color: var(--color-bg-light-grey);\r\n  }\r\n}\r\n\r\n.dropdownItem:hover {\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.noResults {\r\n  padding: 0.5rem;\r\n  color: var(--color-primary-charcoal);\r\n  font-size: 1rem;\r\n  text-align: center;\r\n}\r\n\r\n.errorTextContainer {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  color: var(--color-secondary-burgundy);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  margin-top: 0.75rem;\r\n}\r\n\r\n.errorText {\r\n  margin: 0;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body);\r\n}\r\n", ".statusBadge,\r\n.actionItemStatusBadgeWrapper .statusBadge,\r\n.actionItemStatusBadgeWrapper [data-scope=\"menu\"][data-part=\"trigger\"] {\r\n  position: relative;\r\n  display: grid;\r\n  grid-template-columns: auto 1fr auto;\r\n  column-gap: 0.5rem;\r\n  padding: 0.125rem 0.5rem 0.125rem 0.25rem;\r\n  justify-content: start;\r\n  align-items: center;\r\n  background-color: var(--color-primary-white);\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  border-radius: 100px;\r\n  color: var(--color-primary-slate);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  transition: 100ms ease-out;\r\n  width: max-content;\r\n\r\n  &.clickable {\r\n    transition: background-color 0.2s;\r\n\r\n    &:hover {\r\n      background-color: var(--color-bg-light-grey);\r\n    }\r\n\r\n    &:focus-visible {\r\n      outline: 2px solid var(--color-secondary-cobalt);\r\n      outline-offset: 2px;\r\n    }\r\n  }\r\n}\r\n\r\n.statusBadge[disabled] {\r\n  cursor: unset;\r\n}\r\n\r\n.actionItemStatusBadgeWrapper\r\n  div[data-part=\"positioner\"][id=\"menu:actionItemBadge:popper\"] {\r\n  position: relative !important;\r\n  transform: translate3d(0px, 2px, 0) !important;\r\n}\r\n\r\n.actionItemStatusBadgeWrapper\r\n  div[id=\"menu:actionItemBadge:content\"][data-state=\"open\"][aria-labelledby=\"menu:actionItemBadge:trigger\"] {\r\n  z-index: var(--z-index-chip-dropdown);\r\n}\r\n\r\n.statusBadgeText {\r\n  color: var(--color-primary-charcoal);\r\n  font-family: \"ProximaNova\", Arial, sans-serif;\r\n}\r\n\r\n.statusIconContainer {\r\n  padding: 0.25rem;\r\n  border-radius: 50%;\r\n}\r\n\r\n.itemContainer[data-scope=\"menu\"][data-part=\"item\"] {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  padding: 1rem 1rem;\r\n  width: 100%;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.itemContainer:hover {\r\n  background-color: var(--color-bg-light-grey);\r\n  cursor: pointer;\r\n}\r\n\r\n.itemTextContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  height: 40px;\r\n  width: max-content;\r\n}\r\n\r\n.itemLabel {\r\n  font-size: var(--fontsize-body);\r\n}\r\n\r\n.itemText {\r\n  font-size: var(--fontsize-body-small);\r\n  color: var(--color-primary-inactive-charcoal);\r\n}\r\n\r\n/* TODO: make these not affect globally */\r\n\r\n[data-scope=\"menu\"][data-part=\"positioner\"] {\r\n  display: contents;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"content\"] {\r\n  outline: none;\r\n  display: block;\r\n  border: 1px solid var(--color-border-grey);\r\n  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);\r\n  padding: 0;\r\n  position: relative;\r\n  z-index: var(--z-index-dropdown-list);\r\n  position: absolute;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"content\"][data-state=\"closed\"] {\r\n  visibility: hidden;\r\n  opacity: 0;\r\n  transition: opacity 200ms linear, visibility 200ms linear;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"content\"][data-state=\"open\"] {\r\n  background-color: var(--color-bg-white);\r\n  visibility: visible;\r\n  opacity: 1;\r\n  transition: opacity 200ms linear, visibility 200ms linear;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"item\"] {\r\n  cursor: pointer;\r\n  display: flex;\r\n  padding: 0 1rem;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  align-self: stretch;\r\n  color: var(--color-primary-charcoal);\r\n  font-family: \"ProximaNova\", Arial, sans-serif;\r\n  font-size: var(--fontsize-body);\r\n  font-style: normal;\r\n  font-weight: 400;\r\n  line-height: var(--lineheight-body);\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"item\"].labelWithIcon {\r\n  border: none;\r\n  padding-left: 8px;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"item\"].critical {\r\n  color: var(--color-secondary-burgundy);\r\n}\r\n\r\n.withIcon {\r\n  display: flex;\r\n  flex-direction: row;\r\n  border: transparent;\r\n}\r\n\r\n.withIcon:hover {\r\n  cursor: pointer;\r\n  background-color: var(--color-primary-pale-charcoal);\r\n\r\n  [data-scope=\"menu\"][data-part=\"item\"] {\r\n    background-color: var(--color-primary-pale-charcoal);\r\n  }\r\n}\r\n\r\n.forIcon {\r\n  display: flex;\r\n  flex-direction: column;\r\n  margin-top: 0.75rem;\r\n}\r\n\r\n.actionItemStatusBadgeWrapper\r\n  > [id=\"menu:actionItemBadge:popper\"][aria-expanded=\"true\"] {\r\n  display: none;\r\n}\r\n\r\n.statusIconContainer {\r\n  position: relative;\r\n  width: 1rem;\r\n  height: 1rem;\r\n  border-radius: 50%;\r\n  opacity: 0;\r\n}\r\n\r\n.statusIcon {\r\n  position: absolute;\r\n  top: 7px;\r\n  left: 7px;\r\n}\r\n\r\n.statusIconLarge {\r\n  top: 4px;\r\n  left: 4px;\r\n}\r\n\r\n.statusIconContainerWithPulse {\r\n  animation: pulse 2.75s infinite;\r\n  position: relative;\r\n  width: 1rem;\r\n  height: 1rem;\r\n  border-radius: 50%;\r\n  opacity: 0;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(0);\r\n    opacity: 0;\r\n  }\r\n  50% {\r\n    transform: scale(1.25);\r\n    opacity: 1;\r\n  }\r\n  75% {\r\n    transform: scale(1.25);\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    transform: scale(0);\r\n    opacity: 0;\r\n  }\r\n}\r\n", ".statusChip {\r\n  display: flex;\r\n  flex-direction: row;\r\n  gap: 0.25rem;\r\n  padding: 0.125rem 0.5rem 0.125rem 0.25rem;\r\n  justify-content: start;\r\n  align-items: center;\r\n  background-color: var(--color-primary-white);\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 100px;\r\n  color: var(--color-primary-slate);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  transition: 100ms ease-out;\r\n}\r\n\r\n.statusChip:hover {\r\n  cursor: pointer;\r\n  background-color: var(--color-bg-light-grey);\r\n  border-color: var(--color-border-light-grey);\r\n}\r\n\r\n.statusChipText {\r\n  color: var(--color-primary-charcoal);\r\n  font-family: \"ProximaNova\", Arial, sans-serif;\r\n}\r\n", ".tab {\r\n  padding: 0.75rem 0.5rem;\r\n  text-decoration: none;\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  font-weight: 600;\r\n  color: var(--color-bg-inactive-charcoal);\r\n  border-bottom: 3px solid transparent;\r\n  cursor: pointer;\r\n  border-radius: 0;\r\n  display: inline-block;\r\n}\r\n\r\n.tab:hover {\r\n  color: var(--color-bg-inactive-charcoal);\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.tab:focus-visible {\r\n  outline: 2px solid var(--color-secondary-cobalt);\r\n  outline-offset: 2px;\r\n}\r\n\r\n.tab.active {\r\n  color: var(--color-primary-charcoal);\r\n  border-bottom: 3px solid var(--color-primary-red);\r\n}\r\n", ".tableContainer {\r\n  border-radius: 8px;\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  background-color: var(--color-bg-white);\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.containerNoScroll {\r\n  overflow: hidden;\r\n}\r\n\r\n.containerWithScroll {\r\n  overflow: auto;\r\n}\r\n\r\n.table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  border: none;\r\n  margin: 0;\r\n  flex: 1;\r\n}\r\n\r\n.table thead {\r\n  position: sticky;\r\n  top: 0;\r\n  background-color: #f5f5f5;\r\n  z-index: var(--z-index-table-header);\r\n}\r\n\r\n/* Fix for rendering bug where a pixel peeks underneath */\r\n.table thead::before {\r\n  content: \"\";\r\n  display: block;\r\n  position: absolute;\r\n  background-color: #f5f5f5;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 2px;\r\n  top: -1px;\r\n}\r\n\r\n.tableHeader,\r\n.tableCell {\r\n  padding: 0.75rem;\r\n  border: none;\r\n}\r\n\r\n.tableRow {\r\n  border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n\r\n  [data-scope=\"menu\"][data-part=\"trigger\"] {\r\n    background-color: transparent;\r\n  }\r\n\r\n  [data-scope=\"menu\"][data-part=\"trigger\"]:hover {\r\n    background-color: var(--color-bg-light-grey);\r\n  }\r\n}\r\n\r\n.tableRow:hover {\r\n  background-color: var(--color-primary-soft-charcoal);\r\n}\r\n\r\n.rowClick {\r\n  cursor: pointer;\r\n\r\n  &:focus-visible {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: -2px;\r\n  }\r\n}\r\n\r\n.tableHeader {\r\n  text-align: left;\r\n  padding: 0.75rem;\r\n  background-color: #f5f5f5;\r\n  color: var(--color-primary-light-charcoal);\r\n  font-size: var(--fontsize-body-xsmall);\r\n  text-transform: uppercase;\r\n  border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n  white-space: nowrap;\r\n}\r\n\r\n.tableHeaderCell,\r\n.selectCell,\r\n.titleCell,\r\n.filesCell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-family: var(--primary-font-family);\r\n}\r\n\r\n.tableHeaderCell button {\r\n  font-weight: 600;\r\n  color: var(--color-primary-light-charcoal);\r\n  border: 0;\r\n  padding: 0.75rem;\r\n  width: -webkit-fill-available;\r\n  margin: -0.75rem;\r\n  border-radius: 0;\r\n  transition: 0.2s background-color;\r\n\r\n  &:focus-visible {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: -3px;\r\n  }\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-pale-charcoal);\r\n  }\r\n}\r\n\r\n.selectHeader,\r\n.selectTableCell,\r\n.selectTrashHeader,\r\n.selectTrashTableCell {\r\n  width: 0; /* Zero-width makes cell fit content */\r\n}\r\n\r\n.modifiedCell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  justify-content: flex-start;\r\n  white-space: nowrap;\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.actions {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 1rem;\r\n  margin-left: auto;\r\n}\r\n\r\n.notificationActivityTableCell {\r\n  height: 3.75rem;\r\n}\r\n\r\n.nameCell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  flex: 1;\r\n  margin-left: -8px;\r\n}\r\n\r\n.taskName {\r\n  font-weight: bold;\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.tag {\r\n  display: inline-block;\r\n  background: #f0f0f0;\r\n  color: var(--color-primary-charcoal);\r\n  font-size: var(--fontsize-body-xsmall);\r\n  padding: 2px 8px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.statusCell {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.statusCircle {\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background: var(--color-secondary-cobalt);\r\n  margin-right: 8px;\r\n}\r\n\r\n.dueDateCell,\r\n.modifiedCell {\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n\r\n.assigneeCell {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.assignee,\r\n.avatar,\r\n.initials {\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--fontsize-body-xsmall);\r\n  font-weight: bold;\r\n}\r\n\r\n.assignee {\r\n  background: var(--color-secondary-cobalt);\r\n  color: white;\r\n}\r\n\r\n.initials {\r\n  background: #888;\r\n  color: white;\r\n  font-size: 0.85rem;\r\n}\r\n\r\n.selectCell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.titleCell {\r\n  flex-grow: 1;\r\n  color: var(--color-primary-light-charcoal);\r\n  font-weight: 600;\r\n}\r\n\r\n.filesCell {\r\n  color: var(--color-bg-inactive-charcoal);\r\n  font-weight: 400;\r\n}\r\n\r\n.selectCellIcon {\r\n  padding: 0.5rem;\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n.sortableHeader {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.emptyMessageContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  color: var(--color-bg-inactive-charcoal);\r\n  padding: 2rem;\r\n  margin: 1rem;\r\n  border-radius: 2px;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 90%;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.emptyMessageText {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.emptyMessageTitle {\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n}\r\n\r\n.emptyMessageSubMessage {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n}\r\n\r\n.clientTableHeader {\r\n  gap: 0.5rem;\r\n}\r\n\r\n.clientTableCell {\r\n  padding: 1.125rem 0.75rem;\r\n}\r\n\r\n.trashActionItemHeader,\r\n.trashActionItemCell {\r\n  display: none;\r\n}\r\n.placeholderItemRow {\r\n  cursor: not-allowed;\r\n  border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n}\r\n\r\n.placeholderSelectCell {\r\n  pointer-events: none;\r\n}\r\n\r\n.skeletonItemCell {\r\n  position: relative;\r\n  overflow: hidden;\r\n  background-color: var(--color-bg-light-grey);\r\n  border-radius: 4px;\r\n  height: 1rem;\r\n  width: 80%;\r\n}\r\n\r\n.skeletonItemCell::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100%;\r\n  width: 80%;\r\n  background-image: linear-gradient(\r\n    90deg,\r\n    rgba(242, 242, 242, 0) 0%,\r\n    rgba(255, 255, 255, 0.35) 50%,\r\n    rgba(242, 242, 242, 0) 100%\r\n  );\r\n  background-size: 200% 100%;\r\n  background-repeat: no-repeat;\r\n  animation: shimmer 1.2s infinite linear;\r\n}\r\n\r\n.iconCell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  padding: 0.5rem;\r\n}\r\n\r\n.placeholderItem {\r\n  padding: 0.75rem;\r\n}\r\n\r\n.placeholderItemCell {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n.placeholderItemText {\r\n  display: flex;\r\n  flex-direction: column;\r\n  opacity: 0.5;\r\n}\r\n\r\n.placeholderItemLoadingText {\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.highlightImportantRow {\r\n  background-color: var(--color-bg-warning-yellow-translucent);\r\n}\r\n\r\n@media (min-width: 1280px) {\r\n  .trashActionItemHeader,\r\n  .trashActionItemCell {\r\n    display: table-cell;\r\n  }\r\n}\r\n\r\n.hideOnSmallScreen,\r\n.hideOnMediumScreen {\r\n  display: none;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .hideOnSmallScreen {\r\n    display: table-cell;\r\n  }\r\n}\r\n\r\n@media (min-width: 1024px) {\r\n  .hideOnMediumScreen {\r\n    display: table-cell;\r\n  }\r\n}\r\n\r\n.teamMemberTableCell {\r\n  padding: 0.5rem 1rem;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0% {\r\n    background-position: -150% 0;\r\n  }\r\n  100% {\r\n    background-position: 150% 0;\r\n  }\r\n}\r\n", ".tabsContainerWrapper {\r\n  position: relative;\r\n  width: 100%;\r\n  scrollbar-width: none;\r\n  white-space: nowrap;\r\n  box-sizing: border-box;\r\n  padding: 0 2rem;\r\n\r\n  @media (max-width: 768px) {\r\n    overflow-x: auto;\r\n  }\r\n}\r\n\r\n.tabsContainerWrapper::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n.tabsContainer {\r\n  display: flex;\r\n  gap: 10px;\r\n  min-width: max-content;\r\n}\r\n", "[data-scope=\"menu\"][data-part=\"trigger\"] {\r\n  display: grid;\r\n  grid-template-columns: auto auto;\r\n  align-items: center;\r\n  justify-content: center;\r\n  column-gap: 0.5rem;\r\n  outline: none;\r\n  border: none;\r\n  padding: 0.5rem;\r\n  border-radius: 2px;\r\n  margin: 0;\r\n  background-color: var(--color-primary-white);\r\n  color: var(--color-primary-slate);\r\n  visibility: visible;\r\n  position: relative;\r\n}\r\n\r\n/* TODO: Finish the implementation of quick add post-MVP */\r\n/* See ticket 8091 */\r\n/* Based on existing TODO on the ProjectTasksTable.tsx line 37 */\r\n/* .parent [data-scope=\"menu\"][data-part=\"trigger\"]:hover,\r\n[data-scope=\"menu\"][data-part=\"trigger\"]:focus {\r\n  background-color: var(--color-bg-light-grey);\r\n  cursor: pointer;\r\n} */\r\n\r\n/* [data-scope=\"menu\"][data-part=\"trigger\"]:focus {\r\n  border: 1px solid var(--color-secondary-cobalt);\r\n  margin: -1px;\r\n} */\r\n\r\n/* Removing since it is affecting global styling for other components */\r\n/* [data-scope=\"menu\"][data-part=\"trigger\"][data-state=\"open\"] {\r\n  background-color: var(--color-bg-light-grey);\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"trigger\"][data-state=\"open\"] {\r\n  background-color: var(--color-bg-light-grey);\r\n} */\r\n\r\n/* TODO: make these not affect globally */\r\n\r\n[data-scope=\"menu\"][data-part=\"positioner\"] {\r\n  display: block;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"content\"] {\r\n  border-radius: 2px;\r\n  box-shadow: 0px 4px 4px 0px #00000040;\r\n  padding: 0.5rem 0;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"content\"]:focus-visible {\r\n  outline: none;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"item\"] {\r\n  display: grid;\r\n  grid-template-columns: auto 1fr;\r\n  column-gap: 1rem;\r\n  align-items: center;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--fontsize-body);\r\n  color: var(--color-primary-charcoal);\r\n  width: max-content;\r\n}\r\n\r\n[data-scope=\"menu\"][data-part=\"item\"]:hover,\r\n[data-scope=\"menu\"][data-part=\"item\"][data-highlighted] {\r\n  background-color: var(--color-bg-light-grey);\r\n  cursor: pointer;\r\n}\r\n", ".container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n  width: 100%;\r\n  position: relative;\r\n}\r\n\r\n.hiddenLabel {\r\n  display: none;\r\n}\r\n\r\n.label {\r\n  font-weight: 600;\r\n  line-height: var(--lineheight-h6);\r\n  font-size: var(--fontsize-body);\r\n}\r\n\r\n.maxLength {\r\n  color: var(--color-primary-inactive-charcoal);\r\n  line-height: var(--lineheight-body);\r\n  position: absolute;\r\n  right: 0;\r\n}\r\n\r\n.textArea {\r\n  box-sizing: border-box;\r\n  resize: none;\r\n  overflow: hidden;\r\n  font-size: var(--fontsize-body);\r\n  font-family: var(--primary-font-family);\r\n  padding: 0.75rem 1rem;\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 2px;\r\n  min-height: 80px;\r\n  background-color: var(--color-bg-white);\r\n  color: var(--color-primary-charcoal);\r\n  transition: border-color 0.3s ease, box-shadow 0.3s ease,\r\n    background-color 0.2s;\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-soft-charcoal);\r\n  }\r\n}\r\n\r\n.textArea::placeholder {\r\n  color: var(--color-primary-inactive-charcoal);\r\n}\r\n\r\n.textArea:focus-visible {\r\n  outline: none;\r\n  border: 1px solid var(--color-primary-charcoal);\r\n\r\n  &:global(.focusedByKeyboard) {\r\n    border: 1px solid var(--color-border-pale-grey);\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n    outline-offset: 2px;\r\n  }\r\n}\r\n\r\n.textArea.error {\r\n  border-color: var(--color-secondary-burgundy);\r\n}\r\n\r\n.textArea.success {\r\n  border-color: var(--color-secondary-jade);\r\n}\r\n\r\n.info {\r\n  display: flex;\r\n  flex-direction: row;\r\n  gap: 3px;\r\n  font-size: var(--fontsize-body-small);\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.requiredStar{\r\n  font-size: var(--fontsize-body);\r\n  color: var(--color-primary-red);\r\n}\r\n\r\n.errorMessage {\r\n  color: var(--color-secondary-burgundy);\r\n}\r\n", ".textCarousel {\r\n  display: flex;\r\n  width: 450px;\r\n  padding: 47px 16px 16px 16px;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 24px;\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-border-grey);\r\n  background-color: var(--color-bg-white);\r\n  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.16);\r\n  overflow: hidden;\r\n  position: relative;\r\n\r\n  .didYouKnow {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 1rem;\r\n    font-size: var(--fontsize-body-eyebrow);\r\n    font-style: normal;\r\n    font-weight: 600;\r\n    line-height: var(--lineheight-body-eyebrow);\r\n    letter-spacing: 1px;\r\n    text-transform: uppercase;\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    padding: 4px 12px;\r\n    justify-content: center;\r\n    gap: 4px;\r\n    border-radius: 0px 0px 8px 8px;\r\n    background: linear-gradient(\r\n        0deg,\r\n        rgba(26, 99, 175, 0.2) 0%,\r\n        rgba(26, 99, 175, 0.2) 100%\r\n      ),\r\n      #fff;\r\n  }\r\n\r\n  .carouselItems {\r\n    .carouselItem {\r\n      margin-right: 1rem;\r\n\r\n      .itemText {\r\n        margin-top: 0;\r\n        margin-bottom: 0.5rem;\r\n        font-size: var(--fontsize-h5);\r\n        line-height: var(--lineheight-h5);\r\n        font-weight: 600;\r\n      }\r\n\r\n      .itemSource {\r\n        color: var(--color-primary-slate);\r\n        font-size: var(--fontsize-body-small);\r\n        font-style: normal;\r\n        font-weight: 400;\r\n        line-height: var(--lineheight-body-small);\r\n        letter-spacing: -0.1px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .indicators {\r\n    display: flex;\r\n    flex-direction: row;\r\n    gap: 12px;\r\n\r\n    .indicator {\r\n      width: 24px;\r\n      height: 8px;\r\n    }\r\n\r\n    .indicator[data-state=\"active\"] {\r\n      background-color: var(--color-primary-red);\r\n    }\r\n\r\n    .indicator[data-state=\"inactive\"] {\r\n      background-color: var(--color-border-grey);\r\n    }\r\n  }\r\n}\r\n", ".toast {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 0.65rem 1rem;\r\n  border-radius: 2px;\r\n  color: var(--color-primary-charcoal);\r\n  cursor: pointer;\r\n  opacity: 0.9;\r\n  transform: translateX(100%);\r\n  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;\r\n  opacity: 0;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.toast.show {\r\n  transform: translateX(0);\r\n  opacity: 1;\r\n}\r\n\r\n.toast.hide {\r\n  transform: translateX(100%);\r\n  opacity: 0;\r\n}\r\n\r\n.toastContainer {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 1rem;\r\n  width: 100%;\r\n}\r\n\r\n.toastText {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.15rem;\r\n}\r\n\r\n.toastTitle {\r\n  font-weight: 600;\r\n}\r\n\r\n.toastCTA {\r\n  cursor: pointer;\r\n  color: var(--color-secondary-cobalt);\r\n  font-weight: bold;\r\n}\r\n\r\n.toastTitle,\r\n.toastMessage {\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  word-break: break-word;\r\n  margin: 0;\r\n}\r\n\r\n.toastMessage {\r\n  display: flex;\r\n  gap: 1rem;\r\n}\r\n\r\n.success {\r\n  background-color: var(--color-bg-light-emerald);\r\n  border: 1px solid var(--color-secondary-emerald);\r\n}\r\n\r\n.error {\r\n  background-color: var(--color-bg-light-burgundy);\r\n  border: 1px solid var(--color-secondary-burgundy);\r\n}\r\n\r\n.info {\r\n  background-color: var(--color-bg-light-cobalt);\r\n  border: 1px solid var(--color-secondary-cobalt);\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .toast.show {\r\n    transform: translateY(0);\r\n    opacity: 1;\r\n  }\r\n\r\n  .toast.hide {\r\n    transform: translateY(100%);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .toast {\r\n    max-width: 700px;\r\n  }\r\n}\r\n", ".toggleListItem {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem 1.5rem;\r\n}\r\n\r\n.toggleListItemWithBorder {\r\n  border-bottom: 1px solid var(--color-border-light-grey);\r\n}\r\n\r\n.toggleListInfo {\r\n  margin-right: 1rem;\r\n}\r\n\r\n.titleContainer {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  color: var(--color-secondary-cobalt);\r\n  font-weight: 400;\r\n}\r\n\r\n.title {\r\n  margin-right: 0.5rem;\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.subtitle {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  color: var(--color-primary-light-charcoal);\r\n}\r\n", ".container {\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.container [data-part=\"input\"] {\r\n  display: block;\r\n  width: 100%;\r\n  font-family: var(--primary-font-family);\r\n  font-size: var(--fontsize-body);\r\n  color: var(--color-primary-charcoal);\r\n  border-radius: 2px;\r\n  border: 1px solid var(--color-bg-primary-pale-charcoal);\r\n  background-color: var(--color-primary-white);\r\n  padding: 0.5rem 1rem;\r\n  height: 2.5rem;\r\n  transition: border-color 0.2s ease-in-out, background-color 0.2s ease-in-out;\r\n}\r\n\r\n.container [data-part=\"input\"]::placeholder {\r\n  color: var(--color-primary-inactive-charcoal);\r\n}\r\n\r\n.container [data-part=\"input\"]:hover {\r\n  background-color: var(--color-primary-soft-charcoal);\r\n}\r\n\r\n.container [data-part=\"input\"]:focus,\r\n.container [data-part=\"input\"][data-state=\"open\"] {\r\n  border-color: var(--color-primary-charcoal);\r\n  outline: none;\r\n}\r\n\r\n.container [data-part=\"input\"]:focus-visible:global(.focusedByKeyboard) {\r\n  outline: 2px solid var(--color-secondary-cobalt);\r\n  outline-offset: 2px;\r\n}\r\n\r\n.container [data-part=\"control\"] {\r\n  position: relative;\r\n}\r\n\r\n.container .selectedAvatar {\r\n  font-weight: bold;\r\n  position: absolute;\r\n  left: 1rem;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  pointer-events: none;\r\n}\r\n\r\n.container .selectedAvatar + [data-part=\"input\"] {\r\n  padding-left: 3rem;\r\n}\r\n\r\n.positioner {\r\n  /* Override Ark UI z-index on element */\r\n  z-index: var(--z-index-dropdown-menu) !important;\r\n}\r\n\r\n.content {\r\n  background-color: var(--color-bg-white);\r\n  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);\r\n  max-height: 12rem;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  border-radius: 2px;\r\n}\r\n\r\n.content [data-part=\"item\"] {\r\n  padding: 0.75rem 1rem;\r\n  font-size: 0.875rem;\r\n  color: var(--color-primary-charcoal);\r\n  font-weight: 600;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.content [data-highlighted] {\r\n  background-color: var(--color-bg-light-grey);\r\n  cursor: pointer;\r\n}\r\n\r\n.noResults {\r\n  padding: 0.75rem 1rem;\r\n  color: var(--color-primary-charcoal);\r\n  text-align: center;\r\n}\r\n\r\n.visuallyHidden {\r\n  clip: rect(0 0 0 0);\r\n  clip-path: inset(50%);\r\n  height: 1px;\r\n  overflow: hidden;\r\n  position: absolute;\r\n  white-space: nowrap;\r\n  width: 1px;\r\n}\r\n", ".toolbar {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  padding: 0.75rem;\r\n  background-color: var(--color-bg-white);\r\n  border-bottom: 1px solid var(--color-border-grey);\r\n}\r\n\r\n.button {\r\n  font-family: var(--primary-font-family);\r\n  background-color: transparent;\r\n  border: 1px solid var(--color-border-grey);\r\n  border-radius: 4px;\r\n  padding: 0.5rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.button:hover {\r\n  background-color: var(--color-bg-primary-pale-charcoal);\r\n  border-color: var(--color-border-primary-charcoal);\r\n}\r\n\r\n.button.active {\r\n  background-color: var(--color-bg-primary-pale-charcoal);\r\n  border-color: var(--color-border-primary-charcoal);\r\n}\r\n", ":root {\r\n  --footer-actions-height: 2.5rem;\r\n}\r\n\r\n.wrapper {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.editorContainer {\r\n  position: relative;\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n  border-radius: 4px;\r\n  padding: 0.5rem;\r\n  width: 100%;\r\n  background-color: var(--color-bg-white);\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-soft-charcoal);\r\n  }\r\n}\r\n\r\n.editorContainer--hasContent {\r\n  border: 1px solid var(--color-primary-charcoal);\r\n  background-color: var(--color-bg-white);\r\n}\r\n\r\n.editorContainer:focus-within {\r\n  border: 1px solid var(--color-primary-charcoal);\r\n\r\n  &:global(.focusedByKeyboard) {\r\n    outline: 2px solid var(--color-secondary-cobalt);\r\n  }\r\n}\r\n\r\n.editorContainer--error,\r\n.editorContainer--error:focus-within {\r\n  border: 1px solid var(--color-secondary-burgundy);\r\n}\r\n\r\n.contentEditable {\r\n  font-size: var(--fontsize-body);\r\n  outline: none;\r\n  min-height: 1.5rem;\r\n  max-height: calc(20rem - var(--footer-actions-height));\r\n  overflow: auto;\r\n}\r\n\r\n.contentEditable.smallText {\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.placeholder {\r\n  position: absolute;\r\n  top: 0.5rem;\r\n  left: 0.5rem;\r\n  pointer-events: none;\r\n  color: var(--color-primary-inactive-charcoal);\r\n  line-height: var(--lineheight-body);\r\n  font-size: var(--fontsize-body-small);\r\n}\r\n\r\n.htmlEditor {\r\n  width: 100%;\r\n  height: 200px;\r\n  font-family: monospace;\r\n  font-size: var(--fontsize-body);\r\n  line-height: 1.5;\r\n  border: 1px solid var(--color-border-pale-grey);\r\n  border-radius: 4px;\r\n  padding: 0.75rem;\r\n  box-sizing: border-box;\r\n  resize: none;\r\n  outline: none;\r\n  background-color: var(--color-bg-white);\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.htmlEditor:focus {\r\n  outline: none;\r\n\r\n  &:global(.focusedByKeyboard) {\r\n    border-color: var(--color-secondary-cobalt);\r\n    box-shadow: 0 0 3px rgba(0, 119, 204, 0.8);\r\n  }\r\n}\r\n\r\n.footerActions {\r\n  display: flex;\r\n  justify-content: end;\r\n  gap: 1rem;\r\n  height: var(--footer-actions-height);\r\n}\r\n\r\n.characterCount {\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body-small);\r\n  color: var(--color-primary-inactive-charcoal);\r\n  margin-top: 0.5rem;\r\n}\r\n\r\n.characterCountError {\r\n  color: var(--color-secondary-burgundy);\r\n}\r\n", ".editor-placeholder {\r\n  color: #999;\r\n  font-style: italic;\r\n  pointer-events: none;\r\n}\r\n\r\n.editor-paragraph {\r\n  margin: 0;\r\n  color: var(--color-primary-light-charcoal);\r\n  line-height: var(--lineheight-body);\r\n  font-size: inherit;\r\n}\r\n\r\n.editor-list-ul {\r\n  list-style-type: disc;\r\n  margin-left: 20px;\r\n}\r\n\r\n.editor-listitem {\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.editor-text-bold {\r\n  font-weight: bold;\r\n}\r\n\r\n.editor-text-italic {\r\n  font-style: italic;\r\n}\r\n\r\n.editor-text-underline {\r\n  text-decoration: underline;\r\n}\r\n\r\n.mention-at {\r\n  background-color: var(--color-bg-primary-pale-charcoal);\r\n  color: var(--color-bg-primary-charcoal);\r\n  padding: 2px 4px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.mention-at-focused {\r\n  background-color: var(--color-bg-primary-pale-charcoal);\r\n  color: var(--color-bg-primary-charcoal);\r\n}\r\n\r\n.mention-hash {\r\n  /* Placeholder Color */\r\n  background-color: #e81a3b3b;\r\n  color: var(--color-primary-red);\r\n  padding: 2px 4px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.mention-hash-focused {\r\n  /* Placeholder Color */\r\n  background-color: #e81a3b3b;\r\n  color: var(--color-primary-red);\r\n}\r\n", ".mentionsMenu {\r\n  margin: 0;\r\n  padding: 8px;\r\n  background: var(--color-primary-white);\r\n  border: 1px solid var(--color-bg-primary-pale-charcoal);\r\n  border-radius: 8px;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);\r\n  width: 300px;\r\n  list-style: none;\r\n}\r\n\r\n.mentionsLoading {\r\n  padding: 8px;\r\n  color: var(--color-bg-primary-pale-charcoal);\r\n  font-style: italic;\r\n  text-align: center;\r\n}\r\n\r\n.mentionsItem {\r\n  padding: 8px 12px;\r\n  font-size: var(--fontsize-body-xsmall);\r\n  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;\r\n}\r\n\r\n.mentionsItem:hover {\r\n  background-color: var(--color-bg-primary-pale-charcoal);\r\n  color: black;\r\n}\r\n\r\n.mentionsItemSelected {\r\n  background-color: var(--color-secondary-cobalt);\r\n  color: var(--color-primary-white);\r\n}\r\n", ".comments {\r\n  display: flex;\r\n  flex-direction: column;\r\n  max-width: 100%;\r\n  background-color: var(--color-bg-white);\r\n}\r\n\r\n.header {\r\n  padding: 0.75rem 1.5rem;\r\n  border-bottom: 1px solid var(--color-primary-pale-charcoal);\r\n  border-top: 1px solid var(--color-primary-pale-charcoal);\r\n  outline: none;\r\n}\r\n\r\n.commentsContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1.5rem;\r\n  padding: 1.5rem 0;\r\n}\r\n\r\n.comment {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 1rem;\r\n  padding: 0 2rem;\r\n}\r\n\r\n.commentHeader {\r\n  display: flex;\r\n  justify-content: start;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.buttonsContainer {\r\n  margin-left: auto;\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.newComment {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 1rem;\r\n  padding: 1.5rem 2rem;\r\n}\r\n\r\n.newCommentAvatar {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.newCommentEditor {\r\n  flex-grow: 1;\r\n  min-width: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.commentData {\r\n  margin-top: 0;\r\n  width: 100%;\r\n}\r\n\r\n.commentContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.author {\r\n  color: var(--color-primary-light-charcoal);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n  margin: 0;\r\n}\r\n\r\n.timestamp {\r\n  color: var(--color-bg-inactive-charcoal);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 400;\r\n  margin: 0;\r\n}\r\n\r\n.title {\r\n  color: var(--color-primary-charcoal);\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  font-weight: 600;\r\n}\r\n\r\n.commentText {\r\n  color: var(--color-primary-light-charcoal);\r\n  font-size: var(--fontsize-body-small);\r\n  line-height: var(--lineheight-body);\r\n  white-space: preserve-breaks;\r\n}\r\n\r\n.noCommentsContainer {\r\n  padding: 1rem;\r\n}\r\n\r\n.noCommentsSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  padding: 1.5rem;\r\n  border-radius: 2px;\r\n  background-color: var(--color-primary-soft-charcoal);\r\n\r\n  svg {\r\n    color: var(--color-bg-primary);\r\n  }\r\n}\r\n\r\n.noCommentsText {\r\n  font-size: var(--fontsize-body);\r\n  line-height: var(--lineheight-body);\r\n  color: var(--color-primary-inactive-charcoal);\r\n  font-weight: 600;\r\n}\r\n\r\n.spinner {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 1.5rem 2rem;\r\n}\r\n\r\n.addCommentButton {\r\n  button {\r\n    &:hover {\r\n      background-color: var(--color-primary-slate);\r\n    }\r\n  }\r\n}\r\n", ".container {\r\n  background-color: var(--color-bg-white);\r\n  padding: 1rem;\r\n  border-radius: 0.25rem;\r\n  border: 1px solid var(--color-primary-pale-charcoal);\r\n}\r\n\r\n.header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  justify-content: space-between;\r\n}\r\n\r\n.editContainer {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  align-items: center;\r\n  color: var(--color-secondary-cobalt);\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  padding: 0;\r\n\r\n  &:hover {\r\n    text-decoration: underline;\r\n  }\r\n}\r\n\r\n.legend {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  padding: 0;\r\n}\r\n\r\n.legendTitle {\r\n  color: var(--color-secondary-cobalt);\r\n  font-weight: 600;\r\n  text-align: end;\r\n}\r\n\r\n.legend p {\r\n  font-size: 1rem;\r\n  margin-top: 0;\r\n}\r\n\r\n.timeline {\r\n  padding-top: 1.5rem;\r\n  padding-left: 0.5rem;\r\n  padding-right: 1rem;\r\n}\r\n\r\n.stageWrapper {\r\n  width: 100%;\r\n}\r\n\r\n.stage {\r\n  display: flex;\r\n}\r\n\r\n.subStageTrigger {\r\n  text-align: left;\r\n  width: 100%;\r\n  padding: 0.35rem 0.5rem;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  transition: background-color 0.2s;\r\n\r\n  &:hover {\r\n    background-color: var(--color-primary-soft-charcoal);\r\n  }\r\n}\r\n\r\n.title {\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  color: var(--color-primary-charcoal);\r\n  margin: 0;\r\n  line-height: var(--lineheight-body);\r\n}\r\n\r\n.info {\r\n  display: flex;\r\n  gap: 1rem;\r\n  width: 100%;\r\n}\r\n\r\n.stageTitle {\r\n  font-weight: 600;\r\n  color: var(--color-primary-charcoal);\r\n}\r\n\r\n.stageSubtitle {\r\n  margin: 0;\r\n  color: var(--color-primary-inactive-charcoal);\r\n  font-weight: 400;\r\n}\r\n\r\n.subStageSubtitle {\r\n  margin-top: 0;\r\n  margin-bottom: 0.5rem;\r\n  color: var(--color-primary-inactive-charcoal);\r\n  font-weight: 400;\r\n}\r\n\r\n.icon,\r\n.sub-stage-icon {\r\n  position: relative;\r\n  padding-top: 0.75rem;\r\n}\r\n\r\n.icon div[aria-label=\"Icon\"],\r\n.sub-stage-icon div[aria-label=\"Icon\"] {\r\n  padding: 0.25rem 0;\r\n  background-color: white;\r\n}\r\n\r\n.accordionTrigger {\r\n  align-self: baseline;\r\n  margin-top: 1rem;\r\n  padding: 0;\r\n}\r\n\r\n.subStageIcon {\r\n  padding-top: 1rem;\r\n}\r\n\r\n.finalStage {\r\n  position: relative;\r\n  margin-top: 0.75rem;\r\n}\r\n\r\n.finalStage div[aria-label=\"Icon\"] {\r\n  padding: 0.25rem 0;\r\n  background-color: white;\r\n}\r\n\r\n.icon::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  width: 2px;\r\n  height: 100%;\r\n  background-color: var(--color-bg-grey);\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n}\r\n\r\n.lastUpdated {\r\n  margin-top: 0.5rem;\r\n}\r\n.tooltipWrapper {\r\n  position: relative;\r\n  display: flex;\r\n}\r\n\r\n.tooltipTrigger {\r\n  display: inline-block;\r\n}\r\n\r\n.tooltipCard {\r\n  position: absolute;\r\n  z-index: 300;\r\n  margin: 0;\r\n  background-color: white;\r\n  width: 20rem;\r\n  right: -4rem;\r\n  top: -0.5rem;\r\n  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);\r\n}\r\n\r\n.textBlock {\r\n  margin-left: 0.5rem;\r\n}\r\n\r\n.timelineStatus {\r\n  margin: 1rem 1rem 0.5rem 1rem;\r\n  font-weight: 600;\r\n  cursor: default;\r\n}\r\n\r\n.statusContainer {\r\n  display: flex;\r\n  padding: 0.75rem 1rem 0.75rem;\r\n}\r\n\r\n.statusContainer:last-child {\r\n  padding-bottom: 1.25rem;\r\n}\r\n\r\n.statusLabel {\r\n  font-weight: 400;\r\n  font-size: 16px;\r\n  line-height: 24px;\r\n  letter-spacing: 0%;\r\n  vertical-align: middle;\r\n  color: #333333;\r\n}\r\n\r\n.statusDescription {\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  line-height: 20px;\r\n  letter-spacing: -0.1px;\r\n  vertical-align: middle;\r\n  color: #666666;\r\n}\r\n\r\n.statusIcon {\r\n  padding-top: 0.25rem;\r\n  padding-right: 0.75rem;\r\n}\r\n"]}