import {
  //CardSelector,
  CustomDatePicker,
  DocumentUpload,
  Icon,
  LocationItemChip,
  SearchDropdown,
  SelectedUser,
  Spinner,
  TextArea,
  TextButton,
  TextButtonSizeEnum,
  Toggle,
  DocumentUploadFile,
  InlineMessage,
  Checkbox,
  useFocusManager,
} from "@bcp/uikit";
import {
  ActionItemFormProps,
  ActionItemFormType,
  acceptedFileTypes,
  MAX_FILE_SIZE,
  MAX_INSTRUCTIONS_CHARACTER_COUNT,
} from "../../shared/spec";
import { useEffect, useRef, useState } from "react";
import styles from "./createActionItemForm.module.css";
import sharedStyles from "../../shared/shared.module.css";
import { useUserService, IUser } from "~/services/user";
import { useTeamMembers } from "~/services/teamMembers";
import { useToastService } from "~/services/toast";
import { useTranslation } from "react-i18next";
import {
  isActionItemDocumentRequest,
  isActionItemSignatureRequest,
  sortByName,
} from "~/utils/actionItems/actionItemUtils";
import { useDocusign } from "~/services/action-items/docusign/useDocusign";
import { EditDocusignOverlay } from "../editDocusignOverlay/EditDocusignOverlay";
import { TitleInput } from "../../shared/titleInput/titleInput";
import { AssigneeOrder } from "../assigneeOrder/AssigneeOrder";

const MAX_ASSIGNEES_SIGNATURE_REQUEST = 12;

export const CreateActionItemForm: React.FC<ActionItemFormProps> = ({
  isOpen,
  type,
  selectedAssignees,
  isPriority,
  targetFolder,
  duplicateDataTargetFolder,
  fileDestinationFetchError,
  //signerOption,
  instructions,
  files,
  title,
  setTitle,
  formErrors,
  setSelectedAssignees,
  setIsPriority,
  //setSignerOption,
  setInstructions,
  setFiles,
  setDueDate,
  date,
  docUploadKey,
  showFormRequiredFields,
  openAssignFolderModal,
  envelopeId,
  onChangeEnvelopeId,
  enableSigningOrder,
  onChangeEnableSigningOrder,
}) => {
  const { t } = useTranslation("actionItems");
  const { t: tGlobal } = useTranslation("global");
  const { teamMembers } = useTeamMembers();
  const { profile } = useUserService();
  const [suggestedUsers, setSuggestedUsers] = useState<IUser[]>([]);
  const { showToast, hideAllToasts } = useToastService();
  const isSignatureRequest = isActionItemSignatureRequest(type);
  const isDocumentRequest = isActionItemDocumentRequest(type);
  const { createEnvelope, updateSigners } = useDocusign();
  const [isDocusignOpen, setIsDocusignOpen] = useState<boolean>(false);
  const [isDocusignSaved, setIsDocusignSaved] = useState<boolean>(false);
  const createEnvelopeController = useRef<AbortController | null>(null);
  const focusManager = useFocusManager();

  useEffect(() => {
    if (
      teamMembers &&
      !teamMembers.isBusy &&
      teamMembers.data &&
      profile &&
      !profile.isBusy &&
      profile.data
    ) {
      // Filter the current user out of suggested users for assignees of the Action item
      setSuggestedUsers(
        teamMembers.data.filter(
          teamMember => teamMember.id !== profile.data?.id
        )
      );
    }
  }, [teamMembers, profile]);

  const handleUserAdd = (selectedUser: SelectedUser) => {
    const user = teamMembers.data?.find(
      user => user.bgpId === selectedUser.uniqueUserId
    );

    // TODO: Update SearchDropdown component to avoid this `find`
    // This case should not be possible; types need improvement
    if (!user) {
      console.error("Failed to add user: did not find in Team Members");
      return;
    }

    const newAssignee = { user };
    const updatedAssignees = [...selectedAssignees, newAssignee];
    setSelectedAssignees(updatedAssignees);
    setIsDocusignSaved(false);
  };

  const handleUserRemove = (removedUser: SelectedUser) => {
    // This case should not be possible; types need improvement
    if (!removedUser.uniqueUserId) {
      console.error("Failed to remove user: missing id");
      return;
    }

    const updatedAssignees = selectedAssignees.filter(
      assignee => assignee.user.bgpId !== removedUser.uniqueUserId
    );

    setSelectedAssignees(updatedAssignees);
    setIsDocusignSaved(false);
  };

  // Assumes only one file allowed for Signature Requests
  const handleFileAccept = async (files: DocumentUploadFile[]) => {
    const addedFile = files[0];

    // TODO: Update DocumentUpload component to handle isPending state itself
    setFiles(
      files.map(file => ({
        ...file,
        isPending: true,
      }))
    );

    const signers = selectedAssignees.map(assignee => assignee.user.bgpId);

    try {
      createEnvelopeController.current = new AbortController();

      const { envelopeId: createdEnvelopeId } = await createEnvelope(
        addedFile.file,
        signers,
        enableSigningOrder,
        createEnvelopeController.current.signal
      );

      onChangeEnvelopeId(createdEnvelopeId);

      setFiles(
        files.map(file => ({
          ...file,
          isPending: false,
        }))
      );
    } catch (error) {
      // User cancelled upload
      if (error instanceof DOMException && error.name === "AbortError") {
        return;
      }

      setFiles(files.filter(file => file.id !== addedFile.id));
      showToast({ type: "error", persist: false, message: tGlobal("error") });
    }
  };

  // Assumes only one file allowed for Signature Requests
  // Discards the draft envelope id locally
  // In Docusign, draft envelopes are deleted after some time (30 days default)
  // https://support.docusign.com/s/document-item?language=en_US&rsc_301=&bundleId=pik1583277475390&topicId=ggp1715622165552.html
  const handleFileDelete = async (files: DocumentUploadFile[]) => {
    createEnvelopeController.current?.abort();
    setFiles(files);
    onChangeEnvelopeId(null);
    setIsDocusignOpen(false);
    setIsDocusignSaved(false);
  };

  const handleEditDocusign = async () => {
    setIsDocusignOpen(true);
  };

  const handleBeforeEditDocusign = async (envelopeId: string) => {
    const signers = selectedAssignees.map(assignee => assignee.user.bgpId);
    await updateSigners(envelopeId, signers, enableSigningOrder);
  };

  return (
    <div className={sharedStyles.createActionItemForm}>
      <div className={sharedStyles.properties}>
        <div className={sharedStyles.propertyRows}>
          <p className={sharedStyles.requiredFieldsText}>
            <span>{t("required-fields-message")} </span>
            <span className={sharedStyles.requiredFieldsTextAsterisk}>*</span>
          </p>
          <div className={sharedStyles.propertyRow}>
            <div className={sharedStyles.caption}>
              <Icon iconName="typing-icon" color="#5B6E7F" />
              <span>{t("title")} </span>
              <span className={sharedStyles.requiredFieldsTextAsterisk}>*</span>
            </div>
            <div className={sharedStyles.titleWrapper}>
              <TitleInput
                title={title}
                setTitle={setTitle}
                disabled={false}
                errorMessage={formErrors?.title}
                focusOnLoad={true}
                id="create-action-title-drawer"
              />
            </div>
          </div>
          <div className={sharedStyles.propertyRow}>
            <div className={sharedStyles.caption}>
              <Icon iconName="calendar-ltr" color="#5B6E7F" />
              {t("due-date")}
            </div>
            <CustomDatePicker
              ariaLabel={t("due-date")}
              placeholder={t("empty-optional")}
              selectionMode="single"
              onChange={setDueDate}
              enableRange={false}
              closeOnSelect
              shouldReset={!isOpen}
              calendarId="create-action-item-calendar-id"
              value={date}
              placement="bottom-start"
            />
          </div>
          <div className={sharedStyles.propertyRow}>
            <div className={sharedStyles.caption}>
              <Icon iconName="people-icon" color="#5B6E7F" />
              <span>{t("assignees")}</span>
              {type === ActionItemFormType.SignatureRequest && (
                <span className={sharedStyles.requiredFieldsTextAsterisk}>
                  *
                </span>
              )}
            </div>
            <div className={styles.assigneesWrapper}>
              {!enableSigningOrder && (
                <SearchDropdown
                  ariaLabel={t("assignees")}
                  id="action-item-create-assignees-dropdown"
                  suggestedUsers={suggestedUsers
                    .filter(
                      member =>
                        !selectedAssignees.find(selected => {
                          return selected.user.email === member.email;
                        })
                    )
                    .sort(sortByName)
                    .map((member, i) => {
                      return {
                        name: `${member.firstName} ${member.lastName}`,
                        email: (member.email as string) ?? "",
                        avatarUrl: "",
                        uniqueUserId: member.bgpId,
                        id: i,
                        avatarColor: member.avatarColor || "",
                      };
                    })}
                  selectedUsers={selectedAssignees.map(assignee => ({
                    displayText: assignee.user.displayName,
                    name: assignee.user.displayName,
                    email: assignee.user.email,
                    id: assignee.user.bgpId,
                    uniqueUserId: assignee.user.bgpId,
                  }))}
                  onUserAdd={handleUserAdd}
                  onUserRemove={handleUserRemove}
                  defaultLabel={t("unassigned")}
                  showFormRequiredFields={
                    showFormRequiredFields && isSignatureRequest
                  }
                  {...(isSignatureRequest
                    ? {
                        isRequired: true,
                        maxAssignees: MAX_ASSIGNEES_SIGNATURE_REQUEST,
                      }
                    : {})}
                  showEmail={false}
                  allowUnverifiedUsers={false}
                  errorOnBlur={false}
                />
              )}
              {isSignatureRequest && selectedAssignees.length > 1 && (
                <Checkbox
                  id="set-signing-order"
                  label={t("set-signing-order")}
                  value={enableSigningOrder}
                  onChange={onChangeEnableSigningOrder}
                  className={styles.orderCheckbox}
                />
              )}
              {enableSigningOrder && (
                <AssigneeOrder
                  users={suggestedUsers}
                  maxAssignees={MAX_ASSIGNEES_SIGNATURE_REQUEST}
                  value={selectedAssignees}
                  onChange={assignees => {
                    setIsDocusignSaved(false);
                    setSelectedAssignees(assignees);
                  }}
                />
              )}
            </div>
          </div>
          <div className={sharedStyles.propertyRow}>
            <div className={sharedStyles.caption}>
              <Icon iconName="folder-icon" color="#5B6E7F" />
              <span>{t("file-destination")} </span>
              <span className={sharedStyles.requiredFieldsTextAsterisk}>*</span>
            </div>
            {isSignatureRequest &&
              (!targetFolder ? (
                fileDestinationFetchError ? (
                  <div className={sharedStyles.textButtonWrapper}>
                    <TextButton
                      id="create-action-signature-file-trigger"
                      label={t("select-a-folder")}
                      ariaLabel={`${t("file-destination")} ${t(
                        "select-a-folder"
                      )}`}
                      size={TextButtonSizeEnum.medium}
                      onClick={() => openAssignFolderModal()}
                      includeChevron={false}
                    />
                    {showFormRequiredFields && (
                      <div className={sharedStyles.errorContainer}>
                        <div className={sharedStyles.errorTextContainer}>
                          <Icon
                            iconName="error-circle"
                            altText="Error Icon"
                            size={20}
                          />
                          <p className={sharedStyles.errorText}>
                            {tGlobal("required-field")}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Spinner isLoading={true} />
                )
              ) : (
                <LocationItemChip
                  id="create-action-assign-folder-trigger"
                  onClickAction={() => openAssignFolderModal()}
                  text={targetFolder.node?.displayName ?? ""}
                  iconName="folder-icon"
                />
              ))}
            {isDocumentRequest &&
              (!targetFolder ? (
                duplicateDataTargetFolder ? (
                  <Spinner isLoading={true} />
                ) : (
                  <div className={sharedStyles.textButtonWrapper}>
                    <TextButton
                      id="create-action-document-file-trigger"
                      label={t("select-a-folder")}
                      ariaLabel={`${t("file-destination")} ${t(
                        "select-a-folder"
                      )} ${tGlobal("required-field")}`}
                      size={TextButtonSizeEnum.medium}
                      onClick={() => {
                        openAssignFolderModal();
                        focusManager.saveSelector(
                          "create-action-assign-folder-document-trigger"
                        );
                      }}
                      includeChevron={false}
                    />
                    {showFormRequiredFields && (
                      <div className={sharedStyles.errorContainer}>
                        <div className={sharedStyles.errorTextContainer}>
                          <Icon iconName="error-circle" size={20} />
                          <p className={sharedStyles.errorText}>
                            {tGlobal("required-field")}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )
              ) : (
                <LocationItemChip
                  id="create-action-assign-folder-document-trigger"
                  onClickAction={() => openAssignFolderModal()}
                  text={targetFolder.node?.displayName ?? ""}
                  iconName="folder-icon"
                />
              ))}
          </div>
          <div className={sharedStyles.propertyRow}>
            <div className={sharedStyles.caption}>
              <Icon iconName="chevron-double" color="#5B6E7F" />
              {t("priority")}
            </div>
            <div className={sharedStyles.toggleWrapper}>
              <Toggle
                ariaLabel={t("priority")}
                isDefaultChecked={isPriority}
                onCheckedChange={checked => setIsPriority(checked)}
              />
            </div>
          </div>
        </div>
      </div>
      <div className={sharedStyles.files}>
        {/*
        Hide Card Selector for MVP
        {type === ActionItemFormType.SignatureRequest && (
          <CardSelector
            selectors={[
              {
                description:
                  "Upload a document for automatic signing in DocuSign.",
                title: SignerOptionEnum.DocuSign,
              },
              //Hide for MVP
              {
                description:
                  "Upload documents for clients to sign by hand and return.",
                title: SignerOptionEnum.Manual,
              },
            ]}
            activeSelect={signerOption as string}
            onActiveSelectChange={() => {
              if (!setSignerOption) return;
              setSignerOption(
                signerOption === SignerOptionEnum.DocuSign
                  ? SignerOptionEnum.Manual
                  : SignerOptionEnum.DocuSign
              );
            }}
          />
        )} */}
        <div className={sharedStyles.instructionsWrapper}>
          <TextArea
            label={t("instructions")}
            hideLabel={false}
            placeholder={t("instructions-placeholder")}
            value={instructions}
            onChange={e => setInstructions(e)}
            maxLength={MAX_INSTRUCTIONS_CHARACTER_COUNT}
            displayMaxLength
          />
        </div>
        {isSignatureRequest && (
          <DocumentUpload
            key={docUploadKey}
            id="action-item-documents"
            acceptedFileTypes={acceptedFileTypes}
            existingFiles={files}
            shouldReset={!isOpen}
            showDropZone={true}
            showNoDocumentsBanner={false}
            noDocumentsBannerText={t("upload-empty")}
            maxFiles={1}
            maxFileSize={MAX_FILE_SIZE}
            username={
              profile?.data
                ? `${profile.data.firstName} ${profile.data.lastName}`
                : ""
            }
            title={t("attach-document")}
            subtitle={t("attach-document-subtitle")}
            subtext={t("accepted-files")}
            onFileAccept={handleFileAccept}
            onFileDelete={handleFileDelete}
            showToast={showToast}
            hideAllToasts={hideAllToasts}
            required={true}
            showFormRequiredFields={showFormRequiredFields}
          >
            {envelopeId && !isDocusignSaved && (
              <div className={styles.editDocusignMessage}>
                <InlineMessage
                  type="warning"
                  title={t("set-signature-locations")}
                  message={t("set-signature-locations-message")}
                >
                  <TextButton
                    id="edit-in-docusign"
                    label={t("edit-in-docusign")}
                    size={TextButtonSizeEnum.inline}
                    onClick={handleEditDocusign}
                    includeChevron={false}
                    className={styles.editDocusignButton}
                  />
                </InlineMessage>
              </div>
            )}
            {envelopeId && isDocusignSaved && (
              <div className={styles.editDocusignMessage}>
                <InlineMessage
                  type="success"
                  title={t("signature-locations-success")}
                  message=""
                >
                  <TextButton
                    id="edit-in-docusign"
                    label={t("edit-in-docusign")}
                    size={TextButtonSizeEnum.inline}
                    onClick={handleEditDocusign}
                    includeChevron={false}
                    className={styles.editDocusignButton}
                  />
                </InlineMessage>
              </div>
            )}
          </DocumentUpload>
        )}
      </div>

      {envelopeId && isDocusignOpen && (
        <EditDocusignOverlay
          envelopeId={envelopeId}
          onClose={() => setIsDocusignOpen(false)}
          onSave={() => setIsDocusignSaved(true)}
          onBeforeEdit={() => handleBeforeEditDocusign(envelopeId)}
        />
      )}
    </div>
  );
};
