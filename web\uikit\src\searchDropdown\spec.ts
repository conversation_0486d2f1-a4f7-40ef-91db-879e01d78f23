import { ChipType } from "~/chip/spec";
import { defaultProps } from "~/default.spec";

export enum UserStatus {
  Active = "active",
  Inactive = "inactive",
}

export enum Language {
  English = "english",
  French = "french",
}

export enum Department {
  Other = "other",
  Administration = "administration",
}

export enum EmailFrequency {
  None = "none",
  Weekly = "weekly",
}

export enum NotificationType {
  ActionItemAssigned = "action_item_assigned",
  ActionItemStatusUpdated = "action_item_status_updated",
  ActionItemCommentAdded = "action_item_comment_added",
  ProjectInvite = "project_invite",
  HealthStatusUpdate = "health_status_update",
  FinalDeliveryUploaded = "final_delivery_uploaded",
}

export enum BusinessDomain {
  EconomicResiliency = "economic_resiliency",
  AritificialIntelligence = "aritificial_intelligence",
  InvestmentStrategyAndPlanning = "investment_strategy_and_planning",
  SecurityVulnerabilitiesAndBreaches = "security_vulnerabilities_and_breaches",
  DigitalOrItTransformation = "digital_or_it_transformation",
  FraudProtection = "fraud_protection",
  MergersAndAcquisitions = "mergers_and_acquisitions",
  SupplyChains = "supply_chains",
  OfficeThreeSixFive = "office_three_six_five",
  Other = "other",
}

export interface SelectedUser {
  id?: string;
  name?: string;
  value?: string;
  label?: string;
  displayText: string;
  email: string;
  chipType?: ChipType;
  isSuggested?: boolean;
  uniqueUserId?: string;
  avatarColor?: string;
  userGroupName?: string;
}

export interface SuggestedUser {
  name: string;
  email: string;
  avatarUrl?: string;
  displayText?: string;
  uniqueUserId?: string;
  avatarColor?: string;
  userGroupName?: string;
}

export interface SearchDropdownProps extends defaultProps {
  suggestedUsers: SuggestedUser[];
  onUserAdd: (user: SelectedUser) => void;
  onUserRemove: (user: SelectedUser) => void;
  selectedUsers: SelectedUser[];
  readOnlyUserIds?: string[];
  disabled?: boolean;
  defaultLabel?: string;
  useMaxWidth?: boolean;
  isRequired?: boolean;
  showFormRequiredFields?: boolean;
  showName?: boolean;
  showEmail?: boolean;
  allowUnverifiedUsers?: boolean;
  maxAssignees?: number;
  maxResults?: number;
  id?: string;
  className?: string;
  errorOnBlur?: boolean;
}
