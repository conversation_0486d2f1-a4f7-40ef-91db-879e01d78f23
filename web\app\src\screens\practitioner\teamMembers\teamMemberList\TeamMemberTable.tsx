import React, { useState, useEffect, useCallback } from "react";
import styles from "./teamMemberTable.module.css";
import {
  Table,
  Icon,
  CustomAvatar,
  Chip,
  TextButton,
  TextButtonSizeEnum,
  ChipType,
  DropdownInput,
  IconName,
  getInitials,
  getTimezoneConvertedDate,
  getDueDateLabel,
} from "@bcp/uikit";
import { SortKey, SortConfig } from "../spec";
import classNames from "classnames";
import useSendInvite from "~/shell/inviteUsersModal/useSendInvite";
import ViewProfile from "~/screens/home/<USER>/ViewProfile";
import { IUser } from "~/services/user";
import { UserActions } from "../userActions/UserActions";
import {
  TeamMemberListProps,
  BcpTeamMemberRole,
  BgpTeamMemberRole,
} from "./spec";
import useScreenSize from "~/utils/http/hooks/useScreenSize";
import { DropdownInputItem } from "@bcp/uikit";
import { IUpdateRoleRequest, useTeamMembers } from "~/services/teamMembers";
import { useSettingsService } from "~/services/settings";
import { useClientService } from "~/services/client";
import { useTranslation } from "react-i18next";
import { useRoleService } from "~/services/role";
import { useProjectService } from "~/services/project";
import { TitleDropdownItems } from "~/services/user/spec";
import { formatISO } from "date-fns";
import { useToastService } from "~/services/toast";

export const mapToGlobalUserType = (userType: string): string => {
  switch (userType) {
    case BgpTeamMemberRole.BDO_ADMIN_USER:
      return BcpTeamMemberRole.BDO_ADMIN_USER;
    case BgpTeamMemberRole.BDO_PROJECT_ADMIN:
      return BcpTeamMemberRole.BDO_ADMIN_USER;
    case BgpTeamMemberRole.PRACTITIONER:
      return BcpTeamMemberRole.PRACTITIONER;
    default:
      return BcpTeamMemberRole.CLIENT_USER;
  }
};

export const TeamMembersTable: React.FC<TeamMemberListProps> = ({
  dataTestId = "app-team-members-table",
  ariaDescribedBy,
  ariaLabel,
  ariaLabelledBy,
  role,
  users,
  filter,
  sortConfig,
  setSortConfig,
}) => {
  const { t } = useTranslation("teamMembers");
  const { t: tGlobal } = useTranslation("global");
  const { t: tDropdown, i18n } = useTranslation("dropdown");
  const [expandedUser, setExpandedUser] = useState<string | null>(null);
  const [updatedRoleUsers, setUpdatedRoleUsers] = useState<
    {
      id: string;
      selected: DropdownInputItem;
    }[]
  >([]);
  const [sortedList, setSortedList] = useState<IUser[]>([]);
  const [activeUser, setActiveUser] = useState<IUser | null>(null);
  const [isProfileDrawerVisible, setIsProfileDrawerVisible] = useState(false);
  const [invitingUserIds, setInvitingUserIds] = useState<IUser["id"][]>([]);

  const { isMobile } = useScreenSize();
  const { resendInvite } = useSendInvite();

  const { updateTeamMemberRole, fetchTeamMembers } = useTeamMembers();
  const { memberFirmId } = useSettingsService();
  const { activeClient } = useClientService();
  const { isAdmin, isPractitioner, isClient } = useRoleService();
  const { activeProject } = useProjectService();
  const { showToast } = useToastService();

  const BDO_ROLES = [
    BgpTeamMemberRole.BDO_ADMIN_USER,
    BgpTeamMemberRole.BDO_PROJECT_ADMIN,
    BgpTeamMemberRole.PRACTITIONER,
  ];

  const hasBdoRole = (user?: IUser) =>
    BDO_ROLES.some(role => role === user?.userType);

  const isClientAndNotOnboarded = (user: IUser) =>
    user.userType == BgpTeamMemberRole.CLIENT_USER &&
    !user.completedOnboardingAt;

  const translateTitle = (title?: string) => {
    const translationKey = TitleDropdownItems.find(
      ({ value }) => value === title
    )?.translationKey;

    if (translationKey && i18n.exists(`dropdown:${translationKey}-label`)) {
      return tDropdown(`${translationKey}-label`);
    }

    return title;
  };

  const sortUsers = (a: IUser, b: IUser) => {
    const { key, direction } = sortConfig;
    let valueA;
    let valueB;
    if (key == "organization") {
      valueA = hasBdoRole(a) ? "BDO" : activeClient?.name;
      valueB = hasBdoRole(b) ? "BDO" : activeClient?.name;
    } else if (key == "title") {
      valueA = translateTitle(a.title);
      valueB = translateTitle(b.title);
    } else {
      valueA = a[key];
      valueB = b[key];
    }

    const compare = valueA?.localeCompare(valueB ?? "") ?? 0;
    return direction === "asc" ? compare : -compare;
  };

  useEffect(() => {
    const usersNotYetOnboarded = users.filter(user =>
      isClientAndNotOnboarded(user)
    );

    const usersOnboarded = users.filter(
      user =>
        user.completedOnboardingAt ||
        user.userType != BgpTeamMemberRole.CLIENT_USER
    );

    const sortedNotYetOnboarded = [...usersNotYetOnboarded].sort(sortUsers);
    const sortedUsersOnboarded = [...usersOnboarded].sort(sortUsers);

    setSortedList([...sortedNotYetOnboarded, ...sortedUsersOnboarded]);
  }, [filter, sortConfig, users]);

  const toggleExpand = (userId: string) => {
    setExpandedUser(prev => (prev === userId ? null : userId));
  };

  const toggleSort = (key: SortKey) => {
    const newConfig: SortConfig = {
      key,
      direction:
        sortConfig.key === key && sortConfig.direction === "asc"
          ? "desc"
          : "asc",
    };
    setSortConfig(newConfig);
  };

  const handleInvite = async (user: IUser) => {
    if (invitingUserIds.includes(user.id)) {
      return;
    }

    if (!activeClient?.bgpId) {
      console.error("Client BGPId is not populated");
      return;
    }

    const clientId = activeClient.bgpId;
    const projectId = activeProject?.bgpId;

    setInvitingUserIds(prev => [...prev, user.id]);

    const result = await resendInvite({
      userId: user.bgpId ?? "",
      firmId: memberFirmId,
      clientId,
      projectId,
    });
    if (projectId) {
      await fetchTeamMembers(activeProject.bgpId);
    }

    setInvitingUserIds(prev => prev.filter(id => id !== user.id));

    if (result.success) {
      showToast({
        message: t("invite-sent"),
        type: "success",
        persist: false,
      });
    } else {
      showToast({
        message: t("invite-error"),
        type: "error",
        persist: false,
      });
    }
  };

  const formatUserType: (input: string) => string = input =>
    input.replace(/_/g, "-");

  const rolesForClientAdmin: DropdownInputItem[] = [
    {
      label: t("bdo-client-admin"),
      value: BcpTeamMemberRole.BDO_ADMIN_USER,
    },
    { label: t("practitioner"), value: BcpTeamMemberRole.PRACTITIONER },
  ];

  // NOTE - Be very careful when editing this, could remove real users Access
  const handleSelectionChange = useCallback(
    async (user: IUser, selected: DropdownInputItem) => {
      const value = selected.value as string;
      const globalUserType = mapToGlobalUserType(user.userType!);
      if (
        globalUserType !== value &&
        globalUserType !== mapToGlobalUserType(value)
      ) {
        showToast({
          message: t("update-user-access"),
          type: "info",
          persist: false,
        });
        setUpdatedRoleUsers([
          ...updatedRoleUsers,
          { id: user.id, selected: selected },
        ]);

        if (activeClient?.bgpId && activeProject) {
          const request: IUpdateRoleRequest = {
            clientId: activeClient.bgpId,
            memberFirmId: memberFirmId,
            projectId: activeProject.bgpId,
            email: user.email,
            groupName: [globalUserType, value],
            uniqueUserId: user.bgpId,
          };
          await updateTeamMemberRole(request);
          await fetchTeamMembers(activeProject.bgpId);
        }
      }
    },
    [updateTeamMemberRole]
  );

  const columns = [
    {
      key: "displayName" as keyof IUser,
      headerClass: "teamMemberNameHeader",
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("displayName")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("name") }
          )}
        >
          {tGlobal("name")}
          <div className={styles.sortTableIcon}>
            <Icon
              iconName={
                sortConfig.key == "displayName"
                  ? sortConfig.direction == "asc"
                    ? ("filter-up-arrow" as IconName)
                    : ("filter-down-arrow" as IconName)
                  : ("base-filter-arrows" as IconName)
              }
            />
          </div>
        </button>
      ),
      showFilterIcon: false,
      tableCellClass: "teamMemberTableCell",
      render: (user: IUser) => (
        <div className={classNames(styles.userInformation)}>
          {/* NOTE - will eventually have to add check for profile picture */}
          <CustomAvatar
            data-testid="uikit-user-icon"
            aria-label="user-icon"
            initials={getInitials(`${user.displayName}`)}
            avatarSize="small"
            fontSize="x-small"
            type="monogram"
          />
          <div className={styles.userNameSection}>
            <p className={classNames(styles.userName, styles.truncate)}>
              {isClientAndNotOnboarded(user) ? user.email : user.displayName}
            </p>
            {user.pronouns.length > 0 && (
              <p className={styles.userPronouns}>{user.pronouns}</p>
            )}
          </div>
        </div>
      ),
    },
    {
      key: "title" as keyof IUser,
      headerClass:
        isAdmin || isPractitioner ? "titleHeader" : "titleHeaderNoAccessCol",
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("title")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("job-title") }
          )}
        >
          {tGlobal("job-title")}
          <div className={styles.sortTableIcon}>
            <Icon
              iconName={
                sortConfig.key == "title"
                  ? sortConfig.direction == "asc"
                    ? ("filter-up-arrow" as IconName)
                    : ("filter-down-arrow" as IconName)
                  : ("base-filter-arrows" as IconName)
              }
            />
          </div>
        </button>
      ),
      showFilterIcon: false,
      tableCellClass: "teamMemberTableCell",
      render: (user: IUser) => {
        let userTitle;

        const translationKey = TitleDropdownItems.find(
          title => title.value === user.title
        )?.translationKey;

        if (translationKey && i18n.exists(`dropdown:${translationKey}-label`)) {
          userTitle = tDropdown(`${translationKey}-label`);
        }

        return <p className={styles.userTitle}>{userTitle ?? user.title}</p>;
      },
    },
    {
      key: "department" as keyof IUser,
      headerClass:
        isAdmin || isPractitioner ? "titleHeader" : "titleHeaderNoAccessCol",
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("organization")}
          aria-label={tGlobal(
            sortConfig.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: t("organization") }
          )}
        >
          {tGlobal("organization")}
          <div className={styles.sortTableIcon}>
            <Icon
              iconName={
                sortConfig.key == "organization"
                  ? sortConfig.direction == "asc"
                    ? ("filter-up-arrow" as IconName)
                    : ("filter-down-arrow" as IconName)
                  : ("base-filter-arrows" as IconName)
              }
            />
          </div>
        </button>
      ),
      showFilterIcon: false,
      tableCellClass: "teamMemberTableCell",
      render: (user: IUser) => {
        const userOrganization = hasBdoRole(user) ? "BDO" : activeClient?.name;

        return <p className={styles.userOrganization}> {userOrganization}</p>;
      },
    },
    {
      key: "userType" as keyof IUser,
      headerClass: "titleHeader",
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => toggleSort("userType")}
        >
          {tGlobal("access-level")}
          <div className={styles.sortTableIcon}>
            <Icon
              iconName={
                sortConfig.key == "userType"
                  ? sortConfig.direction == "asc"
                    ? ("filter-up-arrow" as IconName)
                    : ("filter-down-arrow" as IconName)
                  : ("base-filter-arrows" as IconName)
              }
              altText="Sort"
            />
          </div>
        </button>
      ),
      showFilterIcon: false,
      tableCellClass: "teamMemberTableCell",
      render: (user: IUser) => {
        const defaultUserType: DropdownInputItem = {
          label: user.userType ? t(formatUserType(user.userType)) : "",
          value: user.userType ? user.userType : "",
        };
        const updatedUser = updatedRoleUsers.find(
          updatedUser => updatedUser.id == user.id
        );

        return isAdmin && hasBdoRole(user) ? (
          <DropdownInput
            data-testid="uikit-user-type-dropdown"
            ariaLabel="Access level dropdown"
            items={rolesForClientAdmin}
            onSelectionChange={selection => {
              handleSelectionChange(user, selection);
            }}
            defaultSelection={
              updatedUser ? updatedUser.selected : defaultUserType
            }
            error={false}
            floatingLabelEnabled={false}
            disabled={!!updatedUser}
            updatingSpinner={!!updatedUser}
            thin={true}
            enableSearch={false}
            id={`${user.id}-role-dropdown`}
          />
        ) : (
          <div className={styles.actionsProfileContainer}>
            <p className={styles.userType}>
              {user.userType ? t(formatUserType(user.userType)) : <>&ndash;</>}
            </p>
          </div>
        );
      },
    },
    {
      key: "actionsProfile" as keyof IUser,
      headerClass: "resendHeader",
      header: <span className="sr-only">{tGlobal("actions")}</span>,
      showFilterIcon: false,
      tableCellClass: "teamMemberTableCell",
      render: (user: IUser) => {
        const dateInvited = getTimezoneConvertedDate(
          user.invitedAt ?? user.createdAt
        );

        return isClientAndNotOnboarded(user) ? (
          <div className={styles.resendContainer}>
            <div className={styles.resendButton}>
              <TextButton
                data-testId="uikit-send-invite-button"
                aria-label={t("send-invites")}
                label={t("resend-invite")}
                onClick={() => {
                  if (user) {
                    handleInvite(user);
                  }
                }}
                size={TextButtonSizeEnum.medium}
                includeChevron={false}
                id={"resend-invite-button"}
                disabled={invitingUserIds.includes(user.id)}
              />
            </div>
            {user.userType == BgpTeamMemberRole.CLIENT_USER && (
              <div className={styles.invitedText}>
                {getDueDateLabel(formatISO(dateInvited), i18n.language)}
              </div>
            )}
            <Chip
              text={tGlobal("pending")}
              type={ChipType.STATUS}
              textOnly={true}
            />
            <div className={styles.userActionContainer}>
              {user && (
                <>
                  <UserActions
                    dropdownId={user.id}
                    setIsDrawerVisible={setIsProfileDrawerVisible}
                    setActiveUser={setActiveUser}
                    user={user}
                    userRole={
                      mapToGlobalUserType(user.userType!) as BcpTeamMemberRole
                    }
                    isResendInviteDisabled={invitingUserIds.includes(user.id)}
                    onResendInvite={() => {
                      setInvitingUserIds(prev => [...prev, user.id]);
                    }}
                  />
                </>
              )}
            </div>
          </div>
        ) : (
          user && (
            <div className={styles.justUserActions}>
              <UserActions
                dropdownId={user.id}
                setIsDrawerVisible={setIsProfileDrawerVisible}
                setActiveUser={setActiveUser}
                user={user}
                userRole={
                  mapToGlobalUserType(user.userType!) as BcpTeamMemberRole
                }
              />
            </div>
          )
        );
      },
    },
  ];

  if (isMobile) {
    return (
      <div className={styles.listContainer}>
        {sortedList?.map((user, index) => {
          const isExpanded = expandedUser === user.bgpId;
          return (
            <div key={index}>
              <div>
                <div
                  className={classNames(styles.listItem, styles.row, {
                    [styles.expanded]: isExpanded,
                  })}
                  key={user.id}
                  onClick={() => {
                    setExpandedUser(isExpanded ? null : (user.bgpId as string));
                  }}
                >
                  <div
                    className={styles.expandIconContainer}
                    onClick={() => toggleExpand(user.id || "")}
                  >
                    <Icon
                      iconName={isExpanded ? "chevron-down" : "chevron-right"}
                      altText="chevron-down"
                    />
                  </div>

                  <div
                    className={classNames(styles.userInformation, {
                      [styles.expanded]: isExpanded,
                    })}
                  >
                    <CustomAvatar
                      initials={getInitials(`${user.displayName}`)}
                      avatarSize="small"
                      type="monogram"
                    />
                    <div className={styles.userNameSection}>
                      <p className={styles.userName}>{`${user.displayName}`}</p>
                      {user.pronouns.length > 0 && (
                        <p className={styles.userPronouns}>
                          {user.pronouns}
                          {`${""}`}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className={styles.viewProfile}>
                    <UserActions
                      dropdownId={user.id}
                      setIsDrawerVisible={setIsProfileDrawerVisible}
                      setActiveUser={setActiveUser}
                      user={user}
                      userRole={
                        mapToGlobalUserType(user.userType!) as BcpTeamMemberRole
                      }
                    />
                  </div>
                </div>
              </div>

              <div
                className={classNames(styles.expandedDetails, {
                  [styles.expanded]: isExpanded,
                })}
              >
                <div className={styles.detailRow}>
                  <div className={styles.entryName}>{tGlobal("job-title")}</div>
                  <div className={styles.entryValue}>
                    {user.title && user.title.length > 0 ? user.title : "N/A"}
                  </div>
                </div>
                {/**TODO: Backend integration */}
                <div className={styles.detailRow}>
                  <div className={styles.entryName}>
                    {tGlobal("organization")}
                  </div>
                  <div className={styles.entryValue}>{"N/A"}</div>
                </div>
                {/**TODO: Backend integration */}
                <div className={styles.detailRow}>
                  <div className={styles.entryName}>
                    {tGlobal("access-level")}
                  </div>
                  <div className={styles.entryValue}>{""}</div>
                </div>

                <div className={styles.viewProfileMobileWrapper}>
                  <TextButton
                    data-testid="uikit-view-profile-button"
                    aria-label="view-profile-button"
                    size={TextButtonSizeEnum.small}
                    label="View full profile"
                    id={`view-profile-mobile${index}`}
                    onClick={() => {
                      setActiveUser(user);
                      setIsProfileDrawerVisible(true);
                    }}
                  />
                </div>
              </div>
            </div>
          );
        })}

        {activeUser && (
          <ViewProfile
            user={activeUser}
            isDrawerVisible={isProfileDrawerVisible}
            setIsDrawerVisible={setIsProfileDrawerVisible}
            setActiveUser={setActiveUser}
            onProfileSave={() => {
              showToast({
                message: t("save-profile-toast-message"),
                type: "success",
                persist: false,
              });
            }}
          />
        )}
      </div>
    );
  }

  return (
    <div className={styles.tableContainer}>
      <Table
        dataTestId={dataTestId}
        aria-describedby={ariaDescribedBy}
        aria-label={ariaLabel}
        aria-labelledby={ariaLabelledBy}
        role={role}
        columns={
          isClient ? [...columns.slice(0, 3), ...columns.slice(4)] : columns
        }
        data={sortedList}
        className={"dropdown-menu-container"}
        emptyMessageIcon={"error-icon-red"}
        emptyMessage={t("error-loading")}
        emptySubMessage={t("error-loading-sub")}
      />
      {activeUser && (
        <ViewProfile
          user={activeUser}
          isDrawerVisible={isProfileDrawerVisible}
          setIsDrawerVisible={setIsProfileDrawerVisible}
          setActiveUser={setActiveUser}
          onProfileSave={() => {
            showToast({
              message: t("save-profile-toast-message"),
              type: "success",
              persist: false,
            });
          }}
        />
      )}
    </div>
  );
};
