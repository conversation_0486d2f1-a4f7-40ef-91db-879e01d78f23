import { CheckboxSizeEnum } from "~/checkbox/spec";
import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
export interface CheckboxCardProps extends defaultProps {
    id: string;
    iconName: IconName;
    defaultChecked?: boolean;
    label: string;
    iconSize?: number;
    disabled?: boolean;
    checked: boolean;
    onChange: () => void;
    size?: CheckboxSizeEnum;
}
