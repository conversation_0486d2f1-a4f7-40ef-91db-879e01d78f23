import {
  ButtonD<PERSON>down,
  ButtonSizeEnum,
  ButtonTypeEnum,
  CardSelector,
  Chip,
  ChipDropdownItem,
  ChipType,
  FooterButtonAlignment,
  Icon,
  Input,
  isReadonlyPath,
  Modal,
  ModalSize,
  SearchDropdown,
  SearchDropdownProps,
  SelectedUser,
  Spinner,
  validateFileForUpload,
} from "@bcp/uikit";
import styles from "./document.module.css";
import DocumentsTable from "./documentsTable/DocumentsTable";
import { useEffect, useRef, useState } from "react";
import { IPerson, useDocumentService } from "~/services/document";
import {
  canCreateSubfolder,
  getLocalizedFolderKeyName,
  getPathFromUrl,
  getSelectedFilesOrFolders,
  hasRestricted,
  isBulkSelectionValid,
  isFolderNameValid,
  mapApiToDocumentRows,
  nonEmpty,
} from "./utils";
import { DocumentRow, documentTemplate, SortDirection, SortKey } from "./spec";
import { useOutletContext, useSearchParams } from "react-router-dom";
import { useProjectService } from "~/services/project";
import { useTranslation } from "react-i18next";
import { useClientService } from "~/services/client";
import { useSettingsService } from "~/services/settings";
import { useRoleService } from "~/services/role";
import { useUserService } from "~/services/user/useUserService";
import { useToastService } from "~/services/toast";
import { DocumentFilters } from "~/screens/practitioner/documents/documentFilters/DocumentFilters";
import { BulkBanner } from "./bulk-download/BulkBanner";
import { matchesUser } from "~/utils/actionItems/actionItemUtils";
import { UrlUtils } from "~/utils/generic/urlUtils";
import { ErrorMessage } from "~/services/error/spec";

export interface DocumentsOutletContext {
  reloadDocuments: () => void;
  isSearchActive: boolean;
}

const isEqual = (a: SelectedUser[], b: SelectedUser[]) =>
  a.length === b.length &&
  a.every(userA => b.some(userB => userA.uniqueUserId === userB.uniqueUserId));

interface BreadcrumbItem {
  name: string;
  displayName: string;
  path: string;
  isReadonly: boolean;
}

export const Documents: React.FC = () => {
  const { t } = useTranslation("documents");
  const { t: tGlobal } = useTranslation("global");
  const restrictedRadioTitle = t("restrict-access");
  const [createFolderVisible, setCreateFolderVisible] = useState<boolean>(
    false
  );
  const [updatePermissionsVisible, setUpdatePermissionsVisible] = useState<
    boolean
  >(false);
  const [loadingPermissionData, setLoadingPermissionData] = useState<boolean>(
    false
  );
  const [updatePermissionItem, setUpdatePermissionItem] = useState<
    DocumentRow
  >();
  const [originalRestrictedUsers, setOriginalRestrictedUsers] = useState<
    SelectedUser[]
  >([]);
  const [selectedUsers, setSelectedUsers] = useState<SelectedUser[]>([]);
  const [restrictSelected, setRestrictSelected] = useState<boolean>(false);
  const sharedWithEveryoneRadioTitle = t("shared-with-everyone");
  const [activeSelected, setActiveSelected] = useState<string>();
  const [inputFolderName, setInputFolderName] = useState("");
  const [inputFolderNameError, setInputFolderNameError] = useState<
    string | undefined
  >();
  const [selectedFileTypes, setSelectedFileTypes] = useState<
    Set<ChipDropdownItem>
  >(new Set());
  const [selectedFilterUsers, setSelectedFiltersUsers] = useState<
    Set<ChipDropdownItem>
  >(new Set());
  const [selectedDates, setSelectedDates] = useState<Set<Date>>(new Set());
  const {
    nodes,
    uploadDocument,
    createDocumentsFolder,
    getAvailableUsersToAssignAccess,
    updatePermission,
    getPermissions,
    isCurrentFolderRestricted,
    createDownloadFileAndFolders,
    fetchMoveFolderContent,
    currentFolderListItemId,
  } = useDocumentService();
  const { memberFirmId } = useSettingsService();
  const { activeClient } = useClientService();
  const { activeProject, spoSite } = useProjectService();
  const [params, setParams] = useSearchParams();
  const [availableAssignees, setAvailableAssignees] = useState<IPerson[]>([]);
  const [currentWorkingPath, setCurrentWorkingPath] = useState<string>();
  const [modalActionLoading, setModalActionLoading] = useState<boolean>(false);
  const [uploadingFileNames, setUploadingFileNames] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [isDownloadingDocuments, setIsDownloadingDocuments] = useState<boolean>(
    false
  );
  const [
    showRestrictedBulkWarningModal,
    setShowRestrictedBulkWarningModal,
  ] = useState(false);
  const [
    showBulkMoveDocumentToFolderModal,
    setShowBulkMoveDocumentToFolderModal,
  ] = useState(false);
  const [
    showMoveDocumentsToRecycleModal,
    setShowMoveDocumentsToRecycleModal,
  ] = useState(false);

  const [
    showMoveDocumentToFolderModal,
    setShowMoveDocumentToFolderModal,
  ] = useState(false);

  const [showRestrictedWarningModal, setShowRestrictedWarningModal] = useState(
    false
  );

  const [selectedMoveDocument, setSelectedMoveDocument] = useState<DocumentRow>(
    documentTemplate
  );
  const [showMoveDocumentModal, setShowMoveDocumentModal] = useState(false);

  const { isClient } = useRoleService();
  const { profile } = useUserService();
  const { showToast } = useToastService();

  const [sortConfig, setSortConfig] = useState<{
    key: SortKey;
    direction: SortDirection;
  }>({
    key: "modified",
    direction: "desc",
  });

  const selectedFilesAndFolders = getSelectedFilesOrFolders(
    selectedRows,
    nodes?.data ? mapApiToDocumentRows(nodes.data) : []
  );

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUserAdd = (user: SelectedUser) => {
    setSelectedUsers(prev => [...prev, user]);
  };

  const handleUserRemove: SearchDropdownProps["onUserRemove"] = removedUser => {
    setSelectedUsers(prev =>
      prev.filter(user => !matchesUser(user, removedUser))
    );
  };

  const selectedFolders = selectedFilesAndFolders
    .filter(document => document.folderOrFile === "Folder" && document.url)
    .map(document => document.url!);

  const selectedFiles = selectedFilesAndFolders
    .filter(document => document.folderOrFile === "File" && document.url)
    .map(document => document.url!);

  const handleDownload = async () => {
    setIsDownloadingDocuments(true);
    try {
      if (activeClient?.bgpId && activeProject?.bgpId) {
        await createDownloadFileAndFolders(
          memberFirmId,
          activeClient.bgpId,
          activeProject.bgpId,
          [...selectedFiles],
          [...selectedFolders]
        );
      }
      setIsDownloadingDocuments(false);
    } catch (error) {
      console.error("Error downloading files and folders:", error);
      setIsDownloadingDocuments(false);
    }
  };

  const handleOpenBulkMoveDocumentsModal = (
    selectedFilesAndFolders: DocumentRow[]
  ) => {
    const selectedCount = selectedFilesAndFolders.length;
    if (selectedCount === 1) {
      handleSingleDocumentMoveModal(selectedFilesAndFolders[0]);
      return;
    }

    if (hasRestricted(selectedFilesAndFolders) || isCurrentFolderRestricted) {
      setShowRestrictedBulkWarningModal(true);
    } else {
      setShowBulkMoveDocumentToFolderModal(true);
    }
  };

  const handleSingleDocumentMoveModal = (item: DocumentRow) => {
    if (hasRestricted([item]) || isCurrentFolderRestricted) {
      setShowRestrictedWarningModal(true);
    } else {
      setShowMoveDocumentToFolderModal(true);
    }
    setSelectedMoveDocument(item);
  };

  const handleDocumentMoveToRecycleBin = (documents: DocumentRow[]) => {
    if (documents.length === 1) {
      setSelectedMoveDocument(documents[0]);
      setShowMoveDocumentModal(true);
    } else {
      setShowMoveDocumentsToRecycleModal(true);
    }
  };

  const fetchMoveDocumentFolders = async (path: string) => {
    if (activeClient && activeClient.bgpId) {
      await fetchMoveFolderContent(
        memberFirmId,
        activeClient.bgpId,
        path,
        activeProject?.bgpId
      );
    }
  };

  const { reloadDocuments, isSearchActive } = useOutletContext<
    DocumentsOutletContext
  >();

  const handleFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
      fileInputRef.current.click();
    }
  };

  const onAvailableAssigneesResponseHandler = (data: IPerson[]) => {
    setAvailableAssignees(data);
    const currentUser = data.filter(u => u.id === profile?.data?.bgpId)?.[0];
    setSelectedUsers([
      {
        name: currentUser.displayName,
        email: currentUser.email,
        displayText: currentUser.displayName,
        id: currentUser.id,
        uniqueUserId: currentUser.id,
      },
    ]);
  };

  useEffect(() => {
    setActiveSelected(sharedWithEveryoneRadioTitle);
    if (
      activeClient?.bgpId &&
      activeProject?.bgpId &&
      profile?.data &&
      updatePermissionItem?.listItemId &&
      updatePermissionsVisible
    ) {
      setLoadingPermissionData(true);
      let loadingCount = 0;
      getPermissions(
        memberFirmId,
        activeClient.bgpId,
        activeProject.bgpId,
        updatePermissionItem?.listItemId
      )
        .then(data => {
          if (data.users?.length > 0) {
            const users = data.users.map(item => ({
              name: item.displayName,
              email: item.email,
              displayText: item.displayName,
              id: item.id,
              uniqueUserId: item.id,
            }));
            setSelectedUsers(users);
            setOriginalRestrictedUsers(users);
            titleSetter(restrictedRadioTitle);
          } else {
            setSelectedUsers([
              {
                name: profile.data?.displayName,
                email: profile.data?.email + "",
                displayText: profile.data?.displayName + "",
                id: profile.data?.bgpId,
                uniqueUserId: profile.data?.bgpId,
              },
            ]);
            setOriginalRestrictedUsers([]);
          }
        })
        .finally(() => {
          loadingCount++;
          setLoadingPermissionData(!(loadingCount == 2));
        });
      getAvailableUsersToAssignAccess(
        memberFirmId,
        activeClient.bgpId,
        activeProject.bgpId,
        updatePermissionItem?.listItemId
      )
        .then(data => {
          setAvailableAssignees(data);
        })
        .catch(e => {
          console.error(e);
        })
        .finally(() => {
          loadingCount++;
          setLoadingPermissionData(!(loadingCount == 2));
        });
    }
  }, [
    activeClient,
    activeProject,
    profile,
    updatePermissionItem,
    updatePermissionsVisible,
  ]);

  useEffect(() => {
    const path = params.get("path");

    if (path && path !== currentWorkingPath) {
      setCurrentWorkingPath(path);
    }
    if (
      activeClient?.bgpId &&
      activeProject?.bgpId &&
      spoSite &&
      profile?.data &&
      createFolderVisible
    ) {
      const docPath = `/Documents${path === "/" ? "" : path}`;
      const fullPath = UrlUtils.join(spoSite.path, docPath);

      getAvailableUsersToAssignAccess(
        memberFirmId,
        activeClient.bgpId,
        activeProject.bgpId,
        undefined,
        fullPath
      )
        .then(onAvailableAssigneesResponseHandler)
        .catch(e => {
          console.error(e);
        });
    }
  }, [activeClient, activeProject, spoSite, profile, createFolderVisible]);

  useEffect(() => {
    setSelectedRows([]);
    if (nodes && !nodes.isBusy) {
      setSelectedFileTypes(new Set());
      setSelectedFiltersUsers(new Set());
      setSelectedDates(new Set());
    }
  }, [nodes?.isBusy]);

  const openUpdatePermissionModal = (item: DocumentRow) => {
    setUpdatePermissionItem(item);
    setUpdatePermissionsVisible(true);
  };

  const getStructuredFileTypes = (
    documentRows: DocumentRow[] = []
  ): ChipDropdownItem[] => {
    const types = documentRows
      .filter(row => row.folderOrFile.toLowerCase() === "file")
      .map(row => row.fileType?.toLowerCase())
      .filter((type): type is string => Boolean(type));

    const uniqueTypes = Array.from(new Set(types));

    return uniqueTypes.map((type, index) => ({
      id: (index + 1).toString(),
      label: type.toUpperCase(),
      value: type,
    }));
  };

  let structuredFileTypes: ChipDropdownItem[] = [];

  if (nodes?.data) {
    structuredFileTypes = getStructuredFileTypes(
      mapApiToDocumentRows(nodes.data)
    );
  }

  const handleFiles = async (files: File[]) => {
    if (!activeClient?.bgpId) return;
    if (!activeProject) return;
    if (files.length === 0) return;

    const path = getPathFromUrl();
    let successfulUploads = 0;

    const validFiles: File[] = [];

    for (const file of files) {
      const { errors } = validateFileForUpload(file);

      if (errors.length > 0) {
        for (const error of errors) {
          showToast({
            title: t("failed-to-upload", { fileName: file.name }),
            message: error,
            type: "error",
            persist: false,
            errorMessageWithLineBreaks: true,
            replaceLineBreaksWithBulletPoints: true,
          });
        }
      } else {
        validFiles.push(file);
      }
    }

    setUploadingFileNames(validFiles.map(file => file.name));

    const uploadPromises = validFiles.map(async file => {
      try {
        if (activeClient?.bgpId)
          await uploadDocument(
            file,
            activeClient.bgpId,
            activeProject.bgpId,
            path,
            undefined,
            currentFolderListItemId ?? undefined
          );
        successfulUploads++;
      } catch (error) {
        console.error(`Failed to upload file "${file.name}":`, error);

        const fallbackMessage = t("failed-to-upload-unknown-error");
        const errorIsString =
          error &&
          typeof error === "object" &&
          "message" in error &&
          typeof error.message === "string";

        const errorMessage =
          errorIsString && error.message === ErrorMessage.FileAlreadyExists
            ? t("file-already-exists")
            : fallbackMessage;

        setUploadingFileNames([]);

        showToast({
          title: t("failed-to-upload", { fileName: file.name }),
          message: errorMessage,
          type: "error",
          persist: false,
          errorMessageWithLineBreaks: true,
          replaceLineBreaksWithBulletPoints: true,
        });
      }
    });

    await Promise.allSettled(uploadPromises);

    if (successfulUploads > 0) {
      showToast({
        title: t("file-uploaded-multiple", { count: successfulUploads }),
        type: "success",
        persist: false,
      });
      setUploadingFileNames([]);
      reloadDocuments();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    await handleFiles(files);
    e.target.value = "";
  };

  const titleSetter = (title: string) => {
    setActiveSelected(title);
    if (title == restrictedRadioTitle) {
      setRestrictSelected(true);
    } else {
      setRestrictSelected(false);
    }
  };

  const handleFolderRename = (value: string) => {
    setInputFolderName(value);
    if (value === "") {
      setInputFolderNameError(undefined);
    } else if (!isFolderNameValid(value)) {
      setInputFolderNameError(t("error-invalid-folder-name-input"));
    } else {
      setInputFolderNameError(undefined);
    }
  };

  const newFolderNameIsValid = (value: string) =>
    value && isFolderNameValid(value);

  const createFolderAndOrUpdatePermissions = async (
    createNewFolder: boolean
  ) => {
    let failedToUpdatePermissions = false;

    if (createNewFolder && !inputFolderName) {
      setInputFolderNameError(t("folder-name-is-required"));
      return;
    } else if (createNewFolder && !newFolderNameIsValid(inputFolderName)) {
      setInputFolderNameError(t("error-invalid-folder-name-input"));
      return;
    }

    if (activeClient?.bgpId && activeProject?.bgpId) {
      setModalActionLoading(true);
      let itemId: number | undefined;
      if (createNewFolder) {
        let newFolder;
        try {
          newFolder = await createDocumentsFolder(
            memberFirmId,
            activeClient.bgpId,
            activeProject.bgpId,
            getPathFromUrl(),
            inputFolderName
          );
        } catch (e) {
          console.error(e);
        }
        if (!newFolder) {
          showToast({
            message: tGlobal("error"),
            type: "error",
            persist: false,
          });
          setModalActionLoading(false);
          return;
        }
        itemId = newFolder.listItemId;
      } else if (updatePermissionItem) {
        itemId = updatePermissionItem.listItemId;
      }
      if (
        (!createNewFolder || restrictSelected) &&
        itemId &&
        selectedUsers.length > 0
      ) {
        try {
          await updatePermission({
            memberFirmId,
            clientId: activeClient.bgpId,
            projectId: activeProject.bgpId,
            itemId: itemId,
            inherited: !restrictSelected,
            userIds: restrictSelected
              ? selectedUsers.map(u => u.uniqueUserId).filter(nonEmpty)
              : [],
          });
        } catch (e) {
          console.error(e);
          failedToUpdatePermissions = true;
        }
      }
      reloadDocuments();
    }
    setModalActionLoading(false);
    if (!createNewFolder && failedToUpdatePermissions) {
      showToast({
        message: t("error-updating-permissions", {
          folderName: inputFolderName,
        }),
        type: "error",
        persist: false,
      });
    } else if (createNewFolder && failedToUpdatePermissions) {
      showToast({
        message: t("success-creating-folder-error-updating-permissions", {
          folderName: inputFolderName,
        }),
        type: "error",
        persist: false,
      });
    } else if (!createNewFolder) {
      // success
      showToast({
        message: t("success-updating-permissions", {
          itemName: updatePermissionItem?.title,
        }),
        type: "success",
        persist: false,
      });
      hideModal();
      reloadDocuments();
    } else if (createNewFolder) {
      // success
      showToast({
        message: t("success-creating-new-folder", {
          folderName: inputFolderName,
        }),
        type: "success",
        persist: false,
      });
      hideModal();
      reloadDocuments();
    }
  };

  const hideModal = () => {
    setActiveSelected(sharedWithEveryoneRadioTitle);
    setInputFolderName("");
    setRestrictSelected(false);
    setModalActionLoading(false);
    setInputFolderNameError(undefined);
    setCreateFolderVisible(false);
    setUpdatePermissionsVisible(false);
    setSelectedUsers([]);
    setOriginalRestrictedUsers([]);
    setAvailableAssignees([]);
    setLoadingPermissionData(false);
  };

  const uploadFileConfig = {
    id: "upload",
    onClick: handleFile,
    iconName: "arrow-upload",
    label: t("upload-files"),
    value: "upload",
    withIcon: true,
  };

  const createFolderConfig = {
    id: "create",
    onClick: () => setCreateFolderVisible(true),
    iconName: "documents-icon",
    label: t("create-new-folder"),
    value: "create",
    withIcon: true,
  };

  //You can only create folders if less than 2 levels deep in hierarchy
  const buttonDropdownItems = [uploadFileConfig];
  if (canCreateSubfolder(getPathFromUrl())) {
    buttonDropdownItems.push(createFolderConfig);
  }

  const isReadonly = isReadonlyPath(getPathFromUrl());

  const filterChipOptions = {
    showFileType: true,
    showUploadedBy: false,
    showCreatedBy: true,
    showModified: true,
  };

  const generateUniqueUsers = (data: DocumentRow[]): ChipDropdownItem[] => {
    const uniqueUsers = Array.from(
      new Set(data.map(row => row.modifiedByName).filter(Boolean))
    );

    return uniqueUsers.map((user, i) => ({
      id: i.toString(),
      label: user as string,
      value: user as string,
      includeAvatar: false,
    }));
  };

  const usersData = mapApiToDocumentRows(nodes?.data || []);
  const users = generateUniqueUsers(usersData);

  const getFoldersFromPath = (path: string): BreadcrumbItem[] => {
    const rootFolder = {
      path: "/",
      name: t("documents-title"),
      isReadonly: false,
      displayName: t("documents-title"),
    };

    let fullPath = "";

    const folders = path
      .split("/")
      .filter(Boolean)
      .map(name => {
        fullPath += `/${name}`;
        return {
          path: fullPath,
          name,
          isReadonly: isReadonlyPath(fullPath),
          displayName: t(getLocalizedFolderKeyName(name), name),
        };
      });

    return [rootFolder, ...folders];
  };

  const folders = getFoldersFromPath(getPathFromUrl());
  // TODO - remove this in favour of a global refactor where we use 'currentNode', similar to activeClient or activeProject
  const isRootFolder = folders.length <= 1;

  const shouldHideMove = !isBulkSelectionValid(
    selectedFilesAndFolders,
    isClient
  );

  const shouldShowBulkBanner = selectedFilesAndFolders.length > 0;

  return (
    <div className={styles.tableSection}>
      {!shouldShowBulkBanner && (
        <div className={styles.documentsTop}>
          <nav aria-label={t("nav-alt-text")}>
            <ul className={styles.tableTitle}>
              {folders.map((folder, index) => {
                const { path, isReadonly, displayName } = folder;
                const lastItem = index === folders.length - 1;
                return !lastItem ? (
                  <li className={styles.breadcrumbItem} key={`${index}-folder`}>
                    <div
                      onClick={() => {
                        setParams({ path });
                      }}
                      className={styles.nameInPath}
                      style={{ cursor: "pointer" }}
                    >
                      {displayName}
                    </div>
                    <span
                      className={styles.chevronRightBreadcrumb}
                      aria-hidden={true}
                    >
                      <Icon
                        iconName={"right-chevron-breadcrumb"}
                        altText={""}
                      ></Icon>
                    </span>
                  </li>
                ) : (
                  <li className={styles.withChipHeader} key={`${index}-folder`}>
                    <h3
                      className={lastItem ? styles.lastFolderName : ""}
                      aria-current={lastItem ? "page" : undefined}
                    >
                      {displayName}
                    </h3>
                    {isCurrentFolderRestricted && !isRootFolder && (
                      <Icon iconName={"lock-icon"} size={12} />
                    )}
                    {isReadonly && (
                      <Chip
                        text={t("read-only")}
                        iconName={"read-only"}
                        type={ChipType.READONLY}
                        iconHeight={8}
                        iconWidth={11.99}
                      />
                    )}
                  </li>
                );
              })}
            </ul>
          </nav>

          <div>
            {isClient && !isReadonly && !isSearchActive && (
              <div onClick={() => handleFile()}>
                <ButtonDropdown
                  items={[]}
                  id="client-button-dropdown"
                  {...(isClient && {
                    triggerText: t("upload-file"),
                    triggerIcon: "arrow-upload",
                  })}
                  iconColor="charcoal"
                />
              </div>
            )}
            {!isClient && !isSearchActive && (
              <ButtonDropdown
                id="document-add-btn-dropdown"
                triggerText={t("new-file")}
                triggerIcon="add-icon"
                items={buttonDropdownItems}
                iconColor="charcoal"
              />
            )}

            <input
              type="file"
              ref={fileInputRef}
              style={{ display: "none" }}
              onChange={handleFileChange}
              multiple
            />
          </div>
        </div>
      )}

      <Modal
        id="update-permissions-modal"
        ariaLabel={t("update-permissions-modal-aria-label")}
        dataTestId="uikit-modal-updatePermissions"
        title={t("update-permissions-modal-title", {
          fileType: updatePermissionItem?.folderOrFile,
          itemName: updatePermissionItem?.title,
        })}
        isVisible={updatePermissionsVisible}
        hide={hideModal}
        allowOverflow={false}
        size={ModalSize.MEDIUM}
        truncateTitle={true}
        primaryBtnConfig={{
          label: tGlobal("save"),
          type: ButtonTypeEnum.primary,
          onClick: () => createFolderAndOrUpdatePermissions(false),
          withRightIcon: false,
          size: ButtonSizeEnum.large,
          withLeftIcon: false,
          role: "button",
          dataTestId: "uikit-button-saveUpdatePermissions",
          disabled:
            isEqual(originalRestrictedUsers, selectedUsers) &&
            activeSelected != sharedWithEveryoneRadioTitle,
          loading: modalActionLoading,
        }}
        secondaryBtnConfig={{
          id: "secondary-btn",
          label: tGlobal("cancel"),
          type: ButtonTypeEnum.tertiary,
          onClick: hideModal,
          size: ButtonSizeEnum.large,
          withRightIcon: false,
          withLeftIcon: false,
          role: "button",
          dataTestId: "uikit-button-cancelSavePermissions",
        }}
        includeHeaderBorder={false}
        footerBtnAlignment={FooterButtonAlignment.END}
      >
        <>
          {loadingPermissionData ? (
            <Spinner isLoading={true} size={"large"} />
          ) : (
            <>
              <div className={styles.cardSelector}>
                <CardSelector
                  ariaLabel="Select access type"
                  dataTestId="uikit-cardSelector-accessType"
                  regularRadioInput={true}
                  selectors={[
                    {
                      title: sharedWithEveryoneRadioTitle,
                    },
                    {
                      title: restrictedRadioTitle,
                    },
                  ]}
                  activeSelect={activeSelected ?? sharedWithEveryoneRadioTitle}
                  onActiveSelectChange={titleSetter}
                ></CardSelector>
              </div>
              {restrictSelected && (
                <div className={styles.restrictedSelector}>
                  <SearchDropdown
                    id="documents-update-permissions-users-dropdown"
                    suggestedUsers={availableAssignees.map(item => ({
                      name: item.displayName,
                      email: item.email,
                      uniqueUserId: item.id,
                    }))}
                    onUserAdd={handleUserAdd}
                    onUserRemove={handleUserRemove}
                    selectedUsers={selectedUsers}
                    defaultLabel={"Select users to access"}
                    useMaxWidth={false}
                    dataTestId={"uikit-searchDropdown-allowedUsers"}
                    readOnlyUserIds={[profile?.data?.bgpId + ""]}
                    allowUnverifiedUsers={false}
                  ></SearchDropdown>
                </div>
              )}
            </>
          )}
        </>
      </Modal>

      <Modal
        id="create-new-folder-modal"
        ariaLabel="Create new folder modal"
        dataTestId="uikit-modal-createNewFolder"
        title={t("create-new-folder")}
        isVisible={createFolderVisible}
        hide={hideModal}
        allowOverflow={false}
        size={ModalSize.MEDIUM}
        primaryBtnConfig={{
          label: tGlobal("create"),
          type: ButtonTypeEnum.primary,
          onClick: () => createFolderAndOrUpdatePermissions(true),
          withRightIcon: false,
          size: ButtonSizeEnum.large,
          withLeftIcon: false,
          role: "button",
          ariaLabel: t("create-new-folder"),
          dataTestId: "uikit-button-confirmCreate",
          disabled: !inputFolderName?.trim() || inputFolderNameError,
          loading: modalActionLoading,
        }}
        secondaryBtnConfig={{
          id: "secondary-btn",
          label: tGlobal("cancel"),
          type: ButtonTypeEnum.tertiary,
          onClick: hideModal,
          size: ButtonSizeEnum.large,
          withRightIcon: false,
          withLeftIcon: false,
          role: "button",
          ariaLabel: "Cancel creating new folder",
          dataTestId: "uikit-button-cancelCreate",
        }}
        includeHeaderBorder={false}
        footerBtnAlignment={FooterButtonAlignment.END}
      >
        <>
          <div className={styles.inputFolderName}>
            <Input
              inputId={"createNewFolderInput"}
              placeholder={t("folder-name")}
              onValueChange={v => {
                handleFolderRename(v);
              }}
              value={inputFolderName}
              ariaLabel={inputFolderName}
              dataTestId="uikit-input-folderName"
              error={!!inputFolderNameError}
              errorMessage={inputFolderNameError}
              errorMessageWithLineBreaks={true}
              replaceLineBreaksWithBulletPoints={true}
            ></Input>
          </div>
          <span className={styles.manageAccess}>
            {t("set-folder-permissions")}
          </span>
          <div className={styles.cardSelector}>
            <CardSelector
              ariaLabel="Select access type"
              dataTestId="uikit-cardSelector-accessType"
              regularRadioInput={true}
              selectors={[
                {
                  title: sharedWithEveryoneRadioTitle,
                },
                {
                  title: restrictedRadioTitle,
                },
              ]}
              activeSelect={
                activeSelected ?? t("shared-with-everyone-radio-btn-title")
              }
              onActiveSelectChange={titleSetter}
            ></CardSelector>
          </div>
          {restrictSelected && (
            <div className={styles.restrictedSelector}>
              <SearchDropdown
                id="documents-create-folder-users-dropdown"
                suggestedUsers={availableAssignees.map(item => ({
                  name: item.displayName,
                  email: item.email,
                  uniqueUserId: item.id,
                }))}
                onUserAdd={handleUserAdd}
                onUserRemove={handleUserRemove}
                selectedUsers={selectedUsers}
                defaultLabel={t("select-users-placeholder")}
                useMaxWidth={false}
                dataTestId={"uikit-searchDropdown-allowedUsers"}
                readOnlyUserIds={[profile?.data?.bgpId + ""]}
                allowUnverifiedUsers={false}
              ></SearchDropdown>
            </div>
          )}
        </>
      </Modal>

      {shouldShowBulkBanner && (
        <BulkBanner
          selectedRows={selectedRows}
          setSelectedRows={setSelectedRows}
          shouldHideMoveButton={shouldHideMove}
          onClickDownload={() => {
            handleDownload();
          }}
          onClickMove={async () => {
            await handleOpenBulkMoveDocumentsModal(selectedFilesAndFolders);
            await fetchMoveDocumentFolders("/");
          }}
          onClickMoveToBin={() => {
            handleDocumentMoveToRecycleBin(selectedFilesAndFolders);
          }}
        />
      )}

      <DocumentFilters
        initialFileTypes={new Set()}
        initialUsers={new Set()}
        onFiltersChange={({ fileTypes, users, dates }) => {
          setSelectedFileTypes(new Set(fileTypes));
          setSelectedFiltersUsers(new Set(users));
          setSelectedDates(new Set(dates));
        }}
        fileTypes={structuredFileTypes}
        filters={filterChipOptions}
        users={users}
        isSearchActive={isSearchActive}
        showResetButton={
          selectedFileTypes.size > 0 ||
          selectedFilterUsers.size > 0 ||
          selectedDates.size > 0
        }
      />

      {nodes?.data && (
        <DocumentsTable
          files={mapApiToDocumentRows(nodes?.data)}
          uploadingFiles={uploadingFileNames}
          sortConfig={sortConfig}
          setSortConfig={setSortConfig}
          onManageAccess={openUpdatePermissionModal}
          selectedFileTypes={selectedFileTypes}
          selectedUsers={selectedFilterUsers}
          selectedDates={selectedDates}
          setSelectedRows={setSelectedRows}
          selectedRows={selectedRows}
          isDownloadingDocuments={isDownloadingDocuments}
          showRestrictedBulkWarningModal={showRestrictedBulkWarningModal}
          showBulkMoveDocumentToFolderModal={showBulkMoveDocumentToFolderModal}
          setShowRestrictedBulkWarningModal={setShowRestrictedBulkWarningModal}
          setShowBulkMoveDocumentToFolderModal={
            setShowBulkMoveDocumentToFolderModal
          }
          setShowMoveDocumentsToRecycleModal={
            setShowMoveDocumentsToRecycleModal
          }
          showMoveDocumentsToRecycleModal={showMoveDocumentsToRecycleModal}
          setIsDownloadingDocuments={setIsDownloadingDocuments}
          showMoveDocumentToFolderModal={showMoveDocumentToFolderModal}
          setShowMoveDocumentToFolderModal={setShowMoveDocumentToFolderModal}
          showRestrictedWarningModal={showRestrictedWarningModal}
          setShowRestrictedWarningModal={setShowRestrictedWarningModal}
          selectedMoveDocument={selectedMoveDocument}
          setSelectedMoveDocument={setSelectedMoveDocument}
          handleSingleDocumentMoveModal={handleSingleDocumentMoveModal}
          showMoveDocumentModal={showMoveDocumentModal}
          setShowMoveDocumentModal={setShowMoveDocumentModal}
          handleDocumentMoveToRecycleBin={handleDocumentMoveToRecycleBin}
          handleFileUpload={handleFiles} // Pass the File[] handler directly
        />
      )}
    </div>
  );
};
