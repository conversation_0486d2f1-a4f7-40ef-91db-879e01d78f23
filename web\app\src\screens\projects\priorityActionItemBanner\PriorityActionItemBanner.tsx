import React, { useEffect, useRef, useState } from "react";
import {
  PriorityActionItemBannerProps,
  PriorityActionItemBannerType,
} from "./spec";
import classNames from "classnames";
import assistantPicture from "~/assets/images/assistant-placeholder.png";

import styles from "./priorityActionItemBanner.module.css";
import {
  ActionItemStatusType,
  Button,
  ButtonSizeEnum,
  ButtonTypeEnum,
  Chip,
  ChipType,
  CustomAvatar,
  DocumentUploadFile,
  Icon,
  Spinner,
  StatusBadge,
  StatusBadgeType,
  getDateTimeLabel,
  validateFileForUpload,
  getDueDateLabel,
  isPastDue,
  TaskType,
} from "@bcp/uikit";
import { formatISO } from "date-fns";
import { DocumentUploadBanner } from "./documentUploadBanner/DocumentUploadBanner";
import Lottie from "react-lottie";
import confettiAnimation from "~/assets/animations/confetti-animation.json";
import { motion } from "framer-motion";
import { useActionItemsService } from "~/services/action-items/useActionItemsService";
import { useProjectService } from "~/services/project";
import {
  ActionItem,
  ActionItemType,
  FALLBACK_UPLOAD_FOLDER_ROUTE,
  SignActionItemRequest,
} from "~/services/action-items/spec";
import { useClientService } from "~/services/client";
import { useTranslation } from "react-i18next";
import { useUserService } from "~/services/user";
import { useToastService } from "~/services/toast";
import { useSettingsService } from "~/services/settings";
import { UrlUtils } from "~/utils/generic/urlUtils";
import { useRoleService } from "~/services/role/useRoleService";
import { INode } from "~/services/document";
import { ErrorMessage } from "~/services/error/spec";

const defaultOptions = {
  loop: false,
  autoplay: true,
  animationData: confettiAnimation,
  renderer: "svg",
};

export const PriorityActionItemBanner: React.FC<PriorityActionItemBannerProps> = ({
  id,
  actionItem,
  isVisible = true,
  setIsVisible,
  onClick,
  ariaLabel,
  dataTestId,
  ariaDescribedBy,
  ariaLabelledBy,
  role,
}) => {
  const { t, i18n } = useTranslation("overview");
  const { t: tDocuments } = useTranslation("documents");
  const { t: tGlobal } = useTranslation("global");
  const { t: tActionItems } = useTranslation("actionItems");

  const [isUploading, setIsUploading] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [isDeclined, setIsDeclined] = useState(false);
  const [showDropZone, setShowDropZone] = useState(false);
  const [
    fileDestinationNode,
    setFileDestinationNode,
  ] = useState<INode | null>();
  const documentUploadRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const {
    submitActionItem,
    uploadDocumentsToDocumentRequest,
    sign,
    fetchActionItems,
    getActionItem,
    recentlySignedDocusignActionItem,
    recentlyDeclinedDocusignActionItem,
  } = useActionItemsService();
  const { activeProject } = useProjectService();
  const { activeClient } = useClientService();
  const { profile } = useUserService();

  const { showToast, hideAllToasts } = useToastService();
  const hasShownRecentlyDocusignSignedToast = useRef(false);
  const hasShownRecentlyDocusignDeclinedToast = useRef(false);
  const { settings, getSpoSitePath } = useSettingsService();
  const { isPractitioner } = useRoleService();

  const taskIcon = {
    iconName:
      actionItem?.type === ActionItemType.RequestItem
        ? "up-arrow"
        : "signature-request",
    backgroundColor:
      actionItem?.type === ActionItemType.RequestItem ? "ocean" : "jade",
    width: actionItem?.type === ActionItemType.RequestItem ? 10 : 14,
    height: actionItem?.type === ActionItemType.RequestItem ? 12 : 14,
  };

  const isInReviewStatus = actionItem?.status === ActionItemStatusType.INREVIEW;

  // Controls for the sliding animation
  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" },
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: { duration: 0.4, ease: "easeIn" },
    },
  };

  const showSignDocCta = (actionItem: ActionItem) => {
    const showSignButton = actionItem.assignees.some(
      assignee =>
        assignee.outcome === "None" &&
        assignee.user.bgpId === profile?.data?.bgpId
    );
    return showSignButton;
  };

  const getActionItemDetails = async (
    clientId: number,
    projectId: number,
    actionItemId: string
  ) => {
    try {
      const actionItem = await getActionItem(actionItemId, clientId, projectId);
      setFileDestinationNode(actionItem.fileDestination);
    } catch {
      setFileDestinationNode(null);
    }
  };

  useEffect(() => {
    if (recentlySignedDocusignActionItem) {
      setIsComplete(true);

      // Prevents double toast with Strict Mode
      if (!hasShownRecentlyDocusignSignedToast.current) {
        showToast({
          type: "success",
          message: t("signature-complete"),
          persist: false,
          ctaText: tGlobal("view-details"),
          withCTA: true,
          onCTAClick: () => {
            onClick();
            hideAllToasts();
          },
        });

        hasShownRecentlyDocusignSignedToast.current = true;
      }
    } else {
      setIsComplete(false);
    }
  }, [recentlySignedDocusignActionItem]);

  useEffect(() => {
    if (recentlyDeclinedDocusignActionItem) {
      setIsDeclined(true);

      // Prevents double toast with Strict Mode
      if (!hasShownRecentlyDocusignDeclinedToast.current) {
        showToast({
          type: "success",
          message: t("signature-declined"),
          persist: false,
          ctaText: tGlobal("view-details"),
          withCTA: true,
          onCTAClick: () => {
            onClick();
            hideAllToasts();
          },
        });
        hasShownRecentlyDocusignDeclinedToast.current = true;
      }
    } else {
      setIsDeclined(false);
    }
  }, [recentlyDeclinedDocusignActionItem]);

  useEffect(() => {
    if (!actionItem) {
      return;
    }
    getActionItemDetails(
      actionItem.clientId,
      actionItem.projectId,
      actionItem.id
    );
  }, [activeClient]);
  // we do not have the file destination anymore - need to fetch this because this list is being populated by the actionitems which does not return file destination

  if (!isVisible) return;

  if (!actionItem) {
    return (
      <div className={styles.priorityActionItemBannerWrapper} id={id}>
        <div
          className={classNames(styles.priorityActionItemBanner, styles.empty)}
        >
          <CustomAvatar
            imageUrl={assistantPicture}
            initials=""
            avatarSize="medium-small"
          />
          <div className={styles.info}>
            <div className={styles.actionItemInfo}>
              <div className={styles.eyebrow}>{t("good-work")}</div>
              <div
                className={styles.title}
                aria-label={t("completed-action-items")}
              >
                {t("completed-action-items")}
              </div>
            </div>
          </div>
          <button
            className={styles.closeButton}
            aria-label={t("close-action-banner")}
            onClick={() => setIsVisible(false)}
          >
            <Icon iconName="close-icon" altText="close-icon" size={16} />
          </button>
        </div>
      </div>
    );
  }

  const dueDateLabel = actionItem.dueDate
    ? getDueDateLabel(formatISO(actionItem.dueDate), i18n.language)
    : "N/A";
  const isOverdue = actionItem.dueDate
    ? isPastDue(formatISO(actionItem.dueDate))
    : false;

  const submitActionItemHandler = async (
    files: DocumentUploadFile[],
    actionItem: ActionItem
  ) => {
    if (!activeProject) return;
    if (!settings?.data) return;

    setShowDropZone(false);
    setIsUploading(true);

    if (files.length === 0) {
      setIsUploading(false);
      showToast({
        type: "error",
        message: tGlobal("upload-error"),
        persist: false,
      });
      return;
    }

    const sitePath = getSpoSitePath(actionItem.clientId, actionItem.projectId);
    const basePath = UrlUtils.join(sitePath, "/Documents");

    const path = fileDestinationNode
      ? fileDestinationNode.url.replace(basePath, "")
      : "";

    //upload documents
    try {
      const documentUploadResults = await uploadDocumentsToDocumentRequest(
        files,
        path ? path : FALLBACK_UPLOAD_FOLDER_ROUTE,
        actionItem.id
      );

      if (!documentUploadResults) {
        setIsUploading(false);
        showToast({
          type: "error",
          message: tGlobal("upload-error"),
          persist: false,
        });
        return;
      }

      let showGenericErrorToast = false;
      let uploadErrorOccurred = false;
      let duplicateNameErrorOccurred = false;

      for (let i = 0; i < documentUploadResults.length; i++) {
        const documentUploadResult = documentUploadResults[i];
        if (documentUploadResult.message === ErrorMessage.FileAlreadyExists) {
          showToast({
            type: "error",
            title: tDocuments("failed-to-upload", {
              // file validation and all the other types of upload errors
              fileName: documentUploadResult.fileName,
            }),
            message: tDocuments("file-already-exists"),
            persist: false,
            errorMessageWithLineBreaks: true,
            replaceLineBreaksWithBulletPoints: true,
          });
          uploadErrorOccurred = true;
          duplicateNameErrorOccurred = true;
        } else if (documentUploadResult.message !== "Success") {
          showGenericErrorToast = true;
          uploadErrorOccurred = true;
        }
      }

      if (uploadErrorOccurred) {
        //show generic upload toast error if no duplicate errors occurred, so multiple error toasts aren't shown
        if (showGenericErrorToast && !duplicateNameErrorOccurred) {
          showToast({
            type: "error",
            message: tGlobal("upload-error"),
            persist: false,
          });
        }
        setIsUploading(false);
        return;
      }
    } catch {
      showToast({
        type: "error",
        message: tGlobal("upload-error"),
        persist: false,
      });
      setIsUploading(false);
      return;
    }

    //submit the action item
    try {
      await submitActionItem(actionItem.id, activeProject.bgpId);
      setIsUploading(false);
      setIsComplete(true);

      // Wait to refetch so that the animation can finish first
      setTimeout(async () => {
        await fetchActionItems(activeProject.bgpId);
        showToast({
          type: "success",
          message: `${actionItem.name} ${t("in-review-status-message")}`,
          persist: false,
          withCTA: true,
          ctaText: tGlobal("view-details"),
          onCTAClick: () => onClick(),
        });
      }, 3000);
    } catch {
      showToast({
        type: "error",
        message: tActionItems("failed-to-submit"),
        persist: false,
      });
      setIsUploading(false);
      return;
    }
  };

  const fileUploadErrorHandler = () => {
    setShowDropZone(false);
  };

  const signDocumentHandler = async (actionItem: ActionItem) => {
    if (!activeProject || !activeClient || !activeClient.bgpId) {
      return;
    }

    const payload: SignActionItemRequest = {
      taskId: actionItem.id,
      projectId: activeProject?.bgpId,
      clientId: activeClient?.bgpId,
      customReturnUrl: `/client/${activeClient.id}/project/${activeProject.id}/overview?actionItemId=${actionItem.id}`, // link to the action item
    };

    setIsUploading(true);

    try {
      const { docusignUrl } = await sign(payload);
      if (docusignUrl) window.open(docusignUrl, "_self");
    } catch (error) {
      console.error(error);
      setIsUploading(false);
      showToast({
        type: "error",
        message: tActionItems("signature-request-error-message"),
        persist: false,
      });
    }
  };

  const dragOverHandler = (e: React.DragEvent) => {
    e.preventDefault();
    if (actionItem.type === ActionItemType.Signature) return;
    setShowDropZone(true);
  };

  const dragLeaveHandler = (e: React.DragEvent) => {
    e.preventDefault();
    if (
      documentUploadRef.current &&
      documentUploadRef.current.contains(e.target as Node)
    ) {
      setShowDropZone(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (!files || files.length == 0) return;

    let someFailedValidation = false;

    for (const file of files) {
      const { isValid, errors } = validateFileForUpload(file);
      if (!isValid) someFailedValidation = true;

      for (const error of errors) {
        showToast({
          title: tDocuments("failed-to-upload", { fileName: file.name }),
          type: "error",
          persist: false,
          message: error,
          errorMessageWithLineBreaks: true,
          replaceLineBreaksWithBulletPoints: true,
        });
      }
    }

    if (someFailedValidation) return;

    const filesToUpload: DocumentUploadFile[] = Object.values(files).map(
      file => ({
        time: getDateTimeLabel(formatISO(new Date())),
        file: file,
        id: file.name,
      })
    );

    submitActionItemHandler(filesToUpload, actionItem);
  };

  return (
    <div
      className={styles.priorityActionItemBannerWrapper}
      id={id}
      onDragOver={isComplete ? () => {} : dragOverHandler}
      onDragLeave={isComplete ? () => {} : dragLeaveHandler}
      aria-label={ariaLabel}
      data-testid={dataTestId}
      role={role}
      aria-labelledby={ariaLabelledBy}
      aria-describedby={ariaDescribedBy}
    >
      {showDropZone && (
        <div className={styles.documentUploadWrapper} ref={documentUploadRef}>
          <DocumentUploadBanner
            id={`${id}-document-upload`}
            showDropZone={true}
            shouldReset={!showDropZone}
            title=""
            type={
              isOverdue
                ? PriorityActionItemBannerType.Overdue
                : PriorityActionItemBannerType.Default
            }
            onFileAccept={files => submitActionItemHandler(files, actionItem)}
            onFileReject={fileUploadErrorHandler}
          />
        </div>
      )}

      <div
        className={classNames(styles.priorityActionItemBanner, styles.default, {
          [styles.overdue]: isOverdue,
          [styles.transparent]: showDropZone,
        })}
      >
        {/* <CustomAvatar
          imageUrl={assistantPicture}
          initials=""
          avatarSize="medium-small"
        /> */}
        <motion.div
          key={actionItem.id}
          variants={contentVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className={styles.info}
        >
          <div className={styles.actionItemInfo}>
            <div className={styles.eyebrow}>{t("up-next")}</div>
            <div className={styles.propertyContainer}>
              <TaskType
                iconName={taskIcon.iconName}
                backgroundColor={taskIcon.backgroundColor}
                width={taskIcon.width}
                height={taskIcon.height}
              />
              <div
                className={styles.title}
                aria-label={actionItem.name}
                onClick={onClick}
              >
                {actionItem.name}
              </div>
            </div>
            <div className={styles.propertyContainer}>
              <div
                className={classNames(styles.chip, {
                  [styles.overdue]: isOverdue,
                })}
              >
                <Chip
                  type={ChipType.DUE_DATE}
                  iconName="calendar-ltr"
                  text={dueDateLabel}
                />
              </div>
              {actionItem.status === ActionItemStatusType.INREVIEW &&
                isPractitioner && (
                  <StatusBadge
                    badgeType={StatusBadgeType.ACTION_ITEM}
                    type={actionItem.status as ActionItemStatusType}
                  />
                )}
            </div>
          </div>
          {isInReviewStatus && (
            <button
              className={styles.uploadButton}
              aria-label={tActionItems("open-action-item-details", {
                label: actionItem.name,
              })}
              onClick={onClick}
            >
              {tActionItems("review")}
            </button>
          )}
          {actionItem.type === ActionItemType.RequestItem &&
            !isInReviewStatus &&
            !isUploading &&
            !isComplete && (
              <button
                className={styles.uploadButton}
                onClick={() => {
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }}
              >
                {t("upload-files")}
                <Icon iconName="arrow-upload" />
                <input
                  hidden
                  type="file"
                  ref={fileInputRef}
                  multiple
                  onChange={handleFileInputChange}
                />
              </button>
            )}
          {actionItem.type === ActionItemType.Signature &&
            !isInReviewStatus &&
            !isUploading &&
            !isComplete &&
            !isDeclined &&
            showSignDocCta(actionItem) && (
              <Button
                id="sign-document"
                label={tGlobal("sign")}
                type={ButtonTypeEnum.secondary}
                size={ButtonSizeEnum.small}
                rightIconName="signature-icon"
                onClick={() => signDocumentHandler(actionItem)}
              />
            )}
          {isUploading && (
            <div className={styles.spinner}>
              <Spinner isLoading borderColor={"#5b6e7f"} size={"large"} />
            </div>
          )}
          {isComplete && (
            <span className={styles.complete}>
              {t("complete")}
              <div className={styles.animation}>
                <Lottie options={defaultOptions} height={200} width={200} />
              </div>
              <Icon
                iconName="success-checkmark"
                altText="Success Icon"
                size={20}
              />
            </span>
          )}
          {isDeclined && (
            <StatusBadge
              badgeType={StatusBadgeType.ACTION_ITEM}
              type={ActionItemStatusType.DECLINED}
            />
          )}
          <button
            className={styles.chevronButton}
            onClick={onClick}
            aria-label={tActionItems("open-action-item-details", {
              label: actionItem.name,
            })}
          >
            <Icon iconName="chevron-right" altText="Chevron Right" size={20} />
          </button>
        </motion.div>
      </div>
    </div>
  );
};
