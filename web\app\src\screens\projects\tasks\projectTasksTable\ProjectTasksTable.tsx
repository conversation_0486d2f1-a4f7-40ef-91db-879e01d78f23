import React, { useState, useEffect } from "react";
import styles from "./projecttaskstable.module.css";
import {
  ActionItemStatusType,
  CustomAvatar,
  Icon,
  Spinner,
  StatusBadge,
  StatusBadgeType,
  Table,
  TaskType,
  getInitials,
  isPastDue,
  getDueDateLabel,
  getDateLabel,
  AssigneeTooltip,
} from "@bcp/uikit";
import { ActionItem } from "~/services/action-items/spec";
import ViewActionItem from "~/screens/practitioner/actionItemDrawer/ViewActionItem";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import { Link, useMatch } from "react-router-dom";
import { ActionItemDeleteModal } from "~/screens/practitioner/actionItemDrawer/actionItemDeleteModal/ActionItemDeleteModal";
import { ActionItemDrawer } from "~/screens/practitioner/actionItemDrawer/ActionItemDrawer";
import { CreateActionItem } from "~/screens/practitioner/createActionItem/CreateActionItem";
import { formatISO } from "date-fns";

interface ProjectTasksTableProps {
  actionItems: ActionItem[] | undefined;
  isLoading: boolean;
  filtersApplied: boolean;
}

const ProjectTasksTable: React.FC<ProjectTasksTableProps> = ({
  actionItems,
  isLoading,
  filtersApplied,
}) => {
  const { t: tActionItemPage, i18n } = useTranslation("actionItems");
  const { t: tGlobal } = useTranslation("global");
  const [filteredData, setFilteredData] = useState<ActionItem[]>(
    actionItems || []
  );

  const [
    selectedActionItemForAction,
    setSelectedActionItemForAction,
  ] = useState<ActionItem | null>(null);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState<boolean>(
    false
  );
  const [
    isCopyActionItemDrawerVisible,
    setIsCopyActionItemDrawerVisible,
  ] = useState<boolean>(false);

  const actionItemsRouteMatch = useMatch(
    "client/:clientId/project/:projectId/action-items/:actionItemId"
  );

  const [sortConfig, setSortConfig] = useState<{
    key: keyof ActionItem;
    direction: "asc" | "desc";
  } | null>(null);

  useEffect(() => {
    if (actionItems) {
      // Make sure first time page load is based on the sorting logic set in server side.
      // Default Expected sort:
      // Prioritized and past due
      // Past due (due date < today)
      // Prioritized, then by By due date
      // Due date
      if (sortConfig) {
        const sortedData = sortData(
          sortConfig.key,
          sortConfig.direction,
          actionItems
        );
        setFilteredData(sortedData);
      } else {
        setFilteredData(actionItems);
      }
    }
  }, [actionItems, sortConfig]);

  const sortData = (
    key: keyof ActionItem,
    direction: string,
    data: ActionItem[]
  ) => {
    return data.sort((a, b) => {
      const aValue = a[key] ?? "";
      const bValue = b[key] ?? "";

      if (key === "assignees") {
        const aCount = (a.assignees ?? []).length;
        const bCount = (b.assignees ?? []).length;

        if (aCount !== bCount)
          return direction === "asc" ? bCount - aCount : aCount - bCount;

        const aFirst =
          aCount > 0
            ? a.assignees[0]?.user?.displayName?.toLowerCase() ?? ""
            : "";
        const bFirst =
          bCount > 0
            ? b.assignees[0]?.user?.displayName?.toLowerCase() ?? ""
            : "";

        if (aFirst < bFirst) return direction === "asc" ? -1 : 1;
        if (aFirst > bFirst) return direction === "asc" ? 1 : -1;

        if (aCount === 0 && bCount > 0) return 1;
        if (bCount === 0 && aCount > 0) return -1;
      }

      if (key === "fileDestination") {
        const destinationA = a.fileDestination?.name ?? "";
        const destinationB = b.fileDestination?.name ?? "";
        return direction === "asc"
          ? destinationA.localeCompare(destinationB)
          : destinationB.localeCompare(destinationA);
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        return direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (aValue < bValue) return direction === "asc" ? -1 : 1;
      if (aValue > bValue) return direction === "asc" ? 1 : -1;

      return 0;
    });
  };

  const handleSort = (key: keyof ActionItem) => {
    let direction: "asc" | "desc" = "asc";
    if (
      sortConfig &&
      sortConfig.key === key &&
      sortConfig.direction === "asc"
    ) {
      direction = "desc";
    }
    const sortedData = sortData(key, direction, [...filteredData]);
    setSortConfig({ key, direction });
    setFilteredData(sortedData);
  };

  // TODO: refactor into table component
  const getSortIcon = (key: keyof ActionItem) => (
    <Icon
      iconName={
        sortConfig?.key == key
          ? sortConfig.direction == "asc"
            ? "filter-up-arrow"
            : "filter-down-arrow"
          : "base-filter-arrows"
      }
      altText={`Sort ${sortConfig?.direction}`}
    />
  );

  const columns = [
    {
      key: "name" as keyof ActionItem,
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => handleSort("name")}
          aria-label={tGlobal(
            sortConfig?.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: tActionItemPage("name") }
          )}
        >
          {tActionItemPage("name")}
          {getSortIcon("name")}
        </button>
      ),
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (task: ActionItem) => (
        <div className={styles.nameCell}>
          {task.type === "Signature" ? (
            <TaskType
              iconName="signature-request"
              backgroundColor="jade"
              width={14}
              height={14}
            />
          ) : (
            <TaskType
              iconName="up-arrow"
              backgroundColor="ocean"
              width={10}
              height={12}
            />
          )}
          <div className={styles.task}>
            <div className={styles.taskName}>
              <Link
                role="button"
                className={classNames(styles.taskLink, styles.truncate)}
                to={`${location.pathname}/${task.id}`}
                id={task.id}
              >
                {task.name}
              </Link>
              {task.flagged ? (
                <Icon
                  iconName="priority"
                  color="red"
                  width={12}
                  height={11.5}
                />
              ) : null}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "status" as keyof ActionItem,
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => handleSort("status")}
          aria-label={tGlobal(
            sortConfig?.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: tActionItemPage("status") }
          )}
        >
          {tActionItemPage("status")}
          {getSortIcon("status")}
        </button>
      ),
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (task: ActionItem) => (
        <div className={styles.statusBadge}>
          <StatusBadge
            badgeType={StatusBadgeType.ACTION_ITEM}
            type={task.status as ActionItemStatusType}
          />
        </div>
      ),
    },
    {
      key: "dueDate" as keyof ActionItem,
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => handleSort("dueDate")}
          aria-label={tGlobal(
            sortConfig?.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: tActionItemPage("due-date") }
          )}
        >
          {tActionItemPage("due-date")}
          {getSortIcon("dueDate")}
        </button>
      ),
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (task: ActionItem) => {
        if (!task.dueDate) {
          return;
        }
       

        // Check if the task is Complete or In Review
        const isCompleteOrInReview = task.status === ActionItemStatusType.COMPLETE ||
                                   task.status === ActionItemStatusType.INREVIEW;

        // For Complete or In Review items, use full date format; otherwise use due date label
        const displayDate = isCompleteOrInReview
          ? getDateLabel(formatISO(task.dueDate), i18n.language)
          : getDueDateLabel(formatISO(task.dueDate), i18n.language);

        return (
          <div
            className={classNames(styles.dueDate, {
              // Only apply red color for past due items that are NOT Complete or In Review
              [styles.dueDatePastDue]: isPastDue(formatISO(task.dueDate)) && !isCompleteOrInReview,
            })}
          >
            {displayDate}
          </div>
        );
      },
    },
    {
      key: "assignees" as keyof ActionItem,
      header: (
        <button
          className={styles.sortableHeader}
          onClick={() => handleSort("assignees")}
          aria-label={tGlobal(
            sortConfig?.direction == "asc"
              ? "sort-by-ascending"
              : "sort-by-descending",
            { headerName: tActionItemPage("assignees") }
          )}
        >
          {tActionItemPage("assignee")}
          {getSortIcon("assignees")}
        </button>
      ),
      headerClass: "hideOnSmallScreen",
      tableCellClass: "hideOnSmallScreen",
      showFilterIcon: false,
      render: (task: ActionItem) => {
        const { assignees } = task;
        return (
          // TODO: Refactor to a component
          <div className={styles.assigneeCell}>
            {assignees.slice(0, 3).map((assignee, index) => (
              <span
                key={index}
                className={styles.assignee}
                style={{ zIndex: assignees.length - index }}
              >
                <AssigneeTooltip
                  message={assignee.user.displayName}
                  inputId={crypto.randomUUID()}
                  direction="down"
                  smallSize
                  withArrow={false}
                >
                  <CustomAvatar
                    avatarSize="x-small"
                    fontSize="x-small"
                    type="monogram"
                    initials={getInitials(assignee.user.displayName).charAt(0)}
                    fontBold
                  />
                </AssigneeTooltip>
              </span>
            ))}
            {assignees.length > 3 && (
              <AssigneeTooltip
                message={assignees
                  .slice(3)
                  .map(a => a.user.displayName)
                  .join("\n")}
                inputId={crypto.randomUUID()}
                direction="down"
                smallSize
                withArrow={false}
              >
                <span
                  className={`${styles.assignee} ${styles.overlapped} ${styles.assigneeMore}`}
                  style={{ zIndex: 0 }}
                >
                  +{assignees.length - 3}
                </span>
              </AssigneeTooltip>
            )}
            {assignees.length === 0 && (
              <div className={styles.assigneeUnassigned}>
                <span className={`${styles.unassignedIcon}`}>
                  <AssigneeTooltip
                    message={tActionItemPage("unassigned")}
                    inputId={crypto.randomUUID()}
                    direction="down"
                    smallSize
                    withArrow={false}
                  >
                    <Icon iconName="person-filled" altText="" />
                  </AssigneeTooltip>
                </span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      key: "spacer" as keyof ActionItem,
      header: <span className="sr-only">{tActionItemPage("actions")}</span>,
      headerClass: "",
      showFilterIcon: false,
      tableCellClass: "",
      render: (actionItem: ActionItem) => (
        <ViewActionItem
          actionItem={actionItem}
          copyActionItemHandler={copyActionItemHandler}
          deleteActionItemHandler={deleteActionItemHandler}
        />
      ),
    },
  ];

  const deleteActionItemHandler = (actionItem: ActionItem) => {
    setSelectedActionItemForAction(actionItem);
    setIsDeleteModalVisible(true);
  };

  const copyActionItemHandler = (actionItem: ActionItem) => {
    setSelectedActionItemForAction(actionItem);
    setIsCopyActionItemDrawerVisible(true);
  };

  const emptyMessage = filtersApplied
    ? tActionItemPage("no-search-results-title")
    : tActionItemPage("no-action-items");

  const emptySubMessage = filtersApplied
    ? tActionItemPage("no-search-results-message")
    : "";

  const emptyMessageIcon = filtersApplied ? "search-icon" : "task-ltr-icon";

  useEffect(() => {
    if (!isDeleteModalVisible) {
      setSelectedActionItemForAction(null);
    }
  }, [isDeleteModalVisible]);

  useEffect(() => {
    if (!isCopyActionItemDrawerVisible) {
      setSelectedActionItemForAction(null);
    }
  }, [isCopyActionItemDrawerVisible]);

  return (
    <>
      <div className={styles.container}>
        {isLoading ? (
          <div className={styles.spinnerContainer}>
            <Spinner isLoading={true} size={"large"} />
          </div>
        ) : (
          <>
            <Table
              emptyMessage={emptyMessage}
              emptySubMessage={emptySubMessage}
              emptyMessageIcon={emptyMessageIcon}
              columns={columns}
              data={filteredData}
              className={"dropdown-menu-container"}
              dataKeyProperty={"id"}
            />
            {actionItemsRouteMatch &&
              actionItemsRouteMatch.params.actionItemId && (
                <ActionItemDrawer
                  actionItemId={actionItemsRouteMatch.params.actionItemId}
                />
              )}
            {selectedActionItemForAction && isCopyActionItemDrawerVisible && (
              <CreateActionItem
                type={selectedActionItemForAction.type}
                duplicateDataId={selectedActionItemForAction.id}
                isOpen={isCopyActionItemDrawerVisible}
                setIsOpen={setIsCopyActionItemDrawerVisible}
              />
            )}
            <ActionItemDeleteModal
              actionItem={selectedActionItemForAction}
              isVisible={isDeleteModalVisible}
              setIsVisible={setIsDeleteModalVisible}
            />
          </>
        )}
      </div>
    </>
  );
};

export default ProjectTasksTable;
