import React from "react";
import { Direction } from "./spec";
import { defaultProps } from "~/default.spec";
export interface TooltipProps extends defaultProps {
    children: React.ReactNode;
    message: string;
    inputId: string;
    direction?: Direction;
    closeDelay?: number;
    openDelay?: number;
    withArrow?: boolean;
    smallSize?: boolean;
    closerToDrawerButton?: boolean;
    wide?: boolean;
    disabled?: boolean;
}
export declare const CustomTooltip: React.FC<TooltipProps>;
