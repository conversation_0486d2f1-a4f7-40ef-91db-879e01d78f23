import {
  EditProjectStages,
  Modal,
  ModalSize,
  ProjectTimeline,
  Stage,
  Button,
  ButtonSizeEnum,
  ButtonTypeEnum,
  Icon,
  ProgressCompletion,
  SuggestedUser,
  ActionItemStatusType,
  getTimezoneConvertedDate,
  getDateTimeLabel,
} from "@bcp/uikit";
import { formatISO } from "date-fns";
import { PriorityActionItemBanner } from "../priorityActionItemBanner";
import {
  ActionItem,
  ActionItemType,
  BGPSigningStatusOutcomeEnum,
} from "~/services/action-items/spec";
import { useEffect, useRef, useState } from "react";
import { useActionItemsService } from "~/services/action-items/useActionItemsService";
import { ActionItemDrawer } from "~/screens/practitioner/actionItemDrawer/ActionItemDrawer";
import styles from "./overview.module.css";
import { ProjectRiskEntry } from "../projectRiskEntry/ProjectRiskEntry";
import { ActionItemEntry } from "./actionItemEntry/ActionItemEntry";
import { IRisk } from "~/services/risk/spec";
import { RiskModal } from "./risks/riskModal/RiskModal";
import { ProgressOverviewPlaceholder } from "./placeholder/ProgressOverviewPlaceholder";
import { TimelinePlaceholder } from "./placeholder/TimelinePlaceholder";
import { RiskPlaceholder } from "./placeholder/RiskPlaceholder";
import { useUserService, IUser } from "~/services/user";
import { ActionItemPlaceholder } from "./placeholder/ActionItemPlaceholder";
import { InviteUsersModal } from "~/shell/inviteUsersModal/InviteUsersModal";
import { UserData, useTeamMembers } from "~/services/teamMembers";
import {
  IProjectStage,
  IProjectTemplateStages,
  IUpdatedProject,
  useProjectService,
} from "~/services/project";
import { useTranslation } from "react-i18next";
import { useClientService } from "~/services/client";
import { UserGroups, useRoleService } from "~/services/role";
import { useRiskService } from "~/services/risk/useRiskService";
import useNavigateWithParams from "~/utils/navigate/useNavigateWithParams";
import classNames from "classnames";
import EmptyState from "~/screens/home/<USER>/emptyState/EmptyState";
import { useToastService } from "~/services/toast";
import { Accordion } from "@ark-ui/react";

export const Overview: React.FC = () => {
  const { showToast } = useToastService();
  const { profile } = useUserService();
  const {
    teamMembers,
    fetchTeamMembers,
    allUsers,
    clientUsers,
    fetchClientUsers,
    fetchMemberFirmUsers,
  } = useTeamMembers();
  const { t, i18n } = useTranslation("overview");
  const { t: tGlobal } = useTranslation("global");
  const { t: tAccessibility } = useTranslation("accessibility");
  const { t: tProject } = useTranslation("project");
  const { t: tProjectStages, ready: tProjectStagesReady } = useTranslation(
    "projectStages"
  );
  const { t: tRisk } = useTranslation("risks");
  const { isClient, isPractitioner } = useRoleService();
  const { fetchRisks, risks, deleteProjectRisk } = useRiskService();
  const [
    showPriorityActionItemBanner,
    setShowPriorityActionItemBanner,
  ] = useState(true);
  const [drawerActionItem, setDrawerActionItem] = useState<ActionItem | null>(
    null
  );
  const [
    showEditProjectTimelineModal,
    setShowEditProjectTimelineModal,
  ] = useState(false);
  const [stages, setStages] = useState<Stage[]>([]);
  const [disableSaveStages, setDisableSaveStages] = useState<boolean>(false);
  const [showValidationErrors, setShowValidationErrors] = useState<boolean>(
    false
  );
  const [percentage, setPercentage] = useState<number>(40);
  const [progressionUpdatedDate, setProgressionUpdatedDate] = useState<Date>(
    new Date()
  );
  const [timelineUpdatedDate, setTimelineUpdatedDate] = useState<Date | null>(
    null
  );
  // Risks
  const [showRiskModal, setShowRiskModal] = useState(false);
  const [activeRisk, setActiveRisk] = useState<IRisk | undefined>();
  const [showRemoveRiskModal, setShowRemoveRiskModal] = useState(false);
  const riskWrapperRef = useRef<HTMLDivElement>(null);

  const {
    activeProject,
    changeCompletionPercentage,
    updateProjectStages,
    fetchProjects,
    projects,
    fetchProjectTemplate,
    stageTemplate,
  } = useProjectService();
  const {
    actionItems,
    fetchActionItems,
    recentlySignedDocusignActionItem,
    recentlyDeclinedDocusignActionItem,
  } = useActionItemsService();
  const navigate = useNavigateWithParams();
  const [editedStages, setEditedStages] = useState<Stage[]>([]);

  const [isProjectOverviewLoading, setIsProjectOverviewLoading] = useState(
    true
  );
  // Used to prevent a flash of the already loaded action items then spinner to load updated action items when we nav to overview
  // Subject to change or to be removed in future tech debt tickets regarding action items
  const [isActionItemsLoading, setIsActionItemsLoading] = useState<boolean>(
    true
  );
  const [isTimelineLoading, setIsTimelineLoading] = useState(true);
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  const [isInviteUsersModalOpen, setIsInviteUsersModalOpen] = useState(false);
  const [showAssignedOnly, setShowAssignedOnly] = useState(false);
  const [suggestedTeamMemberList, setSuggestedTeamMemberList] = useState<
    SuggestedUser[]
  >([]);
  const { activeClient } = useClientService();
  const sortStagesByTemplateOrder = <
    T extends { translationKey: string; title: string; sortOrder?: number }
  >(
    items: T[],
    parentStageTK?: string
  ): T[] => {
    if (stageTemplate) {
      return items.sort((a, b) => {
        // First priority: Sort by database sortOrder if both items have it
        if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
          return a.sortOrder - b.sortOrder;
        }

        // Second priority: If only one has sortOrder, prioritize it
        if (a.sortOrder !== undefined && b.sortOrder === undefined) {
          return -1; // a comes first
        }
        if (a.sortOrder === undefined && b.sortOrder !== undefined) {
          return 1; // b comes first
        }

        // Third priority: Fall back to template sortingOrder
        let aInStageTemplate: IProjectTemplateStages | undefined;
        let bInStageTemplate: IProjectTemplateStages | undefined;
        if (!parentStageTK) {
          aInStageTemplate = stageTemplate.find(
            stage =>
              stage.translationKey == a.translationKey || stage.name == a.title
          );
          bInStageTemplate = stageTemplate.find(
            stage =>
              stage.translationKey == b.translationKey || stage.name == b.title
          );
        } else {
          aInStageTemplate = stageTemplate
            .find(stage => stage.translationKey == parentStageTK)
            ?.substages.find(
              substage =>
                substage.translationKey == a.translationKey ||
                substage.name == a.title
            );
          bInStageTemplate = stageTemplate
            .find(stage => stage.translationKey == parentStageTK)
            ?.substages.find(
              substage =>
                substage.translationKey == b.translationKey ||
                substage.name == b.title
            );
        }
        if (!aInStageTemplate || !bInStageTemplate) return 0;

        return aInStageTemplate.sortingOrder - bInStageTemplate.sortingOrder;
      });
    } else {
      return [];
    }
  };

  const fetchActionItemsForTopItemsList = async () => {
    if (!activeProject) return;
    setIsActionItemsLoading(true);
    await fetchActionItems(activeProject.bgpId);
    setIsActionItemsLoading(false);
  };

  const fetchAllUsersForInvite = async () => {
    await fetchMemberFirmUsers();
    await fetchClientUsers();
  };

  useEffect(() => {
    if (
      !clientUsers.isBusy &&
      clientUsers.data !== undefined &&
      !allUsers.isBusy &&
      allUsers.data !== undefined
    ) {
      let suggestUserDataList: UserData[] = [];

      if (isClient) {
        // only grab client users if the current user is a client user
        suggestUserDataList =
          clientUsers.data?.filter(
            u => u.userGroupName === UserGroups.CLIENT_USER
          ) ?? [];
      } else {
        // exclude client admin users from suggested users
        suggestUserDataList = [
          ...(allUsers.data?.filter(
            u => u.userGroupName !== UserGroups.CLIENT_ADMIN_USER
          ) ?? []),
        ];
        // Add client users to the list of suggested users
        for (const item of clientUsers.data) {
          if (
            !suggestUserDataList.some(u => u.uniqueUserId === item.uniqueUserId)
          ) {
            suggestUserDataList.push(item);
          }
        }
      }

      if (suggestUserDataList.length > 0) {
        setSuggestedTeamMemberList(
          suggestUserDataList
            .filter(
              u => !teamMembers?.data?.some(t => t.bgpId === u.uniqueUserId)
            ) //filter out existing users
            .map((user, i) => {
              return {
                name: user.displayName,
                email: user.emailAddress,
                avatarUrl: "",
                uniqueUserId: user.uniqueUserId,
                userGroupName: user.userGroupName,
                id: i,
              };
            })
            .sort((a, b) => a.name.localeCompare(b.name))
        );
      }
    }
  }, [clientUsers, allUsers]);

  useEffect(() => {
    const checkVisitStatus = async () => {
      if (
        activeProject &&
        !localStorage.getItem(`seenProjectIntro_${activeProject.bgpId}`)
      ) {
        setShowWelcomeModal(true);
      }
    };

    checkVisitStatus();
    fetchActionItemsForTopItemsList();
  }, [activeProject]);

  useEffect(() => {
    if (activeProject && !projects?.isBusy && projects?.data) {
      if (activeProject.templateId) {
        fetchProjectTemplate(activeProject.templateId);
      }
      const currentActiveProject = projects.data.find(
        project => project.id === activeProject.id
      );
      if (currentActiveProject) {
        setPercentage(currentActiveProject?.completionPercentage);
        setProgressionUpdatedDate(currentActiveProject?.updatedAt);
      }
    }
  }, [activeProject, projects, projects?.isBusy, stages]);

  const handleCloseModal = () => {
    if (activeProject) {
      localStorage.setItem(`seenProjectIntro_${activeProject.bgpId}`, "true");
    }
    setShowWelcomeModal(false);
  };

  const [isFetching, setIsFetching] = useState<boolean>(false);
  const formatTitle: (input: string) => string = input =>
    input
      .split("_")
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");

  const formatToUnderscore: (input: string) => string = input =>
    input
      .toLowerCase()
      .split(" ")
      .join("_");

  useEffect(() => {
    setTimeout(() => {
      setIsProjectOverviewLoading(false);
      setIsTimelineLoading(false);
    }, 2500);
    const fetchData = async () => {
      if (activeProject && !isFetching) {
        setIsFetching(true);
      }
    };
    fetchData();
  }, [activeProject, projects, activeClient]);

  useEffect(() => {
    if (
      activeProject &&
      activeProject.stages &&
      activeProject?.stages?.length &&
      stageTemplate &&
      tProjectStagesReady
    ) {
      setIsTimelineLoading(true);

      const newStages: Stage[] = activeProject.stages.map((stage, index) => {
        const startDate = new Date(stage.startsAt);
        const endDate = new Date(stage.endsAt);

        const substages = stage.childProjectStages.map(substage => {
          const substageStartDate = new Date(substage.startsAt);
          const substageEndDate = new Date(substage.endsAt);

          return {
            id: substage.id,
            title: formatTitle(substage.kind),
            translationKey: substage.translationKey,
            status: substage.status,
            active: substage.isEnabled,
            startDate: substageStartDate,
            endDate: substageEndDate,
            isSubstage: true,
            createdAt: substage?.createdAt,
            updatedAt: substage?.updatedAt,
            sortOrder: substage.sortOrder,
          };
        });

        return {
          id: stage.id,
          title: formatTitle(stage.kind),
          translationKey: stage.translationKey,
          subtitle: `${stage.childProjectStages.length} Substages`,
          status: stage.status,
          active: stage.isEnabled,
          substages: sortStagesByTemplateOrder(substages, stage.translationKey),
          isLastStage: index === (activeProject.stages?.length ?? 0) - 1,
          createdAt: stage?.createdAt,
          updatedAt: stage?.updatedAt,
          startDate: startDate,
          endDate: endDate,
          isSubstage: false,
        } as Stage;
      });

      const orderedStages = sortStagesByTemplateOrder(newStages);
      // const orderedStages = newStages;
      setStages([...orderedStages]);

      const getLatestUpdatedAt = (stage: Stage): number => {
        // Parse dates with proper UTC handling for API strings
        const parseDate = (dateString: string | Date) => {
          if (dateString instanceof Date) return dateString;
          return new Date(
            dateString.endsWith("Z") ? dateString : `${dateString}Z`
          );
        };

        const parentUpdatedAt = parseDate(stage.updatedAt).getTime();

        const substageDates = (stage.substages ?? [])
          .map(sub => parseDate(sub.updatedAt).getTime())
          .filter(date => !isNaN(date));

        const allDates = [parentUpdatedAt, ...substageDates].filter(
          date => !isNaN(date)
        );

        return Math.max(...allDates);
      };
      const sortedStagesByUpdatedDate = newStages.sort(
        (a, b) => getLatestUpdatedAt(b) - getLatestUpdatedAt(a)
      );
      const mostRecentUpdatedAt = getLatestUpdatedAt(
        sortedStagesByUpdatedDate[0]
      );

      setTimelineUpdatedDate(new Date(mostRecentUpdatedAt));
    } else {
      setStages([]);
      setTimelineUpdatedDate(null);
    }
  }, [
    activeClient,
    activeProject,
    projects,
    isFetching,
    tProjectStagesReady,
    stageTemplate,
  ]);

  // Helper function to deep clone stages while preserving Date objects
  const deepCloneStages = (stages: Stage[]): Stage[] => {
    return stages.map(stage => ({
      ...stage,
      startDate: stage.startDate ? new Date(stage.startDate) : undefined,
      endDate: stage.endDate ? new Date(stage.endDate) : undefined,
      createdAt: new Date(stage.createdAt),
      updatedAt: new Date(stage.updatedAt),
      substages: stage.substages?.map(substage => ({
        ...substage,
        startDate: substage.startDate
          ? new Date(substage.startDate)
          : undefined,
        endDate: substage.endDate ? new Date(substage.endDate) : undefined,
        createdAt: new Date(substage.createdAt),
        updatedAt: new Date(substage.updatedAt),
      })),
    }));
  };

  useEffect(() => {
    if (showEditProjectTimelineModal) {
      setEditedStages(deepCloneStages(stages));
      setShowValidationErrors(false);
      setDisableSaveStages(false);
    }
  }, [showEditProjectTimelineModal]);

  const createNewSubstage = (sortOrder: number = 0) => {
    const newSubstage = {
      id: crypto.randomUUID(),
      title: "TEMPLATE_PLACEHOLDER", // Use a placeholder to indicate this is a new template substage
      translationKey: "",
      status: "upcoming",
      active: true,
      isSubstage: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      sortOrder: sortOrder,
      projectId: activeProject?.id, // Associate with current project
    };

    return newSubstage;
  };

  const createNewCustomSubstage = (sortOrder: number = 0) => ({
    id: crypto.randomUUID(),
    title: "",
    translationKey: "", // Empty for custom substages
    status: "upcoming",
    active: true,
    isSubstage: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    sortOrder: sortOrder,
    projectId: activeProject?.id, // Associate with current project
    // No flag means it's a custom substage
  });

  // Validation function to check if all required fields are filled
  const validateStages = (stages: Stage[]): boolean => {
    return stages.every(stage => {
      // Parent stages should always have a title (Kind field in DB)
      const stageHasError = !stage.title || stage.title.trim() === "";

      if (stageHasError) return false;

      const substageErrors = stage.substages?.some(substage => {
        // Determine if this is a template substage that hasn't been selected yet
        const isUnselectedTemplateSubstage =
          substage.title === "TEMPLATE_PLACEHOLDER";

        // Determine if this is a custom substage (has title but no translationKey)
        const isCustomSubstage =
          !isUnselectedTemplateSubstage &&
          (!substage.translationKey || substage.translationKey.trim() === "");

        // For unselected template substages, always show error
        // For custom substages, check title; for selected template substages, check translationKey
        const nameError = isUnselectedTemplateSubstage
          ? true // Always error if template substage not selected
          : isCustomSubstage
          ? !substage.title || substage.title.trim() === ""
          : !substage.translationKey || substage.translationKey.trim() === "";

        const dateError = !substage.startDate || !substage.endDate;
        const statusError = !substage.status || substage.status === "";

        return nameError || dateError || statusError;
      });

      return !substageErrors;
    });
  };

  const handleSaveChanges = async () => {
    // Trigger validation
    const isValid = validateStages(editedStages);

    if (!isValid) {
      setShowValidationErrors(true);
      setDisableSaveStages(true);
      return;
    }

    setStages(...[sortStagesByTemplateOrder(editedStages)]);
    const stagesForUpdate: IProjectStage[] = editedStages.map(stage => {
      const startsAt = stage.startDate;
      const endsAt = stage.endDate;
      return {
        id: stage.id,
        kind: formatToUnderscore(stage.title),
        translationKey: stage.translationKey,
        status: formatToUnderscore(stage.status),
        projectCompletion: stage.projectCompletion,
        isEnabled: stage.active,
        startsAt: startsAt,
        endsAt: endsAt,
        createdAt: new Date(stage.createdAt),
        updatedAt: new Date(),
        childProjectStages: stage.substages?.map((substage: Stage) => {
          const subStageStartsAt = substage.startDate;
          const subStageEndsAt = substage.endDate;
          const isCustomSubstage = substage._isNewTemplate
            ? false // New template substage
            : !substage.translationKey || substage.translationKey.trim() === "";

          return {
            id: substage.id,
            kind: formatToUnderscore(substage.title),
            translationKey: isCustomSubstage
              ? "" // For custom substages, translationKey should be empty
              : substage.translationKey, // For template substages, use the actual translationKey
            status: formatToUnderscore(substage.status),
            projectCompletion: stage.projectCompletion,
            isEnabled: substage.active,
            startsAt: subStageStartsAt,
            endsAt: subStageEndsAt,
            createdAt: new Date(substage.createdAt),
            updatedAt: new Date(),
            sortOrder: substage.sortOrder,
            childProjectStages: [], // Substages don't have child stages
          } as IProjectStage;
        }),
      } as IProjectStage;
    });
    if (activeProject && activeProject.stages) {
      const updatedProject = (activeProject as unknown) as IUpdatedProject;
      updatedProject.stages = stagesForUpdate;
      try {
        setDisableSaveStages(true);
        await updateProjectStages(updatedProject);
        showToast({
          title: tProject("stages-updated"),
          type: "success",
          persist: false,
        });
        fetchProjects();
      } catch (e) {
        console.log(e);
        showToast({
          title: tProject("stages-update-error"),
          type: "error",
          persist: false,
        });
      }
    }
    setShowEditProjectTimelineModal(false);
    setShowValidationErrors(false);
  };

  const handleCancelChanges = () => {
    setEditedStages(stages!);
    setShowEditProjectTimelineModal(false);
    setShowValidationErrors(false);
    setDisableSaveStages(false);
  };

  const handleDeleteSubStage = (stageId: string, subStageId: string) => {
    setEditedStages(prevStages =>
      prevStages.map(stage =>
        stage.id === stageId
          ? {
              ...stage,
              substages: stage.substages?.filter(
                substage => substage.id !== subStageId
              ),
            }
          : stage
      )
    );
  };

  useEffect(() => {
    // Helper function to compare stages while handling Date objects properly
    const compareStages = (stages1: Stage[], stages2: Stage[]): boolean => {
      if (stages1.length !== stages2.length) return false;

      return stages1.every((stage1, index) => {
        const stage2 = stages2[index];
        if (!stage2) return false;

        // Compare basic properties
        const basicPropsMatch =
          stage1.id === stage2.id &&
          stage1.title === stage2.title &&
          stage1.translationKey === stage2.translationKey &&
          stage1.status === stage2.status &&
          stage1.active === stage2.active &&
          stage1.projectCompletion === stage2.projectCompletion;

        // Compare dates
        const datesMatch =
          stage1.startDate?.getTime() === stage2.startDate?.getTime() &&
          stage1.endDate?.getTime() === stage2.endDate?.getTime();

        // Compare substages
        const substagesMatch = (() => {
          if (!stage1.substages && !stage2.substages) return true;
          if (!stage1.substages || !stage2.substages) return false;
          if (stage1.substages.length !== stage2.substages.length) return false;

          return stage1.substages.every((sub1, subIndex) => {
            const sub2 = stage2.substages![subIndex];
            if (!sub2) return false;

            return (
              sub1.id === sub2.id &&
              sub1.title === sub2.title &&
              sub1.translationKey === sub2.translationKey &&
              sub1.status === sub2.status &&
              sub1.active === sub2.active &&
              sub1.startDate?.getTime() === sub2.startDate?.getTime() &&
              sub1.endDate?.getTime() === sub2.endDate?.getTime()
            );
          });
        })();

        return basicPropsMatch && datesMatch && substagesMatch;
      });
    };

    const hasNoChanges = compareStages(editedStages, stages);

    // Only disable save button if there are no changes, not for validation errors
    // Validation errors will be handled when user clicks Save
    if (showValidationErrors) {
      // If validation errors are being shown, check if validation now passes
      const isValid = validateStages(editedStages);
      if (isValid) {
        // Reset validation errors if all fields are now valid
        setShowValidationErrors(false);
        setDisableSaveStages(hasNoChanges);
      } else {
        // Keep save button disabled until validation passes
        setDisableSaveStages(true);
      }
    } else {
      // Normal case: only disable if no changes
      setDisableSaveStages(hasNoChanges);
    }
  }, [editedStages, showValidationErrors]);

  const handleAddSubStage = (stageId: string) => {
    setEditedStages(prevStages =>
      prevStages.map(stage => {
        if (stage.id === stageId) {
          const nextSortOrder = stage.substages ? stage.substages.length : 0;
          const newSubstage = createNewSubstage(nextSortOrder);

          return {
            ...stage,
            substages: stage.substages
              ? stage.substages.concat(newSubstage)
              : [newSubstage],
          };
        }
        return stage;
      })
    );
  };

  const handleAddCustomSubStage = (stageId: string) => {
    setEditedStages(prevStages =>
      prevStages.map(stage => {
        if (stage.id === stageId) {
          const nextSortOrder = stage.substages ? stage.substages.length : 0;
          const newCustomSubstage = createNewCustomSubstage(nextSortOrder);

          return {
            ...stage,
            substages: stage.substages
              ? stage.substages.concat(newCustomSubstage)
              : [newCustomSubstage],
          };
        }
        return stage;
      })
    );
  };
  const doesUserHaveSigningStatusOnActionItem = (
    actionItem: ActionItem,
    userBgpId: string,
    signingStatus: BGPSigningStatusOutcomeEnum
  ) => {
    if (actionItem.type === ActionItemType.Signature) {
      const doesUserHaveSigningStatus = actionItem.assignees.some(
        assignee =>
          assignee.user.bgpId === userBgpId &&
          assignee.outcome === signingStatus
      );

      return doesUserHaveSigningStatus;
    }

    return false;
  };

  const getTopActionItemsList = (
    actionItems: ActionItem[],
    userBgpId: string
  ) => {
    const filtered = actionItems.filter(actionItem => {
      const isAssignedToCurrentUser = actionItem.assignees.some(
        assignee => assignee.user.bgpId === userBgpId
      );

      // For Practitioners, include both TODO and IN_REVIEW status
      // For Clients, only include TODO status
      const validStatuses = isPractitioner
        ? [ActionItemStatusType.TODO, ActionItemStatusType.INREVIEW]
        : [ActionItemStatusType.TODO];

      if (!validStatuses.includes(actionItem.status as ActionItemStatusType)) {
        return false;
      }

      if (showAssignedOnly && !isAssignedToCurrentUser) {
        return false;
      }

      if (
        showAssignedOnly &&
        doesUserHaveSigningStatusOnActionItem(
          actionItem,
          userBgpId,
          BGPSigningStatusOutcomeEnum.Signed
        )
      ) {
        return false;
      }

      return true;
    });

    const sorted = filtered.sort((a, b) => {
      if (a.flagged !== b.flagged) {
        return b.flagged ? 1 : -1; // true first
      }

      const dateA = new Date(a.dueDate).getTime();
      const dateB = new Date(b.dueDate).getTime();

      if (dateA !== dateB) {
        return dateA - dateB;
      }

      return a.name.localeCompare(b.name);
    });

    return sorted;
  };

  useEffect(() => {
    if (activeProject?.id) fetchRisks(activeProject?.id);
  }, [activeProject?.id, showRiskModal, showRemoveRiskModal]);

  const handleUpdateCompletionPercentage = async (
    newPercentage: number
  ): Promise<void> => {
    if (activeProject?.id) {
      try {
        await changeCompletionPercentage(activeProject?.id, newPercentage);
        showToast({
          title: t("progress-completion-updated"),
          type: "success",
          persist: false,
        });
        fetchProjects();
      } catch (error) {
        showToast({
          title: "Error updating completion percentage",
          type: "error",
          persist: false,
        });
        console.error("Error updating completion percentage:", error);
      }
    }
  };

  const handleDeleteRisk = async () => {
    try {
      if (activeRisk?.id) {
        await deleteProjectRisk(activeRisk?.id);
        setActiveRisk(undefined);
        setShowRemoveRiskModal(false);
        showToast({
          type: "success",
          message: tRisk("delete-risk-message"),
          persist: false,
        });
      }
    } catch (error) {
      console.error(error);
      showToast({
        type: "error",
        message: "Error deleting risk",
        persist: false,
      });
    }
  };

  const handleSendInvite = async () => {
    setIsInviteUsersModalOpen(false);
    await fetchTeamMembers(activeProject?.bgpId as number);
  };

  const sortedTeamMembers: IUser[] = [...(teamMembers?.data ?? [])].sort(
    (a, b) => (b.viewedVirtualTour ? 1 : 0) - (a.viewedVirtualTour ? 1 : 0)
  );

  const viewAllTotal = actionItems.data ? ` (${actionItems.data.length})` : "";

  // Top Action Items
  // - The top action items list contains up to 5 action items in the project that "need attention", sorted by:
  //   1. Priority
  //   2. Due Date
  //   3. Alphabetical
  // "Need attention" means:
  //  - For a Document Request Action Item, it is in the BCP "To Do" state (BGP states of Open, In Progress, Returned)
  //  - For a Signature Request Action Item, it is in the BCP "To Do" state AND the current user has not yet signed
  // - When "Show assigned to me" is toggled on, "Need attention" means:
  //  - For a Document Request Action Item, it is in the BCP "To Do" state AND the current user is an assignee
  //  - For a Signature Request Action Item, it is in the BCP "To Do" state AND the current user is an assignee who has not yet signed
  //
  // Priority Banner
  // - The priority banner displays the top action item for the current user:
  //   - For a Document Request Action Item, it is the first one in the top action items list for which the current user is an assignee
  //   - For a Signature Request Action Item, it is the first one in the top action items list for which the current user is an assignee who has not yet signed/declined, and is not pending their turn
  // - The Priority Banner is only visible for Client Users (not Practitioners)
  // - The item displayed in the Priority Banner will *not* be displayed in the top action items list
  // - If there are no action items that meet these criteria, then the Priority Banner displays an "all caught up" message

  let topActionItemsList: ActionItem[] = [];
  let topActionItem: ActionItem | undefined;

  if (actionItems.data && profile?.data) {
    topActionItemsList = getTopActionItemsList(
      actionItems.data,
      profile.data.bgpId
    );
  }

  const topActionItemIndex = topActionItemsList.findIndex(actionItem =>
    actionItem.assignees.some(
      assignee =>
        assignee.user.bgpId === profile?.data?.bgpId &&
        !doesUserHaveSigningStatusOnActionItem(
          actionItem,
          profile?.data?.bgpId ?? "",
          BGPSigningStatusOutcomeEnum.Signed
        ) &&
        !doesUserHaveSigningStatusOnActionItem(
          actionItem,
          profile?.data?.bgpId ?? "",
          BGPSigningStatusOutcomeEnum.Declined
        )
    )
  );

  const configureTopActionItemFromDocusign = (actionItem: ActionItem) => {
    topActionItem = actionItem;

    const recentItemIndex = topActionItemsList.findIndex(
      actionItem => actionItem.id === actionItem.id
    );

    if (recentItemIndex > -1) {
      topActionItemsList.splice(recentItemIndex, 1);
    }
  };

  // Display the declined/signed action item in the priority action item banner, before being cleared
  if (recentlyDeclinedDocusignActionItem) {
    configureTopActionItemFromDocusign(recentlyDeclinedDocusignActionItem);
  } else if (recentlySignedDocusignActionItem) {
    configureTopActionItemFromDocusign(recentlySignedDocusignActionItem);
  } else if (topActionItemIndex > -1) {
    topActionItem = topActionItemsList[topActionItemIndex];
    topActionItemsList.splice(topActionItemIndex, 1);
  }

  topActionItemsList = topActionItemsList.slice(0, 5);

  const actionItemToShow =
    drawerActionItem &&
    actionItems.data?.find(actionItem => actionItem.id === drawerActionItem.id);

  return (
    <div
      className={styles.container}
      id="overview-panel"
      role="tabpanel"
      aria-labelledby="nav-tab-overview"
    >
      <div className={styles.topContainer}>
        <div className={styles.leftColumn}>
          {/** Top action items */}
          <div
            className={styles.topActionItems}
            aria-label={t("top-action-items")}
            data-testid="top-action-items"
          >
            <div className={styles.topActionItemsHeader}>
              <h3 className={styles.Header}>{t("top-action-items")}</h3>
              <div className={styles.buttons}>
                <button
                  className={classNames(styles.showAssignedButton, {
                    [styles.showAssignedSelected]: showAssignedOnly,
                  })}
                  onClick={() => setShowAssignedOnly(prev => !prev)}
                >
                  {showAssignedOnly && (
                    <Icon
                      iconName={"checkbox-checkmark-icon"}
                      altText={"assigned to me"}
                    />
                  )}
                  {t("show-assigned-to-me")}
                </button>
                <div className={styles.viewAllButton}>
                  <Button
                    id={"view-all-button"}
                    type={ButtonTypeEnum.tertiary}
                    size={ButtonSizeEnum.small}
                    label={tGlobal("view-all") + viewAllTotal}
                    ariaLabel={tGlobal("view-all")}
                    onClick={() =>
                      navigate(
                        `/client/${activeClient?.id}/project/${activeProject?.id}/action-items`
                      )
                    }
                  ></Button>
                </div>
              </div>
            </div>

            <div className={styles.topActionItemsBannerAndListContainer}>
              {/* Top action items banner */}
              {!actionItems.isBusy && (
                <PriorityActionItemBanner
                  id={`action-item-banner`}
                  onClick={() => {
                    if (!topActionItem) return;
                    setDrawerActionItem(topActionItem);
                  }}
                  onDragAndDrop={() => {}}
                  files={[]}
                  isVisible={showPriorityActionItemBanner}
                  actionItem={topActionItem}
                  setIsVisible={setShowPriorityActionItemBanner}
                  ariaLabel={tAccessibility("priority-action-item-ariaLabel")}
                  dataTestId="priority-action-item-banner"
                />
              )}

              {/* Top action items list */}
              {actionItems.isBusy || isActionItemsLoading ? (
                <ActionItemPlaceholder ariaLabel={tProject("action-items")} />
              ) : (
                <div className={styles.actionItemsContainer}>
                  {topActionItemsList.map(actionItem => (
                    <ActionItemEntry
                      key={actionItem.id}
                      label={actionItem.name}
                      dueDate={actionItem.dueDate}
                      assignees={actionItem.assignees}
                      onClick={() => {
                        setDrawerActionItem(actionItem);
                      }}
                      id={actionItem.id}
                      drawerActionItemId={drawerActionItem?.id}
                      actionItemType={actionItem.type}
                      flagged={actionItem.flagged}
                      status={actionItem.status}
                    />
                  ))}

                  {topActionItemsList.length === 0 && (
                    <EmptyState
                      emptyMessage={t("no-action-items")}
                      iconName="task-ltr-icon"
                    />
                  )}
                </div>
              )}
            </div>
          </div>

          {/** Project risks */}
          {/** Each time the modal visibility changes the trigger button disapears and the skeleton shows up,
           * to keep track of the focus we are putting it back to the wrapper element */}
          <div
            className={styles.projectRisksContainer}
            ref={riskWrapperRef}
            tabIndex={-1}
          >
            {risks?.isBusy && (
              <RiskPlaceholder
                title={t("project-risks")}
                eyebrow={t("project-risks-eyebrow")}
                dataTestId="project-risks-placeholder"
              />
            )}
            {!risks?.isBusy && (
              <>
                <div
                  className={styles.projectRisksHeader}
                  key={"project-risks-header"}
                >
                  <div className={styles.projectRisksTitleRow}>
                    <h3 className={styles.projectRisksTitle}>
                      {t("project-risks")}
                    </h3>
                    {!isClient && (
                      <div>
                        <button
                          aria-haspopup="dialog"
                          aria-expanded={showRiskModal}
                          aria-controls="risk-modal"
                          className={styles.addRisk}
                          onClick={() => setShowRiskModal(true)}
                        >
                          {t("add-risk")}
                          <Icon
                            iconName="add-icon"
                            altText=""
                            width={9}
                            height={9}
                          />
                        </button>
                      </div>
                    )}
                  </div>
                  {Array.isArray(risks?.data) && risks.data.length > 0 && (
                    <div className={styles.projectRisksSubTitle}>
                      {t("project-risks-eyebrow")}
                    </div>
                  )}
                </div>
                <Accordion.Root multiple className={styles.risksWrapper}>
                  {risks?.data?.map(projectRisk => (
                    <Accordion.Item
                      key={projectRisk.id}
                      value={`${projectRisk.id}`}
                      asChild
                    >
                      <ProjectRiskEntry
                        risk={projectRisk}
                        setActiveRisk={setActiveRisk}
                        setShowRiskModal={setShowRiskModal}
                        setShowRemoveRiskModal={setShowRemoveRiskModal}
                      />
                    </Accordion.Item>
                  ))}
                </Accordion.Root>
                {risks?.data?.length === 0 && (
                  <EmptyState
                    emptyMessage={t("no-risks")}
                    iconName="checkmark"
                  />
                )}
              </>
            )}
          </div>
        </div>

        {/** Progress and timeline */}
        <div className={styles.rightColumn}>
          {isProjectOverviewLoading && (
            <ProgressOverviewPlaceholder
              title={t("progress-completion")}
              role="progress-completion-placeholder"
              ariaLabel={t("progress-completion")}
              dataTestId="progress-completion-placeholder"
            />
          )}
          {!isProjectOverviewLoading && (
            <ProgressCompletion
              title={t("progress-completion")}
              percentage={percentage}
              updatedDateTime={getDateTimeLabel(
                formatISO(getTimezoneConvertedDate(progressionUpdatedDate)),
                i18n.language
              )}
              isClient={isClient}
              setPercentage={setPercentage}
              onClick={handleUpdateCompletionPercentage}
              dataTestId="progress-completion"
            />
          )}

          {isTimelineLoading && (
            <TimelinePlaceholder
              title={t("timeline")}
              ariaLabel={t("timeline")}
              dataTestId="timeline-placeholder"
            />
          )}
          {!isProjectOverviewLoading &&
            activeProject &&
            activeProject.stages &&
            activeProject.stages?.length > 0 &&
            !isTimelineLoading && (
              <ProjectTimeline
                title={t("timeline")}
                lastUpdated={
                  timelineUpdatedDate
                    ? getDateTimeLabel(
                        formatISO(timelineUpdatedDate),
                        i18n.language
                      )
                    : undefined
                }
                stages={stages!}
                isClient={isClient}
                isEditMode={() => {
                  setShowEditProjectTimelineModal(true);
                }}
                ariaLabel={t("timeline")}
                key={activeProject?.bgpId}
              />
            )}
        </div>
      </div>

      {actionItemToShow && (
        <ActionItemDrawer
          actionItemId={actionItemToShow.id}
          showDropdownItems={false}
          closeDrawerAction={() => {
            setDrawerActionItem(null);
          }}
          hideDocusignRedirectToast={
            recentlyDeclinedDocusignActionItem !== null
          }
        />
      )}

      <RiskModal
        id="risk-modal"
        risk={activeRisk}
        setShowRiskModal={setShowRiskModal}
        showRiskModal={showRiskModal}
        setActiveRisk={setActiveRisk}
        dataTestId="risk-modal"
        ariaLabel={tAccessibility("risk-modal-ariaLabel")}
        returnFocusRef={riskWrapperRef}
      />

      {/** Confirmation modal for removing a risk */}
      <Modal
        id="remove-risk-confirmation"
        title={tRisk("delete-risk")}
        subtitle={tRisk("delete-risk-confirmation")}
        isVisible={showRemoveRiskModal}
        hide={() => {
          setActiveRisk(undefined);
          setShowRemoveRiskModal(false);
        }}
        primaryBtnConfig={{
          label: tRisk("delete-risk"),
          type: ButtonTypeEnum.primary,
          onClick: () => handleDeleteRisk(),
          withRightIcon: false,
          disabled: risks?.isBusy,
        }}
        secondaryBtnConfig={{
          label: tGlobal("cancel"),
          type: ButtonTypeEnum.tertiary,
          onClick: () => {
            setActiveRisk(undefined);
            setShowRemoveRiskModal(false);
          },
          withRightIcon: false,
          disabled: risks?.isBusy,
        }}
        children={[]}
        allowOverflow
        includeHeaderBorder={false}
        size={ModalSize.MEDIUM}
        ariaLabel={tAccessibility("remove-risk-modal-ariaLabel")}
        dataTestId="remove-risk-modal"
      />
      <Modal
        id="welcome-project-modal"
        title={t("welcome-header")}
        subtitle={t("welcome-sub-header")}
        isVisible={showWelcomeModal}
        hide={() => {
          handleCloseModal();
        }}
        primaryBtnConfig={{
          label: t("invite-team"),
          type: ButtonTypeEnum.primary,
          onClick: () => {
            setIsInviteUsersModalOpen(true);
            fetchAllUsersForInvite();
            handleCloseModal();
          },
          withRightIcon: false,
        }}
        secondaryBtnConfig={{
          label: t("cancel"),
          type: ButtonTypeEnum.tertiary,
          onClick: () => {
            handleCloseModal();
          },
          withRightIcon: false,
        }}
        children={[]}
        allowOverflow
        includeHeaderBorder={false}
        size={ModalSize.MEDIUM}
      />
      <InviteUsersModal
        isOpen={isInviteUsersModalOpen}
        onModalClose={() => setIsInviteUsersModalOpen(false)}
        suggestedUsersList={suggestedTeamMemberList || []}
        onSendInvite={handleSendInvite}
        existingUsers={sortedTeamMembers}
      />

      <Modal
        id="configure-project-stages"
        title={t("configure-project-stages")}
        size={ModalSize.LARGE}
        isVisible={showEditProjectTimelineModal}
        hide={() => {
          setShowEditProjectTimelineModal(false);
          setShowValidationErrors(false);
        }}
        allowOverflow={true}
        isScrollable={false}
        primaryBtnConfig={{
          label: tGlobal("save"),
          onClick: () => {
            handleSaveChanges();
          },
          disabled: disableSaveStages,
          withRightIcon: false,
          type: ButtonTypeEnum.primary,
          size: ButtonSizeEnum.large,
        }}
        secondaryBtnConfig={{
          label: tGlobal("cancel"),
          onClick: () => {
            handleCancelChanges();
          },
          disabled: false,
          withRightIcon: false,
          type: ButtonTypeEnum.tertiary,
          size: ButtonSizeEnum.large,
        }}
        slottedContainerClassName={styles.slottedContainerProjectStages}
      >
        <div className={styles.slottedContainer}>
          <div className={styles.slottedContainer}>
            {editedStages.map(stage => {
              let substageOptions: { label: string; value: string }[] = [];
              if (stageTemplate && stageTemplate?.length > 0) {
                const targetStage = stageTemplate.find(template => {
                  return template.name == stage.title;
                });
                substageOptions =
                  targetStage?.substages.map(substage => {
                    return {
                      label: tProjectStages(substage.translationKey),
                      value: substage.translationKey,
                    };
                  }) ?? [];
              }
              return (
                <EditProjectStages
                  id={stage.id}
                  key={stage.title}
                  stageName={
                    stage.translationKey && stage.translationKey !== ""
                      ? tProjectStages(stage.translationKey)
                      : stage.title
                  }
                  subStages={stage?.substages || []}
                  startDate={stage.startDate}
                  endDate={stage.endDate}
                  isActive={stage.active}
                  status={stage.status}
                  setStages={setEditedStages}
                  onDeleteSubStage={handleDeleteSubStage}
                  onAddSubstage={handleAddSubStage}
                  onAddCustomSubstage={handleAddCustomSubStage}
                  substageOptions={substageOptions}
                  showValidationErrors={showValidationErrors}
                />
              );
            })}
          </div>
        </div>
        <div></div>
      </Modal>
    </div>
  );
};
