import {
  <PERSON><PERSON>,
  ButtonSizeEnum,
  ButtonType<PERSON>num,
  <PERSON>D<PERSON>down,
  ChipDropdown<PERSON>temType,
  Spinner,
  SuggestedUser,
} from "@bcp/uikit";

import styles from "./teamMembers.module.css";
import { useEffect, useState } from "react";
import { TeamMembersTable } from "./teamMemberList/TeamMemberTable";
import { InviteUsersModal } from "~/shell/inviteUsersModal/InviteUsersModal";
import { useTeamMembers } from "~/services/teamMembers/useTeamMembers";
import {
  MembersFilterType,
  SortConfig,
  SortDirection,
  SortKey,
  userTypeFilterItems,
  alphabaticFilterItems,
} from "./spec";
import useScreenSize from "~/utils/http/hooks/useScreenSize";
import { useProjectService } from "~/services/project";
import { defaultProps } from "~/default.spec";
import { useTranslation } from "react-i18next";
import { UserGroups, useRoleService } from "~/services/role";
import { UserData } from "~/services/teamMembers";

interface TeamMembersProps extends defaultProps {
  isPractioner?: boolean;
}

export const TeamMembers: React.FC<TeamMembersProps> = ({
  dataTestId = "app-team-members-screen",
  ariaDescribedBy,
  ariaLabel,
  ariaLabelledBy = "nav-tab-team",
  role = "tabpanel",
}) => {
  const { t } = useTranslation("teamMembers");
  const [filter, setFilter] = useState<MembersFilterType>(
    MembersFilterType.all
  );
  const [sortConfig, setSortConfig] = useState<{
    key: SortKey;
    direction: SortDirection;
  }>({
    key: "displayName",
    direction: "asc",
  });
  const [memberFilters, setMemberFilters] = useState<Set<MembersFilterType>>(
    new Set()
  );
  const [isInviteUsersModalOpen, setIsInviteUsersModalOpen] = useState(false);
  const [suggestedTeamMemberList, setSuggestedTeamMemberList] = useState<
    SuggestedUser[]
  >([]);
  const { isClient } = useRoleService();

  const {
    teamMembers,
    fetchTeamMembers,
    fetchMemberFirmUsers,
    fetchClientUsers,
    allUsers,
    clientUsers,
  } = useTeamMembers();

  const { isMobile } = useScreenSize();
  const { activeProject } = useProjectService();

  const handleSendInvite = async () => {
    setIsInviteUsersModalOpen(false);
    await fetchTeamMembers(activeProject?.bgpId as number);
    await fetchMemberFirmUsers();
    await fetchClientUsers();
  };

  useEffect(() => {
    if (
      !clientUsers.isBusy &&
      clientUsers.data !== undefined &&
      !allUsers.isBusy &&
      allUsers.data !== undefined
    ) {
      let suggestUserDataList: UserData[] = [];

      if (isClient) {
        // only grab client users if the current user is a client user
        suggestUserDataList =
          clientUsers.data?.filter(
            u => u.userGroupName === UserGroups.CLIENT_USER
          ) ?? [];
      } else {
        // exclude client admin users from suggested users
        suggestUserDataList = [
          ...(allUsers.data?.filter(
            u => u.userGroupName !== UserGroups.CLIENT_ADMIN_USER
          ) ?? []),
        ];
        // Add client users to the list of suggested users
        for (const item of clientUsers.data) {
          if (
            !suggestUserDataList.some(u => u.uniqueUserId === item.uniqueUserId)
          ) {
            suggestUserDataList.push(item);
          }
        }
      }

      if (suggestUserDataList.length > 0) {
        setSuggestedTeamMemberList(
          suggestUserDataList
            .filter(
              u => !teamMembers?.data?.some(t => t.bgpId === u.uniqueUserId)
            ) //filter out existing users
            .map((user, i) => {
              return {
                name: user.displayName,
                email: user.emailAddress,
                avatarUrl: "",
                uniqueUserId: user.uniqueUserId,
                userGroupName: user.userGroupName,
                id: i,
              };
            })
            .sort((a, b) => a.name.localeCompare(b.name))
        );
      }
    }
  }, [clientUsers, allUsers]);

  if (teamMembers.isBusy) {
    return (
      <div className={styles.container}>
        <div className={styles.spinnerContainer}>
          <Spinner
            isLoading
            background="rgba(0,0,0,0.4)"
            message="Getting your team members"
            size={"large"}
          />
        </div>
      </div>
    );
  }
  
  return (
    <div
      data-testid={dataTestId}
      aria-describedby={ariaDescribedBy}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
      role={role}
      className={styles.wrapper}
      id="team-panel"
    >
      <div className={styles.container}>
        <div className={styles.header}>
          <h3 className={styles.heading} data-testid={"app-team-members-title"}>
            {t("manage-team")}
          </h3>
          <Button
            id="invite-team-member-button"
            data-testid={"uikit-invite-team-members-button"}
            label={t("invite")}
            onClick={() => {
              setIsInviteUsersModalOpen(true);
              fetchMemberFirmUsers();
              fetchClientUsers();
            }}
            size={ButtonSizeEnum.small}
            type={ButtonTypeEnum.secondary}
            withRightIcon={false}
            withBorder={true}
            aria-haspopup="dialog"
            aria-expanded={isInviteUsersModalOpen}
            aria-controls="dialog:invite-users-modal:content"
          />
        </div>
        {isMobile && (
          <div className={styles.filters}>
            <ChipDropdown
              data-testid={"app-team-member-filter"}
              aria-label={"team-member-filter"}
              triggerLabel="All user types"
              itemType={ChipDropdownItemType.Checkbox}
              items={userTypeFilterItems}
              id="user type filter"
              includeSearchBar={false}
              includeReset={false}
              triggerIconName={null}
              onChange={item => {
                if (!item.checked) {
                  memberFilters.delete(item.value as MembersFilterType);
                } else {
                  memberFilters.add(item.value as MembersFilterType);
                }
                setMemberFilters(new Set(Array.from(memberFilters)));
                setFilter(
                  memberFilters.size === 0
                    ? MembersFilterType.all
                    : (item.value as MembersFilterType)
                );
              }}
            />
            <ChipDropdown
              data-testid={"app-team-member-sort"}
              aria-label={"team-member-sort"}
              triggerLabel="Sort"
              triggerIconName={null}
              itemType={ChipDropdownItemType.Radio}
              id="sort"
              includeSearchBar={false}
              includeReset={false}
              chevronIconName="arrow-sort-down-line"
              onChange={item => {
                const [newKey, newDirection] = item.value.split(" ");
                setSortConfig({
                  key: newKey,
                  direction: newDirection,
                } as SortConfig);
              }}
              items={alphabaticFilterItems}
            />
          </div>
        )}

        <div className={styles.content}>
          <TeamMembersTable
            users={teamMembers.data ?? []}
            isLoading={!!teamMembers?.isBusy}
            filter={filter}
            sortConfig={sortConfig}
            setFilter={setFilter}
            setSortConfig={setSortConfig}
          />
        </div>
      </div>
      <InviteUsersModal
        isOpen={isInviteUsersModalOpen}
        onModalClose={() => setIsInviteUsersModalOpen(false)}
        suggestedUsersList={suggestedTeamMemberList || []}
        onSendInvite={handleSendInvite}
        existingUsers={teamMembers.data ?? []}
      />
    </div>
  );
};
