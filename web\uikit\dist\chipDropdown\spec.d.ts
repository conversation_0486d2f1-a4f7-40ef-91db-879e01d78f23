import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
export interface ChipDropdownProps extends defaultProps {
    triggerLabel: string;
    triggerLabelSecondary?: string;
    triggerIconName?: IconName;
    triggerIconSize?: number;
    itemType: ChipDropdownItemType;
    items: ChipDropdownItem[];
    defaultOpen?: boolean;
    id: string;
    defaultSelections?: ChipDropdownItem[];
    onChange: (item: ChipDropdownItem) => void;
    includeSearchBar: boolean;
    searchbarPlaceholder?: string;
    onReset?: () => void;
    chevronIconName?: IconName;
    includeReset?: boolean;
    onResetTrigger?: boolean;
    selected?: Set<ChipDropdownItem>;
    menuLabel?: string;
}
export interface ChipDropdownItem {
    id: string;
    label: string;
    value: string;
    onClick?: (value: string) => void;
    checked?: boolean;
    includeAvatar?: boolean;
    firstInitial?: boolean;
    avatarUrl?: string;
}
export declare enum ChipDropdownItemType {
    Radio = 0,
    Checkbox = 1
}
