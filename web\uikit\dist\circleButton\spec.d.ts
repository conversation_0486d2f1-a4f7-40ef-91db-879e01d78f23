import { defaultProps } from "~/default.spec";
import { ReactElement } from "react";
export interface CircleButtonProps extends defaultProps {
    toolTipText?: string;
    inputId: string;
    icon?: ReactElement;
    onClick?: () => void;
    ariaLabel?: string;
    withArrow?: boolean;
    smallTooltip?: boolean;
    toolTipCloserToButton?: boolean;
    withBorder?: boolean;
    size?: CircleButtonIconSizeEnum;
    disabled?: boolean;
    isComment?: boolean;
    className?: string;
}
export declare enum CircleButtonIconSizeEnum {
    small = "small",
    regular = "regular"
}
