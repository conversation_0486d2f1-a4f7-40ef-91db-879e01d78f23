import {
  DatePickerDate,
  DocumentUploadFile,
  Drawer,
  DrawerSizeEnum,
  Modal,
  ModalSize,
  ButtonTypeEnum,
  ButtonSizeEnum,
  FooterButtonAlignment,
  Spinner,
  useFocusManager,
  useClickOutside,
} from "@bcp/uikit";
import { useState, useCallback, useRef, useEffect } from "react";
import _ from "lodash";
import styles from "./createActionItem.module.css";
import sharedStyles from "../shared/shared.module.css";
import { CreateActionItemForm } from "./createActionItemForm/CreateActionItemForm";
import { useUserService } from "~/services/user";
import {
  ActionItem,
  ActionItemType,
  ICreateActionItemDetails,
  ICreateDocumentActionItemBody,
  ICreateSignatureActionItemBody,
  SignerOptionEnum,
} from "~/services/action-items/spec";
import { useActionItemsService } from "~/services/action-items/useActionItemsService";
import { ActionItemFormType, SelectedAssignee } from "../shared/spec";
import { useProjectService } from "~/services/project";
import { useClientService } from "~/services/client";
import { FolderNode } from "../shared/assignFolderModal/spec";
import { AssignFolderModal } from "../shared/assignFolderModal/AssignFolderModal";
import { InformationBanner } from "./informationBanner/InformationBanner";
import { useSettingsService } from "~/services/settings";
import { useToastService } from "~/services/toast";
import { useTranslation } from "react-i18next";
import {
  isActionItemDocumentRequest,
  isActionItemSignatureRequest,
  validateActionItemTitle,
} from "~/utils/actionItems/actionItemUtils";
import { useDocusign } from "~/services/action-items/docusign/useDocusign";
import unsavedChangesModalStyles from "../actionItemDrawer/unsavedChangesModal.module.css";
import { ErrorMessage } from "~/services/error/spec";

interface CreateActionItemProps {
  isOpen: boolean;
  duplicateDataId?: string | null;
  setIsOpen: (value: boolean) => void;
  type: ActionItemFormType | string;
}

const focusInvalidFieldID = (ref: HTMLDivElement | null, id: string) => {
  ref?.querySelector<HTMLElement>(`#${CSS.escape(id)}`)?.focus();
};

export const CreateActionItem: React.FC<CreateActionItemProps> = ({
  isOpen,
  setIsOpen,
  duplicateDataId = null,
  type,
}) => {
  const { t } = useTranslation("actionItems");
  const { t: tDocuments } = useTranslation("documents");
  const { t: tGlobal } = useTranslation("global");
  const { t: tSettings } = useTranslation("settings");
  const { activeProject, spoSite } = useProjectService();
  const { activeClient } = useClientService();
  const { profile } = useUserService();
  const { getActionItem } = useActionItemsService();

  const isDocumentRequest = isActionItemDocumentRequest(type);
  const isSignatureRequest = isActionItemSignatureRequest(type);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [duplicateData, setDuplicateData] = useState<ActionItem | null>();

  const [selectedAssignees, setSelectedAssignees] = useState<
    SelectedAssignee[]
  >(duplicateData?.assignees ?? []);

  const [isPriority, setIsPriority] = useState(duplicateData?.flagged ?? false);
  const [targetFolder, setTargetFolder] = useState<FolderNode | null>(null);
  const [showAssignFolderModal, setShowAssignFolderModal] = useState(false);
  const [dueDate, setDueDate] = useState<Date | string>();
  const [instructions, setInstructions] = useState<string>("");
  const [title, setTitle] = useState<string>("");
  const [signatureDocuments, setSignatureDocuments] = useState<
    DocumentUploadFile[]
  >([]);
  const [signerOption, setSignerOption] = useState(SignerOptionEnum.DocuSign);
  const [formErrors, setFormErrors] = useState<Record<string, string>>();
  const [showFormRequiredFields, setShowFormRequiredFields] = useState(false);
  const [fileDestinationFetchError, setFileDestinationFetchError] = useState(
    false
  );
  const [isDuplicateDetailsLoading, setIsDuplicateDetailsLoading] = useState<
    boolean
  >(duplicateDataId != null); // if fetching duplicate data, need to trigger loader
  const [enableSigningOrder, setEnableSigningOrder] = useState(
    duplicateData?.signingOrderEnabled ?? false
  );
  const [showUnsavedChangesModal, setShowUnsavedChangesModal] = useState(false);
  const [pendingCloseAction, setPendingCloseAction] = useState<
    (() => void) | null
  >(null);

  const wrapperRef = useRef<HTMLDivElement>(null);

  const { showToast } = useToastService();
  const focusManager = useFocusManager();

  const {
    createSignatureActionItem,
    createDocumentActionItem,
    actionItems,
    fetchActionItems,
  } = useActionItemsService();
  const { settings } = useSettingsService();
  const [docUploadKey, setDocUploadKey] = useState<number>(0);
  const [envelopeId, setEnvelopeId] = useState<string | null>(null);
  const { updateSigners } = useDocusign();

  // Helper function to get initial state for comparison
  const getInitialState = useCallback(
    () => ({
      title: "",
      instructions: "",
      assignees: [],
      files: [],
      targetFolder: null, // Will be conditionally checked based on isSignatureRequest
      dueDate: "",
      isPriority: false,
    }),
    []
  );

  // Helper function to get current state for comparison
  const getCurrentState = useCallback(
    () => ({
      title: title.trim(),
      instructions: instructions.trim(),
      assignees: selectedAssignees,
      files: signatureDocuments,
      targetFolder: isSignatureRequest ? null : targetFolder, // Ignore targetFolder for Signature Requests
      dueDate: dueDate || "",
      isPriority: isPriority,
    }),
    [
      title,
      instructions,
      selectedAssignees,
      signatureDocuments,
      targetFolder,
      dueDate,
      isPriority,
      isSignatureRequest,
    ]
  );

  // Function to check if there are unsaved changes using deep comparison
  const hasUnsavedChanges = useCallback(() => {
    const initialState = getInitialState();
    const currentState = getCurrentState();
    return !_.isEqual(initialState, currentState);
  }, [getInitialState, getCurrentState]);

  const getActionItemDetails = async (
    clientId: number,
    projectId: number,
    actionItemId: string
  ) => {
    setIsDuplicateDetailsLoading(true);
    try {
      const actionItem = await getActionItem(actionItemId, clientId, projectId);
      setDuplicateData(actionItem);
      setTitle(
        actionItem?.name ? t("copy-of", { name: actionItem?.name }) : ""
      );
      setIsPriority(actionItem.flagged);
      setInstructions(actionItem?.description ?? "");
      const targetFolderNode = new FolderNode(
        null,
        actionItem.fileDestination?.name,
        actionItem.fileDestination,
        actionItem.fileDestination?.parentUrl,
        actionItem.fileDestination?.displayName
      );
      setTargetFolder(targetFolderNode);
      setSelectedAssignees(actionItem.assignees);
    } catch {
      setDuplicateData(null);
    }
    setIsDuplicateDetailsLoading(false);
  };

  useEffect(() => {
    if (
      !activeClient ||
      !activeClient.bgpId ||
      !activeProject ||
      !activeProject.bgpId ||
      !duplicateDataId
    ) {
      setDuplicateData(null);
      return;
    }

    getActionItemDetails(
      activeClient.bgpId,
      activeProject.bgpId,
      duplicateDataId
    );
  }, [activeClient, activeProject, duplicateDataId]);

  const folderFileDestination = fileDestinationFetchError
    ? undefined
    : duplicateData?.fileDestination;

  const showGlobalErrorToast = () =>
    showToast({
      type: "error",
      message: tGlobal("error"),
      persist: false,
    });

  const resetForm = useCallback(() => {
    setTitle("");
    setDueDate("");
    setSelectedAssignees([]);
    setIsPriority(false);
    setTargetFolder(null);
    setSignerOption(SignerOptionEnum.DocuSign);
    setInstructions("");
    setSignatureDocuments([]);
    setDocUploadKey(prev => prev + 1);
  }, []);

  // Function to handle drawer close with unsaved changes check
  const handleCloseDrawer = useCallback(
    (closeAction: () => void) => {
      if (hasUnsavedChanges()) {
        setPendingCloseAction(() => closeAction);
        setShowUnsavedChangesModal(true);
      } else {
        closeAction();
      }
    },
    [hasUnsavedChanges]
  );

  // Function to confirm closing without saving
  const handleConfirmClose = useCallback(() => {
    setShowUnsavedChangesModal(false);
    if (pendingCloseAction) {
      pendingCloseAction();
      setPendingCloseAction(null);
    }
  }, [pendingCloseAction]);

  // Function to cancel closing and continue editing
  const handleCancelClose = useCallback(() => {
    setShowUnsavedChangesModal(false);
    setPendingCloseAction(null);
  }, []);

  // Function to handle click outside with proper checks
  const handleClickOutsideDrawer = useCallback(() => {
    if (!isOpen) return;

    const currentHasUnsavedChanges = hasUnsavedChanges();

    if (currentHasUnsavedChanges && !showUnsavedChangesModal) {
      // Show confirmation modal when there are unsaved changes
      setPendingCloseAction(() => () => {
        setIsOpen(false);
        resetForm();
      });
      setShowUnsavedChangesModal(true);
    } else if (!currentHasUnsavedChanges && !showUnsavedChangesModal) {
      // Close directly when no unsaved changes
      setIsOpen(false);
      resetForm();
    }
  }, [isOpen, hasUnsavedChanges, showUnsavedChangesModal, resetForm]);

  // Custom click outside handler for drawer
  const drawerRef = useClickOutside<HTMLDivElement>({
    onClickOutside: handleClickOutsideDrawer,
    isVisible: isOpen && !showUnsavedChangesModal,
    ignoreSelector: [
      "#assign-folder-modal",
      "#unsaved-changes-confirmation-modal",
      '[data-scope="dialog"][data-part="content"]',
      '[data-scope="dialog"][data-part="positioner"]',
      '[data-scope="date-picker"][data-part="positioner"]',
      '[data-scope="date-picker"][data-part="content"]',
      '[data-scope="dropdown"][data-part="positioner"]',
      '[data-scope="dropdown"][data-part="content"]',
    ],
  });

  const handleDueDateChange = (value: DatePickerDate) => {
    setDueDate(
      new Date(Date.UTC(value.year, value.month - 1, value.day, 12, 0, 0))
    );
  };

  const handleChangeAssignees = (assignees: SelectedAssignee[]) => {
    setSelectedAssignees(assignees);

    if (assignees.length < 2) {
      setEnableSigningOrder(false);
    }
  };

  const isFileDestinationValid = (targetFolder: FolderNode | null) => {
    return (
      targetFolder &&
      targetFolder.node &&
      targetFolder.node.name &&
      targetFolder.node.driveItemId &&
      targetFolder.node.url
    );
  };

  const validateDocumentRequest = () => {
    const errors: Record<string, string> = {};

    const titleError = validateActionItemTitle(title);
    if (titleError) {
      errors.title = t(titleError);
    }

    if (!isFileDestinationValid(targetFolder)) {
      errors.destination = tGlobal("required-field");
    }

    return errors;
  };

  const validateSignatureRequest = () => {
    const errors: Record<string, string> = {};

    const titleError = validateActionItemTitle(title);
    if (titleError) {
      errors.title = t(titleError);
    }

    if (selectedAssignees.length === 0) {
      errors.selectedUsers = tGlobal("required-field");
    }

    if (signatureDocuments.length === 0) {
      errors.signatureDocuments = tGlobal("required-field");
    }

    if (!isFileDestinationValid(targetFolder)) {
      errors.destination = tGlobal("required-field");
    }

    if (!envelopeId) {
      errors.docusign = tGlobal("required-field");
    }

    return errors;
  };

  // TODO: clean up/break out this function for readability
  const createActionItemHandler = async () => {
    let errors: Record<string, string> = {};

    if (isDocumentRequest) {
      errors = validateDocumentRequest();

      if (errors.title) {
        focusInvalidFieldID(wrapperRef.current, "create-action-title-drawer");
      } else if (errors.destination) {
        focusInvalidFieldID(
          wrapperRef.current,
          "create-action-document-file-trigger"
        );
      }
    }

    if (isSignatureRequest) {
      errors = validateSignatureRequest();

      if (errors.title) {
        focusInvalidFieldID(wrapperRef.current, "create-action-title-drawer");
      } else if (errors.selectedUsers) {
        focusInvalidFieldID(
          wrapperRef.current,
          "search-dropdown-trigger-action-item-create-assignees-dropdown"
        );
      } else if (errors.destination) {
        focusInvalidFieldID(
          wrapperRef.current,
          "create-action-assign-folder-trigger"
        );
      } else if (errors.signatureDocuments) {
        focusInvalidFieldID(
          wrapperRef.current,
          "file:action-item-documents:dropzone"
        );
      }
    }

    if (Object.keys(errors).length) {
      setShowFormRequiredFields(true);
      setFormErrors(errors);
      return;
    }

    if (!activeProject || !activeClient || !activeClient.bgpId) return;
    if (!profile?.data?.bgpId) return;
    if (!(targetFolder && targetFolder.node)) return;
    if (!settings?.data) return;
    if (!spoSite) return;

    setIsLoading(true);
    let createdActionItem;

    const actionItemDetails: ICreateActionItemDetails = {
      projectId: activeProject.bgpId,
      clientId: activeClient.bgpId,
      type: isSignatureRequest
        ? ActionItemType.Signature
        : ActionItemType.RequestItem,
      ownerUniqueUserId: profile.data.bgpId,
      name: title.trim(),
      description: instructions.trim(),
      assigneeIds: selectedAssignees.map(assignee => assignee.user.bgpId),
      fileDestination: {
        driveItemId: targetFolder.node.driveItemId,
        name: targetFolder.node.name,
        serverRelativeUrl: targetFolder.node.url,
      },
    };

    if (dueDate) {
      actionItemDetails.dueDate = dueDate as Date;
    }

    if (isSignatureRequest) {
      if (!envelopeId) return;

      const createSignatureRequestBody: ICreateSignatureActionItemBody = {
        signingDocument: signatureDocuments[0], // currently support only one signature document per action item, subject to change post MVP
        isPriority: isPriority,
        enableSigningOrder: enableSigningOrder,
        actionItemDetails: actionItemDetails,
        envelopeId: envelopeId,
      };

      try {
        await updateSigners(
          envelopeId,
          actionItemDetails.assigneeIds,
          enableSigningOrder
        );

        createdActionItem = await createSignatureActionItem(
          createSignatureRequestBody
        );

        if (!createdActionItem) {
          showGlobalErrorToast();
          setIsLoading(false);
          return;
        }
      } catch (error) {
        if (error instanceof Error) {
          const isFileExistsError =
            error &&
            typeof error === "object" &&
            error.message === ErrorMessage.FileAlreadyExists;
          const fileName = signatureDocuments[0].file.name;

          if (isFileExistsError) {
            showToast({
              type: "error",
              title: tDocuments("failed-to-upload", {
                fileName: fileName,
              }),
              message: tDocuments("file-already-exists"),
              persist: false,
              errorMessageWithLineBreaks: true,
              replaceLineBreaksWithBulletPoints: true,
            });
            setIsLoading(false);
            return;
          }
        }
        showGlobalErrorToast();
        setIsLoading(false);
        return;
      }
    }

    if (isDocumentRequest) {
      const createDocumentRequestBody: ICreateDocumentActionItemBody = {
        isPriority: isPriority,
        actionItemDetails: actionItemDetails,
      };

      try {
        createdActionItem = await createDocumentActionItem(
          createDocumentRequestBody
        );

        if (!createdActionItem) {
          showGlobalErrorToast();
          setIsLoading(false);
          return;
        }
      } catch {
        showGlobalErrorToast();
        setIsLoading(false);
        return;
      }
    }

    if (
      createdActionItem &&
      actionItemDetails.assigneeIds.length !==
        createdActionItem.assignees.length
    ) {
      showToast({
        type: "info",
        message: "Some users could not be added",
        persist: false,
      });
    }

    setIsLoading(false);
    showToast({
      type: "success",
      message: isSignatureRequest
        ? t("created-signature-request-success")
        : t("created-document-request-success"),
      persist: false,
    });
    resetForm();
    setIsOpen(false);
    await fetchActionItems(activeProject.bgpId);
  };

  const formIsPending =
    actionItems.isCreating || actionItems.isUploading || isLoading;

  return (
    <div ref={drawerRef}>
      <Drawer
        ariaLabel={
          isSignatureRequest
            ? t("create-signature-request")
            : t("create-document-request")
        }
        key="create-action-item"
        primaryBtnConfig={{
          text: t("create"),
          type: "primary",
          onClick: createActionItemHandler,
          disabled: isLoading,
        }}
        secondaryBtnConfig={{
          text: tGlobal("cancel"),
          type: "secondary",
          disabled: isLoading,
          onClick: () => {
            if (!isLoading) {
              handleCloseDrawer(() => {
                setIsOpen(false);
                resetForm();
              });
            }
          },
        }}
        id="create-action-item"
        hasOverlay={false}
        isVisible={isOpen}
        closeDrawer={() => {
          if (!isLoading) {
            handleCloseDrawer(() => {
              setIsOpen(false);
              resetForm();
            });
          }
        }}
        headerConfig={{
          title: isSignatureRequest
            ? t("create-signature-request")
            : t("create-document-request"),
          component: isSignatureRequest && (
            <div className={sharedStyles.informationBanner}>
              <InformationBanner
                message={t("signature-request-information-banner")}
              />
            </div>
          ),
        }}
        size={DrawerSizeEnum.Large}
        closeOnClickOutside={false}
      >
        {isDuplicateDetailsLoading ? (
          <div className={styles.createActionItemFormSpinner}>
            <Spinner isLoading />
          </div>
        ) : (
          <div className={styles.createActionItemFormWrapper}>
            <CreateActionItemForm
              docUploadKey={docUploadKey}
              isOpen={isOpen}
              type={type as ActionItemFormType}
              date={dueDate ? new Date(dueDate).toISOString() : null}
              selectedAssignees={selectedAssignees}
              isPriority={isPriority}
              targetFolder={targetFolder}
              fileDestinationFetchError={fileDestinationFetchError}
              duplicateDataTargetFolder={folderFileDestination}
              signerOption={signerOption}
              instructions={instructions}
              files={signatureDocuments}
              title={title}
              setTitle={setTitle}
              setSelectedAssignees={handleChangeAssignees}
              setIsPriority={setIsPriority}
              setSignerOption={setSignerOption}
              setInstructions={setInstructions}
              setFiles={setSignatureDocuments}
              setDueDate={handleDueDateChange}
              showFormRequiredFields={showFormRequiredFields}
              formErrors={formErrors}
              openAssignFolderModal={() => {
                setShowAssignFolderModal(true);
              }}
              envelopeId={envelopeId}
              onChangeEnvelopeId={envelopeId => setEnvelopeId(envelopeId)}
              enableSigningOrder={enableSigningOrder}
              onChangeEnableSigningOrder={enableSigningOrder =>
                setEnableSigningOrder(enableSigningOrder)
              }
            />
            {formIsPending && (
              <div className={styles.createActionItemFormSpinner}>
                <Spinner isLoading />
              </div>
            )}
          </div>
        )}
      </Drawer>
      <AssignFolderModal
        isOpen={showAssignFolderModal}
        setIsOpen={setShowAssignFolderModal}
        onFolderChange={(folder: FolderNode | null) => {
          setTargetFolder(folder);
          // if no folder selected but is been set currently needs to focus the new item.
          if (!targetFolder && folder) {
            focusManager.focus();
          }
        }}
        targetFolder={targetFolder}
        duplicateDataFolderDestination={folderFileDestination}
        setFileDestinationFetchError={setFileDestinationFetchError}
        type={type as ActionItemFormType}
      />

      {/* Unsaved Changes Confirmation Modal */}
      <Modal
        id="unsaved-changes-confirmation-modal"
        title={tSettings("cancel-title")}
        isVisible={showUnsavedChangesModal}
        hide={() => {
          // Only allow closing via X button when not saving
          if (!isLoading) {
            handleCancelClose();
          }
        }}
        size={ModalSize.SMALL}
        allowOverflow={false}
        includeHeaderBorder={false}
        footerBtnAlignment={FooterButtonAlignment.END}
        closeButton={!isLoading}
        closeOnInteractOutside={false}
        primaryBtnConfig={{
          label: tSettings("cancel-confirm"),
          onClick: handleConfirmClose,
          type: ButtonTypeEnum.primary,
          size: ButtonSizeEnum.large,
          withRightIcon: false,
          disabled: isLoading,
        }}
        secondaryBtnConfig={{
          label: tSettings("cancel-keep-editing"),
          onClick: () => {
            if (!isLoading) {
              handleCancelClose();
            }
          }, 
          type: ButtonTypeEnum.tertiary,
          size: ButtonSizeEnum.large,
          withRightIcon: false,
          disabled: isLoading,
        }}
      >
        <div className={unsavedChangesModalStyles.modalContent}>
          {tSettings("cancel-description")}
        </div>
      </Modal>
    </div>
  );
};
