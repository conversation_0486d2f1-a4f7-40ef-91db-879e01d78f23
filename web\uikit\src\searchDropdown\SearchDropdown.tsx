import React, { useMemo, useState } from "react";
import { isValidEmail } from "~/utils/emailUtil";
import styles from "./searchDropdown.module.css";
import { SearchDropdownProps, SelectedUser } from "./spec";
import { InviteUserCard } from "~/inviteUserCard";
import { Chip } from "~/chip";
import { ChipType } from "~/chip/spec";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import { Field, Combobox, useCombobox } from "@ark-ui/react";
import { Icon } from "~/icon";
import { useFocusedBy } from "~/utils/accessibility";

export const SearchDropdown: React.FC<SearchDropdownProps> = ({
  suggestedUsers,
  onUserAdd,
  onUserRemove,
  selectedUsers,
  readOnlyUserIds,
  disabled = false,
  dataTestId = "uikit-sectionDropdown",
  ariaLabel,
  ariaDescribedBy,
  ariaLabelledBy,
  defaultLabel,
  useMaxWidth = true,
  isRequired = false,
  showFormRequiredFields,
  showName = true,
  showEmail = true,
  allowUnverifiedUsers = true,
  maxAssignees,
  maxResults = 100,
  id,
  className,
  errorOnBlur = true,
}) => {
  const { t: tGlobal } = useTranslation("global");
  const { t: tActionItems } = useTranslation("actionItems");
  const [inputValue, setInputValue] = useState("");
  const [emptyInputError, setEmptyInputError] = useState(false);
  const { focusClass } = useFocusedBy();
  const inputHasUsers = selectedUsers.length > 0;
  const maxSelectedUsersReached = selectedUsers.length === maxAssignees;
  const noUsersSelectedForRequiredField =
    showFormRequiredFields && selectedUsers.length === 0;

  const handleUserSelect = (email: string) => {
    const suggestedUser = suggestedUsers.find(
      suggestedUser => suggestedUser.email === email
    );

    const selectedUser: SelectedUser = {
      email,
      displayText: suggestedUser?.name?.replace(" ", "") // If it's not just a space
        ? suggestedUser.name
        : email,
      uniqueUserId: suggestedUser?.uniqueUserId,
      chipType: suggestedUser
        ? ChipType.DEFAULT
        : isValidEmail(email)
        ? ChipType.WARNING
        : ChipType.ERROR,
      avatarColor: "",
      userGroupName: suggestedUser?.userGroupName,
    };

    onUserAdd(selectedUser);
    setInputValue("");
  };

  const filteredUsers = useMemo(
    () =>
      suggestedUsers
        .filter(
          user =>
            (user.name?.toLowerCase().includes(inputValue.toLowerCase()) ||
              user.email.toLowerCase().includes(inputValue.toLowerCase())) &&
            selectedUsers.findIndex(
              selectedUser => selectedUser.uniqueUserId === user.uniqueUserId
            ) === -1 // NOTE: needed as BCP/BGP names aren't synched, allows for already for selected user with mismatch name to display twice, remove when BCP and BGP names are synched
        )
        .slice(0, maxResults),
    [inputValue, suggestedUsers, selectedUsers]
  );

  const showNewUserOption =
    inputValue && filteredUsers.length === 0 && allowUnverifiedUsers;

  const combobox = useCombobox({
    id,
    allowCustomValue: allowUnverifiedUsers,
    items: filteredUsers.map(u => u.email),
    multiple: true,
    closeOnSelect: true,
    openOnClick: true,
    onValueChange: ({ value }: Combobox.ValueChangeDetails<string>) => {
      handleUserSelect(value[value.length - 1]);
    },
    value: selectedUsers.map(u => u.email),
    disabled: disabled || maxSelectedUsersReached,
    positioning: {
      gutter: 0,
    },
    loopFocus: true,
    inputValue,
    onInputValueChange: ({ inputValue }: Combobox.InputValueChangeDetails) => {
      setInputValue(inputValue);
    },
    selectionBehavior: "preserve",
    inputBehavior: "autohighlight",
    highlightedValue: showNewUserOption ? `${inputValue}` : undefined,
  });

  const isNotReadOnlyUser = (id: string | undefined) => {
    return (readOnlyUserIds ?? []).indexOf(id ?? "") === -1;
  };

  return (
    <Field.Root
      className={styles.fieldRoot}
      invalid={
        emptyInputError ||
        noUsersSelectedForRequiredField ||
        maxSelectedUsersReached
      }
    >
      <Combobox.RootProvider value={combobox}>
        <Combobox.Control
          data-testid={dataTestId}
          className={classNames(styles.userSelectInput, className, focusClass, {
            [styles.useMaxWidth]: useMaxWidth,
            [styles.userSelectInputError]:
              emptyInputError ||
              noUsersSelectedForRequiredField ||
              maxSelectedUsersReached,
          })}
        >
          <div className={styles.selectedUsers}>
            {selectedUsers.map(user => (
              <Chip
                key={user.uniqueUserId ?? user.email}
                text={user.displayText}
                onDismissBtnClick={
                  isNotReadOnlyUser(user.id)
                    ? () => {
                        onUserRemove(user);
                      }
                    : undefined
                }
                type={user.chipType}
                iconName={
                  user.chipType == ChipType.WARNING
                    ? "person-warning-icon"
                    : user.chipType == ChipType.ERROR
                    ? "error-circle"
                    : undefined
                }
                iconHeight={user.chipType == ChipType.ERROR ? 20 : undefined}
                iconWidth={user.chipType == ChipType.ERROR ? 20 : undefined}
              />
            ))}
            <div className={styles.label}>
              <Combobox.Input
                aria-label={ariaLabel}
                aria-describedby={ariaDescribedBy}
                aria-labelledby={ariaLabelledBy}
                onFocus={() => setEmptyInputError(false)}
                onBlur={() => {
                  if (errorOnBlur && isRequired && selectedUsers.length === 0) {
                    setEmptyInputError(true);
                  }
                }}
                autoComplete="off"
                className={styles.input}
                placeholder={
                  !inputHasUsers
                    ? defaultLabel
                      ? defaultLabel
                      : tGlobal("search-dropdown-placeholder")
                    : ""
                }
              />
            </div>
          </div>
        </Combobox.Control>
        <Combobox.Positioner>
          <Combobox.Content className={styles.dropdown}>
            {filteredUsers.map(user => (
              <Combobox.Item
                key={user.uniqueUserId}
                item={user.email}
                className={styles.dropdownItem}
              >
                <Combobox.ItemText>
                  <InviteUserCard
                    name={
                      showName
                        ? user.name.replace(" ", "") // If name isn't just a blank space
                          ? user.name
                          : user.email
                        : ""
                    } // Fallback to email as the display name if user does not contain display name
                    email={showEmail ? user.email : ""}
                    avatarColor={user.avatarColor}
                  />
                </Combobox.ItemText>
                <Combobox.ItemIndicator />
              </Combobox.Item>
            ))}
            {showNewUserOption && (
              <Combobox.Item
                item={inputValue}
                className={styles.dropdownItem}
                id={inputValue}
              >
                <Combobox.ItemText>
                  <InviteUserCard
                    email={combobox.inputValue.toLowerCase()}
                    isNewUser
                  />
                </Combobox.ItemText>
              </Combobox.Item>
            )}
            {!showNewUserOption &&
              filteredUsers.length === 0 &&
              suggestedUsers.length > 0 && (
                <div id="no-results" className={styles.noResults}>
                  {tGlobal("no-results-found")}
                </div>
              )}
          </Combobox.Content>
        </Combobox.Positioner>
      </Combobox.RootProvider>
      <Field.ErrorText>
        <div className={styles.errorContainer}>
          <div className={styles.errorTextContainer}>
            <Icon iconName="error-circle" size={20} />
            <p className={styles.errorText}>
              {(noUsersSelectedForRequiredField || emptyInputError) &&
                tGlobal("required-field")}
              {maxSelectedUsersReached &&
                tActionItems("max-selected-users-text", { maxAssignees })}
            </p>
          </div>
        </div>
      </Field.ErrorText>
    </Field.Root>
  );
};
