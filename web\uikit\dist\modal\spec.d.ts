import { ReactNode, RefObject } from "react";
import { ButtonProps } from "~/button";
import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
export interface ModalProps extends defaultProps {
    id?: string;
    title?: string;
    subtitle?: string;
    informationText?: string;
    informationTextType?: InformationTextType;
    size?: ModalSize;
    includeHeaderBorder?: boolean;
    isVisible?: boolean;
    hide: () => void;
    primaryBtnConfig?: ButtonProps;
    secondaryBtnConfig?: ButtonProps;
    footerBtnAlignment?: FooterButtonAlignment;
    slottedContainerRef?: React.RefObject<HTMLDivElement>;
    children?: ReactNode;
    allowOverflow: boolean;
    isScrollable?: boolean;
    closeButton?: boolean;
    manageAccess?: boolean;
    addFooterButton?: boolean;
    addFooterButtonMethod?: () => void;
    minHeight?: string;
    isClient?: boolean;
    truncateTitle?: boolean;
    customHeaderContent?: ReactNode;
    returnFocusRef?: RefObject<HTMLElement>;
    slottedContainerClassName?: string;
    wrapperClassName?: string;
    closeOnInteractOutside?: boolean;
}
export interface FileDocModalProps {
    id: string;
    sharedWith: string;
    locationFileName: string;
    lastModifiedDate: string;
    lastModifiedBy: string;
    uploadedDate?: string;
    uploadedBy?: string;
    manageAccess?: boolean;
    defaultOpen?: boolean;
    typeOfModal: InfoModalType;
    onManageAccess: () => void;
}
export interface MiniModalSectionProps {
    header: string;
    inputDate?: string;
    personName?: string;
    chipName?: string;
    chipIcon?: IconName;
    chipSection?: boolean;
}
export declare enum FooterButtonAlignment {
    START = "start",
    END = "end",
    BETWEEN = "between"
}
export declare enum ModalSize {
    SMALL = "Small",
    MEDIUM = "Medium",
    LARGE = "Large",
    MINI = "Mini"
}
export declare enum InformationTextType {
    INFO = "info",
    ERROR = "error"
}
export declare enum InfoModalType {
    DOCUMENT = "Document",
    FOLDER = "Folder"
}
