import { defaultProps } from "~/default.spec";
import { IconName } from "../icon";
export interface DropdownMenuItem extends defaultProps {
    label?: string;
    value: string | number | boolean;
    isCritical?: boolean;
    criticalHandler?: (target?: HTMLElement) => void;
    iconName?: IconName;
    text?: string;
    labelSelection?: string;
    isDisabled?: boolean;
}
export interface DropdownMenuProps extends defaultProps {
    id: string;
    items: DropdownMenuItem[];
    onChange: (item: DropdownMenuItem) => void;
    defaultOpen?: boolean;
    closeOnSelect?: boolean;
    iconName?: IconName;
    triggerClassName?: string;
}
