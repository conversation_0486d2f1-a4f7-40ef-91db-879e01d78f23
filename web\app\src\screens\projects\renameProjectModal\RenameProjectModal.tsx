import {
  ButtonSizeEnum,
  ButtonTypeEnum,
  Input,
  Modal,
  ModalSize,
} from "@bcp/uikit";
import styles from "./renameProjectModal.module.css";
import { useEffect, useState } from "react";
import { IProject } from "~/services/project";
import { useProjectService } from "~/services/project";
import { useTranslation } from "react-i18next";
import { useClientService } from "~/services/client";

interface RenameProjectModalProps {
  project: IProject | null;
  setActiveProject: (project: IProject | null) => void;
  showRenameProjectModal: boolean;
  hideModal: () => void;
}

export const RenameProjectModal: React.FC<RenameProjectModalProps> = ({
  project,
  showRenameProjectModal,
  hideModal,
  setActiveProject,
}) => {
  const { t } = useTranslation("project");
  const { renameState, fetchProjects, renameProject } = useProjectService();
  const { activeClient } = useClientService();
  const [projectName, setProjectName] = useState(project ? project.name : "");
  const [projectId, setProjectId] = useState(project ? project.bgpId : 0);
  const [originalName, setOriginalName] = useState(project ? project.name : "");

  const isNameValid = () => {
    const trimmedName = projectName.trim();

    const pattern = /[\\[\]=;`^$_~#%*{}:<>?/+|"!-]/;
    if (pattern.test(trimmedName)) {
      return false;
    }

    if (trimmedName.length <= 0 || trimmedName.length > 100) {
      return false;
    }

    if (trimmedName.startsWith(".") || trimmedName.endsWith(".")) {
      return false;
    }

    return true;
  };

  useEffect(() => {
    if (project) {
      setProjectName(project.name);
      setOriginalName(project.name);
      setProjectId(project.bgpId);
    }
  }, [project]);

  const isNameUnchanged = () => projectName.trim() === originalName.trim();

  const handleRename = async () => {
    if (!activeClient?.bgpId) return;
    const trimmedName = projectName.trim();
    const success = await renameProject(
      projectId,
      trimmedName,
      activeClient?.bgpId
    );
    if (success) {
      //-- TO DO
      //-- project re-fetching should be implemented so that up-to-date data is populating this modal and not relying on us
      //-- manually setting state should be added
      setOriginalName(trimmedName);
      fetchProjects();
    }
    hideModal();
    setActiveProject(null);
  };

  return (
    <Modal
      id="rename-project-modal"
      title={t("rename-project-title")}
      hide={() => {
        setActiveProject(null);
        hideModal();
      }}
      closeButton={false}
      isVisible={showRenameProjectModal}
      size={ModalSize.SMALL}
      allowOverflow={true}
      includeHeaderBorder={false}
      primaryBtnConfig={{
        label: t("rename-project-cta"),
        onClick: () => handleRename(),
        loading: renameState?.isBusy,
        disabled: !isNameValid() || isNameUnchanged(),
        type: ButtonTypeEnum.primary,
        size: ButtonSizeEnum.large,
        withRightIcon: false,
      }}
      secondaryBtnConfig={{
        label: t("rename-project-cancel"),
        onClick: () => {
          hideModal();
          setProjectName(originalName);
        },
        disabled: false,
        type: ButtonTypeEnum.tertiary,
        size: ButtonSizeEnum.large,
        withRightIcon: false,
      }}
    >
      <div className={styles.inputText}>{t("rename-project-field-label")}</div>
      <Input
        inputId="updated-project-name"
        placeholder={t("rename-project-field-label")}
        value={projectName}
        maxLength={250}
        error={!isNameValid()}
        errorMessage={t("error-invalid-project-name-input")}
        replaceLineBreaksWithBulletPoints={true}
        errorMessageWithLineBreaks={true}
        disabled={renameState?.isBusy}
        onValueChange={value => {
          setProjectName(value);
        }}
      />
    </Modal>
  );
};
