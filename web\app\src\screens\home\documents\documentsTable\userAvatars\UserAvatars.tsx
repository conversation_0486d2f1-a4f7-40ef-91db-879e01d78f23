import React from "react";
import { CustomAvatar, getInitials, AssigneeTooltip } from "@bcp/uikit";
import styles from "./userAvatars.module.css";

interface User {
  id: string;
  displayName: string;
  email: string;
}

interface UserAvatarsProps {
  users: User[];
  maxVisible?: number;
}

export const UserAvatars: React.FC<UserAvatarsProps> = ({
  users,
  maxVisible = 5
}) => {
  if (!users || users.length === 0) {
    return null;
  }

  const visibleUsers = users.slice(0, maxVisible);
  const remainingCount = users.length - maxVisible;

  // Always show "+X" format for remaining users
  const displayCount = `+${remainingCount}`;

  return (
    <div className={styles.avatarContainer}>
      {visibleUsers.map((user, index) => (
        <span
          key={user.id}
          className={styles.avatar}
          style={{ zIndex: users.length - index }}
        >
          <AssigneeTooltip
            message={user.displayName}
            inputId={crypto.randomUUID()}
            direction="down"
            smallSize
            withArrow={false}
          >
            <CustomAvatar
              avatarSize="small"
              fontSize="x-small"
              type="monogram"
              initials={getInitials(user.displayName).charAt(0)}
              fontBold
            />
          </AssigneeTooltip>
        </span>
      ))}
      {remainingCount > 0 && (
        <AssigneeTooltip
          message={users
            .slice(maxVisible)
            .map(u => u.displayName)
            .join("\n")}
          inputId={crypto.randomUUID()}
          direction="down"
          smallSize
          withArrow={false}
        >
          <span
            className={`${styles.avatar} ${styles.avatarMore}`}
            style={{ zIndex: 0 }}
          >
            {displayCount}
          </span>
        </AssigneeTooltip>
      )}
    </div>
  );
};
