import React from "react";
import styles from "./attachmentHistory.module.css";
import { Icon } from "~/icon";
import { AttachmentHistoryProps } from "./spec";
import { Button, ButtonTypeEnum } from "~/button";
import classNames from "classnames";
import { downloadFile, getDateTimeLabel } from "~/utils";
import { useTranslation } from "react-i18next";
import { Chip, ChipType } from "~/chip";
import { formatISO } from "date-fns";

export const AttachmentHistory: React.FC<AttachmentHistoryProps> = ({
  attachmentHistories,
  dataTestId = "uikit-attachmentHistory",
  role,
  ariaLabel = "Attachment History",
  ariaDescribedBy,
  ariaLabelledBy,
  tabIndex,
  type,
  isPendingStatus = false,
}) => {
  const { i18n } = useTranslation();
  const { t: tActionItems } = useTranslation("actionItems");

  const hasAttachments = attachmentHistories.length > 0;
  const isSignatureRequest = type === "Signature";

  return (
    <div
      className={styles.attachmentHistoryWrapper}
      data-testid={dataTestId}
      role={role}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-labelledby={ariaLabelledBy}
      tabIndex={tabIndex}
    >
      <div className={styles.title}>
        {isSignatureRequest
          ? tActionItems("attachments")
          : tActionItems("uploaded-documents")}
      </div>
      {!hasAttachments ? (
        <div className={styles.noDocumentsUploaded}>
          {tActionItems("upload-empty")}
        </div>
      ) : (
        <ul className={styles.historyItems}>
          {attachmentHistories.map((historyDocument, i) => (
            <li className={styles.historyItem} key={i}>
              <div className={styles.leftHandInfo}>
                <div className={styles.leftIconWrapper}>
                  <Icon iconName="documents-icon" size={16} />
                </div>
                <div className={styles.documentName}>
                  {historyDocument.name}
                </div>
              </div>

              <div
                className={classNames(
                  styles.rightHandInfo,
                  styles[`rightHandInfo--${historyDocument?.btnConfig?.type}`]
                )}
              >
                {isPendingStatus ? (
                  <Chip
                    text={tActionItems("Pending all signatures")}
                    type={ChipType.STATUS}
                    textOnly={true}
                  />
                ) : historyDocument.btnConfig ? (
                  <Button
                    id={`${i}-${historyDocument.name}`}
                    type={historyDocument.btnConfig.type}
                    label={historyDocument.btnConfig.label}
                    onClick={() => {
                      if (historyDocument.btnConfig?.onClick) {
                        historyDocument.btnConfig.onClick(historyDocument.url);
                      }
                    }}
                    size={historyDocument.btnConfig.size}
                    rightIconName={historyDocument.btnConfig.rightIconName}
                    withBorder={
                      historyDocument.btnConfig.type ===
                      ButtonTypeEnum.secondary
                    }
                    loading={historyDocument.btnConfig.isLoading}
                    disabled={historyDocument.btnConfig.disabled}
                  />
                ) : (
                  <>
                    {historyDocument.author && (
                      <div className={styles.nameContainer}>
                        <span>{historyDocument.author}</span>
                        <span>
                          {historyDocument.timeCreated
                            ? ` • ${getDateTimeLabel(
                                formatISO(historyDocument.timeCreated),
                                i18n.language,
                                false,
                                true
                              )}`
                            : ""}
                        </span>
                      </div>
                    )}
                    <button
                      className={styles.downloadIcon}
                      aria-label={tActionItems("download-document", {
                        name: historyDocument.name,
                      })}
                      onClick={() =>
                        downloadFile(historyDocument.url, historyDocument.name)
                      }
                    >
                      <Icon iconName="arrow-download" size={20} />
                    </button>
                  </>
                )}
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
