import { ReactNode } from "react";
import { defaultProps } from "~/default.spec";
export interface CheckboxProps extends defaultProps {
    id: string;
    value?: boolean;
    className?: string;
    onChange?: (value: boolean) => void;
    disabled?: boolean;
    label?: string;
    size?: CheckboxSizeEnum;
    customLabel?: ReactNode;
}
export declare enum CheckboxSizeEnum {
    large = "large",
    small = "small"
}
