import React from "react";
import { TableProps } from "./spec";
export declare const Table: <T>({ columns, data, placeHolderItems, emptyMessage, emptySubMessage, emptyMessageIcon, dataTestId, role, ariaLabel, ariaDescribedBy, ariaLabelledBy, tabIndex, trailingComponent, className, onClickRow, isLoading, caption, shouldRenderDocumentsSkeleton, dataKeyProperty, highlightImportantRow, }: TableProps<T>) => React.JSX.Element;
