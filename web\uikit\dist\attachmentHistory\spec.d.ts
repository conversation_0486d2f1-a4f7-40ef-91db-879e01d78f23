import { ButtonSizeEnum, ButtonTypeEnum } from "~/button";
import { defaultProps } from "~/default.spec";
import { IconName } from "~/icon";
export interface AttachmentHistoryProps extends defaultProps {
    attachmentHistories: AttachmentHistoryDocument[];
    isPractitioner?: boolean;
    type: string;
    isPendingStatus?: boolean;
}
export interface AttachmentHistoryDocument {
    name: string;
    url: string;
    timeCreated?: Date;
    author?: string;
    btnConfig: ButtonConfig | null;
}
interface ButtonConfig {
    label: string;
    type: ButtonTypeEnum;
    onClick: (documentUrl: string) => void;
    size: ButtonSizeEnum;
    rightIconName: IconName;
    isLoading: boolean;
    disabled?: boolean;
}
export interface ActionItemDocument {
    driveItemId?: string;
    name: string;
    serverRelativeUrl: string;
    timeCreated?: Date;
    timeLastModified?: Date;
    length?: number;
    gpRestricted?: boolean;
    gpReadonly?: boolean;
    gpRequestApproval?: string;
    gpRequestSignature?: string;
    author?: string;
    modifiedBy?: string;
}
export {};
