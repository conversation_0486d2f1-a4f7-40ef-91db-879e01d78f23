/// <reference types="react" />
import { defaultProps } from "~/default.spec";
declare type SelectionMode = "single" | "range";
export interface CustomDatePickerProps extends defaultProps {
    placeholder: string;
    selectionMode?: SelectionMode;
    defaultOpen?: boolean;
    closeOnSelect?: boolean;
    childrenInput?: React.ReactNode;
    childrenTrigger?: React.ReactNode;
    enableRange?: boolean;
    onChange: (day: DatePickerDate) => void;
    onRangeChange?: (dates: Date[]) => void;
    shouldReset?: boolean;
    calendarId?: string;
    value?: string | null;
    startDate?: string | null;
    endDate?: string | null;
    includeReset?: boolean;
    onReset?: () => void;
    placement?: "bottom" | "bottom-start" | "bottom-end";
    disableToday?: boolean;
    triggerClassName?: string;
}
export interface DatePickerDate {
    day: number;
    month: number;
    year: number;
    era: string;
}
export {};
