import React, { useEffect, useState, useMemo } from "react";
import styles from "./InviteFromPastProject.module.css";
import {
  SearchDropdown,
  InlineMessage,
  DropdownInput,
  SelectedUser,
  Spinner,
  SuggestedUser,
  getInvalidEmails,
  SearchDropdownProps,
} from "@bcp/uikit";
import { DropdownInputItem } from "@bcp/uikit";
import {
  IProject,
  IProjectMember,
  useProjectService,
} from "~/services/project";
import { defaultProps } from "~/default.spec";
import { useTranslation } from "react-i18next";
import { isValidEmail } from "@bcp/uikit/src/utils/emailUtil";
import { BcpTeamMemberRole } from "~/screens/practitioner/teamMembers/teamMemberList/spec";
import { matchesUser } from "~/utils/actionItems/actionItemUtils";

interface InviteFromPastProjectEntryProps extends defaultProps {
  suggestedUsers: SuggestedUser[];
  selectedUsers: SelectedUser[];
  onUserListChange: React.Dispatch<React.SetStateAction<SelectedUser[]>>;
  invalidInviteEmails: string[];
  setInvalidInviteEmails: React.Dispatch<React.SetStateAction<string[]>>;
  clientProjects?: IProject[] | null;
  isLoading: boolean;
  excludeUsersFromCurrentProject: <T extends { email: string }>(
    userList: T[]
  ) => T[];
  project: string;
  setProject: (value: string) => void;
  disabled: boolean;
}

export const InviteFromPastProject: React.FC<InviteFromPastProjectEntryProps> = ({
  dataTestId = "app-invite-team-members-past",
  ariaDescribedBy,
  invalidInviteEmails,
  setInvalidInviteEmails,
  clientProjects,
  onUserListChange,
  selectedUsers,
  suggestedUsers,
  isLoading,
  excludeUsersFromCurrentProject,
  project,
  setProject,
  disabled,
}) => {
  const { t } = useTranslation("teamMembers");
  const [showAllAlreadyInvited, setShowAllAlreadyInvited] = useState(false);
  const [showNonSuggestedWarning, setShowNonSuggestedWarning] = useState(false);

  const {
    activeProject,
    projectMembers,
    fetchProjectMembers,
  } = useProjectService();

  const handleUserAdd = (user: SelectedUser) => {
    const email = user.email || user.displayText;
    const newUser = {
      displayText: user.displayText,
      email,
      userGroupName: user.userGroupName ?? BcpTeamMemberRole.CLIENT_USER,
      chipType: user.chipType,
    };

    const updatedUsers = [...selectedUsers, newUser].filter(
      (user, index, self) =>
        index === self.findIndex(u => u.email === user.email)
    );
    onUserListChange(updatedUsers);
    updateValidationStates(updatedUsers);
  };

  const handleUserRemove: SearchDropdownProps["onUserRemove"] = removedUser => {
    const updatedUsers = selectedUsers.filter(
      user => !matchesUser(user, removedUser)
    );
    onUserListChange(updatedUsers);
    updateValidationStates(updatedUsers);
  };

  const updateValidationStates = (users: SelectedUser[]) => {
    setShowNonSuggestedWarning(
      users.some(
        user =>
          isValidEmail(user.email) &&
          !suggestedUsers.some(
            suggestedUser => suggestedUser.email === user.email
          )
      )
    );
    const newInvalidEmailsList = getInvalidEmails(users);
    const emailDifferenceResults: string[] = newInvalidEmailsList
      .filter(newEmail => !invalidInviteEmails.includes(newEmail))
      .concat(
        invalidInviteEmails.filter(
          oldEmail => !newInvalidEmailsList.includes(oldEmail)
        )
      );

    if (emailDifferenceResults.length > 0) {
      setInvalidInviteEmails(newInvalidEmailsList);
    }
  };

  const filteredSuggestedUsers: SuggestedUser[] = useMemo(
    () =>
      suggestedUsers.filter(
        suggestedUser =>
          !selectedUsers.some(
            selectedUser => selectedUser.email === suggestedUser.email
          )
      ),
    [suggestedUsers, selectedUsers]
  );

  const dropdownItems =
    clientProjects
      ?.map(({ bgpId, name }) => ({
        label: name,
        value: `${bgpId}`,
      }))
      .filter(project => {
        return project.value != `${activeProject?.bgpId}`;
      }) ?? [];

  useEffect(() => {
    updateValidationStates(selectedUsers);
  }, [selectedUsers]);

  useEffect(() => {
    setShowAllAlreadyInvited(false);
  }, []);

  useEffect(() => {
    if (project && projectMembers?.data) {
      const projectMemberUsers = projectMembers.data.map(
        (projectMember: IProjectMember) => {
          return {
            email: projectMember.email,
            displayText: projectMember.displayName,
            isSuggested: true,
            userGroupName:
              projectMember.userRoles[0] == "ClientUser"
                ? BcpTeamMemberRole.CLIENT_USER
                : BcpTeamMemberRole.PRACTITIONER,
          };
        }
      );

      const updatedUsers = [...selectedUsers, ...projectMemberUsers].filter(
        (user, index, self) =>
          index === self.findIndex(u => u.email === user.email)
      );
      onUserListChange(updatedUsers);
      updateValidationStates(updatedUsers);
      setShowAllAlreadyInvited(
        excludeUsersFromCurrentProject(projectMemberUsers).length == 0
      );
    }
  }, [projectMembers?.data]);

  return (
    <div
      data-testid={dataTestId}
      aria-describedby={ariaDescribedBy}
      className={styles.inviteFromPastProjectWrapper}
    >
      {showAllAlreadyInvited && project && (
        <InlineMessage
          data-testid="app-all-users-added-info-message"
          type="info"
          title={t("all-users-added")}
          message=""
        />
      )}
      {isLoading ? (
        <div className={styles.spinner}>
          <Spinner background="#f0f0f0" isLoading message={t("loading")} />
        </div>
      ) : (
        dropdownItems.length > 0 && (
          <DropdownInput
            id="previous-project"
            dataTestId="uikit-user-projects"
            items={dropdownItems}
            placeholder={t("invite-from-project-placeholder")}
            onSelectionChange={(item: DropdownInputItem) => {
              setShowAllAlreadyInvited(false);
              setProject(item.label ?? "");
              onUserListChange([]);
              fetchProjectMembers(`${item.value}`);
            }}
            error={false}
            disabled={disabled}
          />
        )
      )}
      {project && projectMembers?.data && (
        <SearchDropdown
          id="team-members-project-selected-dropdown"
          dataTestId="uikit-search-team-members"
          ariaLabel={t("selected-team-members")}
          suggestedUsers={filteredSuggestedUsers as SuggestedUser[]}
          onUserAdd={handleUserAdd}
          onUserRemove={handleUserRemove}
          selectedUsers={selectedUsers}
          disabled={disabled}
        />
      )}
      {showNonSuggestedWarning && (
        <InlineMessage
          data-testid="app-user-warning-message"
          type="warning"
          title={t("invite-warning-title")}
          message={t("invite-warning-sub")}
        />
      )}
      {invalidInviteEmails.length > 0 && (
        <InlineMessage
          data-testid="app-user-error-message"
          type="error"
          title={t("invalid-email")}
          message={t("invalid-email-description")}
        />
      )}
    </div>
  );
};
