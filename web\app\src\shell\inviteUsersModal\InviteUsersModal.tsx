import { useEffect, useState, useMemo } from "react";
import styles from "./inviteUsersModal.module.css";
import {
  ButtonSizeEnum,
  ButtonTypeEnum,
  FooterButtonAlignment,
  Modal,
  ModalSize,
  SelectionCard,
  SelectedUser,
  SuggestedUser,
} from "@bcp/uikit";
import { InviteUsersModalProps } from "./spec";
import { InviteManually } from "./inviteManually/InviteManually";
import { InviteFromPastProject } from "./inviteFromPastProject/InviteFromPastProject";
import useSendInvite from "./useSendInvite";
import { useProjectService } from "~/services/project";
import { useClientService } from "~/services/client";
import { useTranslation } from "react-i18next";
import { useRoleService } from "~/services/role";
import { useToastService } from "~/services/toast";

import { BcpTeamMemberRole } from "~/screens/practitioner/teamMembers/teamMemberList/spec";
import { UpdateOrCreateOptimisticUsers } from "~/screens/practitioner/optimisticInviteUtils";

const bdoDomainsRegex = /@bdo\.[a-z]*$/i;

export const InviteUsersModal: React.FC<InviteUsersModalProps & {
  onSendInvite: () => void;
}> = ({
  dataTestId = "app-invite-users-modal",
  ariaDescribedBy,
  ariaLabel = "Invite users to current project",
  ariaLabelledBy,
  isOpen,
  onModalClose,
  suggestedUsersList,
  onSendInvite,
  existingUsers,
}) => {
  const { t } = useTranslation("teamMembers");
  const { t: tGlobal } = useTranslation("global");
  const [visualState, setVisualState] = useState<
    "start" | "inviteManually" | "inviteFromPrevious"
  >("start");
  const [selectedUsers, setSelectedUsers] = useState<SelectedUser[]>([]);
  const [invalidInviteEmails, setInvalidInviteEmails] = useState<string[]>([]);
  const [project, setProject] = useState<string>("");
  const [isInviting, setIsInviting] = useState(false);
  const excludedEmails = existingUsers
    ? existingUsers.map(user => user.email.toLowerCase())
    : [];
  const excludeUsersFromCurrentProject = <T extends { email: string }>(
    userList: T[]
  ) => userList.filter(u => !excludedEmails.includes(u.email.toLowerCase()));
  const excludeBdoUsers = (userList: SuggestedUser[]) =>
    userList.filter(u => !bdoDomainsRegex.test(u.email.toLowerCase()));

  const suggestedUsers: SuggestedUser[] = useMemo(() => suggestedUsersList, [
    suggestedUsersList,
  ]);

  const { sendInvite } = useSendInvite();
  const { projects, activeProject } = useProjectService();
  const { activeClient } = useClientService();
  const { isClient } = useRoleService();
  const userEmails = existingUsers
    ? new Set(existingUsers.map(user => user.email.toLowerCase()))
    : new Set();
  const { showToast } = useToastService();

  const closeModal = () => {
    onModalClose?.();
    setProject("");
    setSelectedUsers([]);
    setVisualState("start");
  };

  const getFirstLastNames = (
    displayName: string | null | undefined
  ): { firstName: string; lastName: string } => {
    if (!displayName || displayName.trim() === "") {
      return { firstName: "", lastName: "" };
    }

    displayName = displayName.trim();
    const commaCount = (displayName.match(/,/g) || []).length;

    if (commaCount > 1) {
      return { firstName: "", lastName: "" };
    }

    // "lastname, firstname" format
    if (commaCount === 1) {
      const parts = displayName.split(",").map(p => p.trim()).filter(p => p.length > 0);
      if (parts.length > 1) {
        return { firstName: parts[1], lastName: parts[0] };
      } else {
        return { firstName: "", lastName: "" };
      }
    }

    // "firstname lastname" format
    const nameParts = displayName.split(" ").filter(p => p.length > 0);
    if (nameParts.length > 1) {
      return {
        firstName: nameParts[0],
        lastName: nameParts.slice(1).join(" ")
      };
    } else {
      return { firstName: "", lastName: "" };
    }
  };

  const getFirstName = (displayName: string | null | undefined): string => {
    const result = getFirstLastNames(displayName);
    return result.firstName;
  }

  const getLastName = (displayName: string | null | undefined): string => {
    const result = getFirstLastNames(displayName);
    return result.lastName;
  }

  const inviteUserList = selectedUsers.map(user => {
    let groupName = user.userGroupName;
    if (!groupName) {
      groupName = bdoDomainsRegex.test(user.email.toLowerCase())
        ? BcpTeamMemberRole.PRACTITIONER
        : BcpTeamMemberRole.CLIENT_USER;
    }
    const userDetails = {
      email: user.email.toLowerCase(),
      firstName: getFirstName(user.displayText),
      lastName: getLastName(user.displayText),
      groupName: groupName,
    };

    return userDetails;
  });

  const handleInvite = async () => {
    if (!activeClient || !activeProject) return;
    const clientId = activeClient.bgpId;
    const projectId = activeProject.bgpId;

    if (clientId && projectId) {
      const nonDuplicateUsers = inviteUserList.filter(
        user => !userEmails.has(user.email.toLowerCase())
      );

      if (
        isClient &&
        nonDuplicateUsers.some(user =>
          bdoDomainsRegex.test(user.email.toLowerCase())
        )
      ) {
        showToast({
          title: tGlobal("access-level"),
          type: "error",
          persist: false,
          message: t("unauthorized-invite-bdo-users"),
        });
        return;
      }

      if (nonDuplicateUsers.length == 0) {
        showToast({
          title: t("duplicate-user-invite-title"),
          type: "error",
          persist: false,
          message:t("duplicate-user-invite-description"),
        });
        return;
      }

      const result = await sendInvite(nonDuplicateUsers, clientId, projectId);
      // Code to optimistically store users who are invited but not immediately returned when fetching project members
      UpdateOrCreateOptimisticUsers(projectId, clientId, result.response);

      if (result.success) {
        showToast({
          message: t("invite-sent"),
          type: "success",
          persist: false,
        });
      } else {
        showToast({
          message: t("failed-send-invites"),
          type: "error",
          persist: false,
        });
      }
    } else {
      console.error("clientId is not set");
    }
  };

  const validateAndClose = async () => {
    if (!invalidInviteEmails.length) {
      setIsInviting(true);
      await handleInvite();
      setIsInviting(false);
      onSendInvite();
      closeModal();
      return;
    }
    closeModal();
  };

  const getSubtitle = (): string => {
    if (visualState === "start") {
      return "";
    }

    if (isClient) {
      return t("invite-client-subtitle");
    }

    if (visualState === "inviteManually") {
      return t("invite-manually-subtitle");
    }

    return t("invite-from-project-subtitle");
  };

  const buttonConfigs =
    visualState === "inviteManually" || visualState === "inviteFromPrevious"
      ? {
          primaryBtnConfig: {
            label: t("send-invites"),
            type: ButtonTypeEnum.primary,
            size: ButtonSizeEnum.large,
            withIcon: false,
            withRightIcon: false,
            onClick: validateAndClose,
            disabled:
              selectedUsers.length === 0 ||
              (visualState === "inviteFromPrevious" &&
                excludeUsersFromCurrentProject(selectedUsers)!.length === 0) ||
              invalidInviteEmails.length !== 0 ||
              isInviting,
            loading: isInviting,
            labelOnLoading: true,
          },
          secondaryBtnConfig: {
            label: tGlobal("cancel"),
            type: ButtonTypeEnum.tertiary,
            size: ButtonSizeEnum.large,
            withIcon: false,
            withRightIcon: false,
            onClick: closeModal,
            disabled: isInviting,
          },
        }
      : null;

  useEffect(() => {
    if (isOpen) {
      setVisualState(isClient ? "inviteManually" : "start");
    }
  }, [isOpen]);

  const handleUserListChange = (newSelectedUsers: SelectedUser[]) => {
    if (newSelectedUsers.length > 15) {
      showToast({
        type: "error",
        persist: false,
        message: t("invite-limit-error"),
      });
      return;
    }
    setSelectedUsers(newSelectedUsers);
  };

  return (
    <Modal
      id="invite-users-modal"
      data-testid={dataTestId}
      aria-describedby={ariaDescribedBy}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
      title={
        visualState === "inviteFromPrevious"
          ? t("invite-client")
          : t("invite-team-members")
      }
      subtitle={getSubtitle()}
      isVisible={isOpen}
      hide={closeModal}
      footerBtnAlignment={FooterButtonAlignment.END}
      size={ModalSize.LARGE}
      includeHeaderBorder={false}
      {...buttonConfigs}
      allowOverflow={false}
    >
      {visualState === "start" ? (
        <div className={styles.selectionCardContainer}>
          <SelectionCard
            data-testid="uikit-selection-invite-manually"
            aria-label="select-manually"
            title={t("invite-manually")}
            subtitle={t("invite-manually-description")}
            onCardClick={() => setVisualState("inviteManually")}
            leftIconConfig={{ name: "people-search", altText: "Group icon" }}
            rightIconConfig={{ name: "arrow-right", altText: "arrow right" }}
          />
          <SelectionCard
            data-testid="uikit-selection-invite-previous-project"
            aria-label="select-previous"
            title={t("invite-from-project")}
            subtitle={t("invite-from-project-description")}
            onCardClick={() => setVisualState("inviteFromPrevious")}
            leftIconConfig={{ name: "copy-select", altText: "Group icon" }}
            rightIconConfig={{ name: "arrow-right", altText: "arrow right" }}
            disabled={projects?.data && projects.data.length > 1 ? false : true}
          />
        </div>
      ) : (
        visualState === "inviteManually" && (
          <InviteManually
            suggestedUsers={
              isClient
                ? excludeUsersFromCurrentProject(
                    excludeBdoUsers(suggestedUsers)
                  )!
                : excludeUsersFromCurrentProject(suggestedUsers)!
            }
            selectedUsers={excludeUsersFromCurrentProject(selectedUsers)!}
            onUserListChange={handleUserListChange}
            invalidInviteEmails={invalidInviteEmails}
            setInvalidInviteEmails={setInvalidInviteEmails}
            disabled={isInviting}
          />
        )
      )}
      {visualState === "inviteFromPrevious" && (
        <div className={styles.inviteFromPreviousContainer}>
          <InviteFromPastProject
            suggestedUsers={
              isClient
                ? excludeUsersFromCurrentProject(
                    excludeBdoUsers(suggestedUsers)
                  )!
                : excludeUsersFromCurrentProject(suggestedUsers)!
            }
            selectedUsers={excludeUsersFromCurrentProject(selectedUsers)!}
            onUserListChange={setSelectedUsers}
            invalidInviteEmails={invalidInviteEmails}
            setInvalidInviteEmails={setInvalidInviteEmails}
            clientProjects={projects?.data}
            isLoading={!!projects?.isBusy}
            excludeUsersFromCurrentProject={excludeUsersFromCurrentProject}
            project={project}
            setProject={setProject}
            disabled={isInviting}
          />
        </div>
      )}
    </Modal>
  );
};
