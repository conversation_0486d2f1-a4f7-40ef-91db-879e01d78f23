export declare const MILLISECONDS_IN_MINUTE = 60000;
export declare const MILLISECONDS_IN_HOUR = 3600000;
export declare const MILLISECONDS_IN_DAY = 86400000;
export declare const getTimezoneConvertedDate: (date: Date | string) => Date;
export declare const isPastDue: (dateString: string) => boolean;
export declare const getDateLabel: (dateString: string, localeString?: string) => string;
export declare const getDateTimeLabel: (dateString: string, localeString?: string, alwaysShowTime?: boolean, capitalize?: boolean) => string;
export declare const getDueDateLabel: (dateString: string, localeString?: string) => string;
export declare const getDateRangeLabel: (dates: Set<Date>, locale?: string) => string;
export declare const getDocumentsDateLabel: (dateString: string, locale?: string) => string;
export declare const getStartEndDateRange: (range: "today" | "last7Days" | "last30Days") => {
    startOfRange: Date;
    endOfRange: Date;
};
export declare const isDueDateInDateRange: (startDate: Date, endDate: Date, dueDate: Date) => boolean;
