import { ChipType } from "~/chip/spec";
import { defaultProps } from "~/default.spec";
export declare enum UserStatus {
    Active = "active",
    Inactive = "inactive"
}
export declare enum Language {
    English = "english",
    French = "french"
}
export declare enum Department {
    Other = "other",
    Administration = "administration"
}
export declare enum EmailFrequency {
    None = "none",
    Weekly = "weekly"
}
export declare enum NotificationType {
    ActionItemAssigned = "action_item_assigned",
    ActionItemStatusUpdated = "action_item_status_updated",
    ActionItemCommentAdded = "action_item_comment_added",
    ProjectInvite = "project_invite",
    HealthStatusUpdate = "health_status_update",
    FinalDeliveryUploaded = "final_delivery_uploaded"
}
export declare enum BusinessDomain {
    EconomicResiliency = "economic_resiliency",
    AritificialIntelligence = "aritificial_intelligence",
    InvestmentStrategyAndPlanning = "investment_strategy_and_planning",
    SecurityVulnerabilitiesAndBreaches = "security_vulnerabilities_and_breaches",
    DigitalOrItTransformation = "digital_or_it_transformation",
    FraudProtection = "fraud_protection",
    MergersAndAcquisitions = "mergers_and_acquisitions",
    SupplyChains = "supply_chains",
    OfficeThreeSixFive = "office_three_six_five",
    Other = "other"
}
export interface ExistingUser {
    uniqueUserId: string;
    name: string;
    email: string;
    displayText?: string;
    avatarUrl?: string;
    avatarColor?: string;
    userGroupName?: string;
}
export interface PotentialNewUser {
    email: string;
    name?: string;
    displayText?: string;
}
export declare type AvailableUser = ExistingUser | PotentialNewUser;
export interface SelectedUser {
    email: string;
    name: string;
    displayText: string;
    uniqueUserId?: string;
    chipType?: ChipType;
    avatarColor?: string;
    userGroupName?: string;
    id?: string;
    value?: string;
    label?: string;
    isSuggested?: boolean;
}
export declare const isExistingUser: (user: AvailableUser) => user is ExistingUser;
export declare const isPotentialNewUser: (user: AvailableUser) => user is PotentialNewUser;
export interface SearchDropdownProps extends defaultProps {
    availableUsers: AvailableUser[];
    onUserAdd: (user: SelectedUser) => void;
    onUserRemove: (user: SelectedUser) => void;
    selectedUsers: SelectedUser[];
    readOnlyUserIds?: string[];
    disabled?: boolean;
    defaultLabel?: string;
    useMaxWidth?: boolean;
    isRequired?: boolean;
    showFormRequiredFields?: boolean;
    showName?: boolean;
    showEmail?: boolean;
    allowPotentialNewUsers?: boolean;
    maxAssignees?: number;
    maxResults?: number;
    id?: string;
    className?: string;
    errorOnBlur?: boolean;
}
