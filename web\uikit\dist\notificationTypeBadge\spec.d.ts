import { defaultProps } from "~/default.spec";
export interface NotificationTypeBadgeProps extends defaultProps {
    type: NotificationBadgeType;
}
export declare enum NotificationBadgeType {
    ACTION_ITEM = "ActionItem",
    DELIVERABLE = "Deliverable",
    PROJECT_HEALTH_STATUS = "ProjectHealthStatus",
    GENERAL = "General"
}
export declare const NotificationTypeToBadgeColorMap: {
    ActionItem: string;
    Deliverable: string;
    ProjectHealthStatus: string;
    General: string;
};
