import { defaultProps } from "~/default.spec";
export interface TreeRootProps extends defaultProps {
    baseUrl: string;
    isLoading: boolean;
    updateIsRestricted: (restricted: boolean) => void;
    updateFolderItemId: (listItemId: number) => void;
    id: string;
    items: TreeBranchProps[];
}
export interface TreeBranchProps extends defaultProps {
    id: string;
    name: string;
    displayName: string;
    baseUrl: string;
    descendants?: TreeBranchProps[];
    onExpanded?: (id: string) => void;
    type?: ChildType;
    path?: string;
    readonly: boolean;
    restricted: boolean;
    listItemId?: number;
    isLoading: boolean;
    deletionInProgress: boolean;
    updateIsRestricted: (restricted: boolean) => void;
    updateFolderItemId: (listItemId: number) => void;
    changeExpandedValues: (id: string) => void;
    openTab: boolean;
}
export interface TreeProps extends defaultProps {
    id: string;
    name: string;
    descendants?: TreeProps[];
}
export declare enum ChildType {
    FOLDER = "folder",
    FILE = "file"
}
