import React, { KeyboardEvent } from "react";
import { IconName } from "~/icon";
import { defaultProps } from "~/default.spec";
export interface InputProps extends defaultProps, Omit<React.InputHTMLAttributes<HTMLInputElement>, "role"> {
    placeholder?: string;
    value?: string;
    onValueChange?: (value: string) => void;
    onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    inputId: string;
    className?: string;
    optionalLabel?: string;
    disabled?: boolean;
    maxLength?: number;
    onClick?: () => void;
    onBlur?: () => void;
    onKeyDown?: (e: KeyboardEvent<HTMLInputElement>) => void;
    error?: boolean;
    errorMessage?: string;
    floatingLabelEnabled?: boolean;
    withLeftIcon?: boolean;
    leftIconName?: IconName;
    additionalErrorMessages?: string[];
    enableSearch?: boolean;
    errorMessageWithLineBreaks?: boolean;
    replaceLineBreaksWithBulletPoints?: boolean;
    showClearSearchTerm?: boolean;
    thin?: boolean;
    required?: boolean;
    leftIconPaddingLeft?: string;
    showRequiredIndicator?: boolean;
    autoComplete?: string;
}
export declare const Input: React.FC<InputProps>;
