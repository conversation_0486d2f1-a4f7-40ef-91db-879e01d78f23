import { ReactNode } from "react";
import { defaultProps } from "~/default.spec";
import { DropdownInputItem } from "~/dropdownInput/spec";
export declare enum DrawerSizeEnum {
    Default = 0,
    Large = 1
}
export interface DrawerProps extends defaultProps {
    isVisible: boolean;
    hasOverlay?: boolean;
    closeDrawer: () => void;
    headerConfig?: DrawerHeaderConfig;
    primaryBtnConfig?: FooterButtonConfig;
    secondaryBtnConfig?: FooterButtonConfig;
    children: ReactNode;
    withAvatar?: boolean;
    userInitial?: string;
    withBackButton?: boolean;
    onBack?: () => void;
    closeOnClickOutside?: boolean;
    size?: DrawerSizeEnum;
    isCopyable?: boolean;
    onCopyLink?: () => void;
    dropdownMenuItems?: DropdownInputItem[];
    avatarColor?: string;
    id?: string;
}
interface DrawerHeaderConfig {
    pretitle?: string;
    title: string;
    subtitle?: string;
    component?: ReactNode;
}
interface FooterButtonConfig {
    type: string;
    text: string;
    disabled: boolean;
    onClick: () => void;
    isLoading?: boolean;
}
export {};
