export declare enum ThemeColor {
    PrimaryRed = "primaryRed",
    PrimaryCharcoal = "primaryCharcoal",
    PrimaryPaleCharcoal = "primaryPaleCharcoal",
    PrimaryLightCharcoal = "primaryLightCharcoal",
    PrimarySlate = "primarySlate",
    SecondaryBurgundy = "secondaryBurgundy",
    SecondaryCobalt = "secondaryCobalt",
    SecondaryEmerald = "secondaryEmerald",
    SecondaryOcean = "secondaryOcean",
    SecondaryGold = "secondaryGold",
    SecondaryJade = "secondaryJade",
    BgWhite = "bgWhite",
    BgPrimaryCharcoal = "bgPrimaryCharcoal",
    BgPrimaryPaleCharcoal = "bgPrimaryPaleCharcoal",
    BgGrey = "bgGrey",
    BgLightGrey = "bgLightGrey",
    BgLightCharcoal = "bgLightCharcoal",
    BgInactiveCharcoal = "bgInactiveCharcoal"
}
export declare const colorVarMap: Record<string, string>;
