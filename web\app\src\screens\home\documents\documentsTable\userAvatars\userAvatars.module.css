.avatarContainer {
  display: flex;
  position: relative;
  align-items: center;
  z-index: 1;
}

.avatar {
  border-radius: 50%;
  position: relative;
  border: 1px solid white;
}

.avatar:first-child {
  margin-left: -1px; /* Compensate for overlap border */
}

.avatar:not(:last-child) {
  margin-right: -0.25rem; /* Create overlap effect */
}

.avatarMore {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  color: var(--color-primary-inactive-charcoal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--fontsize-body-xsmall);
  border: 1px solid var(--color-bg-grey);
  letter-spacing: 0.1px;
  font-weight: bold;
  line-height: var(--lineheight-body-xsmall);
  background-color: var(--color-bg-white);
}
