import React, { useEffect, useState, useMemo } from "react";
import styles from "./InviteManually.module.css";
import {
  SearchDropdown,
  SelectedUser,
  InlineMessage,
  SuggestedUser,
  getInvalidEmails,
  SearchDropdownProps,
} from "@bcp/uikit";
import { defaultProps } from "~/default.spec";
import { useTranslation } from "react-i18next";
import { isValidEmail } from "@bcp/uikit/src/utils/emailUtil";
import { matchesUser } from "~/utils/actionItems/actionItemUtils";

interface InviteManualEntryProps extends defaultProps {
  suggestedUsers: SuggestedUser[];
  selectedUsers: SelectedUser[];
  onUserListChange: (users: SelectedUser[]) => void;
  invalidInviteEmails: string[];
  setInvalidInviteEmails: (emails: string[]) => void;
  disabled: boolean;
}

export const InviteManually: React.FC<InviteManualEntryProps> = ({
  dataTestId = "app-invite-team-member-manual",
  ariaDescribedBy,
  ariaLabel = "",
  ariaLabelledBy,
  role,
  suggestedUsers,
  selectedUsers,
  onUserListChange,
  invalidInviteEmails,
  setInvalidInviteEmails,
  disabled,
}) => {
  const [showNonSuggestedWarning, setShowNonSuggestedWarning] = useState(false);
  const { t } = useTranslation("teamMembers");

  const handleUserAdd = (user: SelectedUser) => {
    const email = user.email || user.displayText;
    const newUser = {
      displayText: user.displayText?.trim() || email,
      email,
      userGroupName: user.userGroupName,
      chipType: user.chipType,
    };

    const updatedUsers = [...selectedUsers, newUser].filter(
      (user, index, self) =>
        index === self.findIndex(u => u.email === user.email)
    );
    onUserListChange(updatedUsers);
    updateValidationStates(updatedUsers);
  };

  const handleUserRemove: SearchDropdownProps["onUserRemove"] = removedUser => {
    const updatedUsers = selectedUsers.filter(
      user => !matchesUser(user, removedUser)
    );
    onUserListChange(updatedUsers);
    updateValidationStates(updatedUsers);
  };

  const updateValidationStates = (users: SelectedUser[]) => {
    setShowNonSuggestedWarning(
      users.some(
        user =>
          isValidEmail(user.email) &&
          !suggestedUsers.some(
            suggestedUser => suggestedUser.email === user.email
          )
      )
    );

    const newInvalidEmailsList = getInvalidEmails(users);
    const emailDifferenceResults: string[] = newInvalidEmailsList
      .filter(newEmail => !invalidInviteEmails.includes(newEmail))
      .concat(
        invalidInviteEmails.filter(
          oldEmail => !newInvalidEmailsList.includes(oldEmail)
        )
      );

    if (emailDifferenceResults.length > 0) {
      setInvalidInviteEmails(newInvalidEmailsList);
    }
  };

  const filteredSuggestedUsers: SuggestedUser[] = useMemo(
    () =>
      suggestedUsers.filter(
        suggestedUser =>
          !selectedUsers.some(
            selectedUser => selectedUser.email === suggestedUser.email
          )
      ),
    [suggestedUsers, selectedUsers]
  );

  useEffect(() => {
    updateValidationStates(selectedUsers);
  }, [selectedUsers]);

  return (
    <div
      data-testid={dataTestId}
      aria-describedby={ariaDescribedBy}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
      role={role}
      className={styles.inviteManuallyWrapper}
    >
      <SearchDropdown
        id="team-members-invite-dropdown"
        className={styles.searchDropdown}
        data-testid="uikit-invite-team-members-search"
        aria-label={t("invite-manually-placeholder")}
        suggestedUsers={filteredSuggestedUsers}
        onUserAdd={handleUserAdd}
        onUserRemove={handleUserRemove}
        selectedUsers={selectedUsers}
        disabled={selectedUsers.length >= 15 || disabled}
        defaultLabel={t("invite-manually-placeholder")}
      />
      {showNonSuggestedWarning && (
        <InlineMessage
          data-testid="app-user-warning-message"
          type="warning"
          title={t("invite-warning-title")}
          message={t("invite-warning-sub")}
        />
      )}
      {invalidInviteEmails.length > 0 && (
        <InlineMessage
          data-testid="app-user-error-message"
          type="error"
          title={t("invite-warning-invalid-format-title")}
          message={t("invite-warning-invalid-format-body")}
        />
      )}
    </div>
  );
};
