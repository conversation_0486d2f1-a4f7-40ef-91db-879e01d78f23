import React from "react";
import { Stage } from "~/timeline";
import { defaultProps } from "~/default.spec";
interface SubStage {
    id: string;
    title: string;
    translationKey: string;
    startDate?: Date;
    endDate?: Date;
    status: string;
    sortOrder?: number;
    _isNewTemplate?: boolean;
}
interface EditProjectStagesProps extends defaultProps {
    id: string;
    stageName: string;
    subStages: SubStage[];
    substageOptions: {
        label: string;
        value: string;
    }[];
    startDate?: Date;
    endDate?: Date;
    status: string;
    isActive: boolean;
    setStages: React.Dispatch<React.SetStateAction<Stage[]>>;
    onDeleteSubStage: (stageId: string, subStageId: string) => void;
    onAddSubstage: (stageId: string) => void;
    onAddCustomSubstage: (stageId: string) => void;
    showValidationErrors?: boolean;
}
export declare const validateSubStages: (subStages: SubStage[]) => boolean;
export declare const EditProjectStages: React.FC<EditProjectStagesProps>;
export {};
