import { defaultProps } from "~/default.spec";
export declare const MAX_COMMENT_CHARACTER_COUNT = 1000;
interface CreatedAndModifiedBy {
    id: string;
    name: string;
}
export interface Comment extends defaultProps {
    id: string;
    memberFirmId?: number;
    clientId?: number;
    projectId?: number;
    taskId?: string;
    text: string;
    createdOn: string;
    createdBy?: CreatedAndModifiedBy;
    modifiedOn?: string;
    modifiedBy?: CreatedAndModifiedBy;
    canEdit: boolean;
    onCommentDelete: (commentId: string, taskId: string) => void;
    onCommentUpdate: (commentId: string, taskId: string, text: string) => void;
}
export interface CommentsProps extends defaultProps {
    comments: Comment[];
    onCommentDelete: (commentId: string, taskId: string) => void;
    onCommentUpdate: (commentId: string, taskId: string, text: string) => void;
    onCommentCreate: (text: string) => void;
    userDisplayName?: string;
    userAvatarColor?: string;
    isCommentingDisabled?: boolean;
    isLoading?: boolean;
    isReady?: boolean;
    onCommentTextChange?: (text: string) => void;
}
export {};
