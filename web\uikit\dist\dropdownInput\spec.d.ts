import { defaultProps } from "~/default.spec";
export interface DropdownInputProps extends defaultProps {
    id?: string;
    items: DropdownInputItem[];
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    defaultOpen?: boolean;
    itemGroupLabel?: string;
    onSelectionChange: (item: DropdownInputItem) => void;
    onInputChange?: (value: string) => void;
    error: boolean;
    formValidator?: (value: DropdownInputItem | null) => boolean;
    errorMessage?: string;
    defaultSelection?: DropdownInputItem;
    showNoResultsMessage?: boolean;
    noResultsMessage?: string;
    showCTA?: boolean;
    ctaText?: string;
    onCTAClicked?: () => void;
    withIcon?: boolean;
    floatingLabelEnabled?: boolean;
    displayOffset?: boolean;
    showAsChip?: boolean;
    renderTop?: boolean;
    enableSearch?: boolean;
    limitDropdownWidth?: boolean;
    updatingSpinner?: boolean;
    thin?: boolean;
    required?: boolean;
    leftIconPaddingLeft?: string;
    dropdownPortalClass?: string;
    showRequiredIndicator?: boolean;
    shouldSortTranslatedItems?: boolean;
    truncateSelectedText?: number;
}
export interface DropdownInputItem {
    label?: string;
    value: string | number | boolean;
    description?: string;
    icon?: string;
    labelSelection?: string;
    translationKey?: string;
    useCustomStyle?: boolean;
}
